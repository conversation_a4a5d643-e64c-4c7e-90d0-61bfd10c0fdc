import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import { viteStaticCopy } from 'vite-plugin-static-copy';
import autoprefixer from 'autoprefixer';

// Entry points configuration (migrated from webpack entries.json)
const entries = {
    theme: path.resolve(__dirname, './js/theme.js'),
    product: path.resolve(__dirname, './js/product.js'),
    checkout: path.resolve(__dirname, './js/checkout.js'),
    listing: path.resolve(__dirname, './js/listing.js'),
};

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
    // Load env file based on `mode` in the current directory.
    // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
    const env = loadEnv(mode, process.cwd(), '');

    // Environment variables with defaults (migrated from webpack.vars.js)
    const {
        VITE_PORT = '3200',
        VITE_SERVER_ADDRESS = 'localhost',
        VITE_SITE_URL = 'http://localhost',
        VITE_PUBLIC_PATH = '/themes/falcon-vite/assets/',
    } = env;

    const isDev = command === 'serve';
    const port = parseInt(VITE_PORT);

    return {
        // Define global constants (similar to webpack DefinePlugin)
        define: {
            // Add any global constants here if needed
            __DEV__: JSON.stringify(mode === 'development'),
        },

        // Multiple entry points (migrated from webpack entries)
        build: {
            rollupOptions: {
                input: entries,
                output: {
                    /**
                     * Entry file naming pattern (migrated from webpack output config)
                     */
                    entryFileNames: 'assets/js/[name].js',

                    /**
                     * Chunk file naming pattern (migrated from webpack output config)
                     */
                    chunkFileNames:
                        mode === 'production'
                            ? 'assets/js/[name]-[hash].js'
                            : 'js/[name].js',

                    /**
                     * Asset file naming pattern (enhanced from webpack parts)
                     */
                    assetFileNames: (assetInfo) => {
                        const name = assetInfo.names?.[0] || assetInfo.name;
                        // Images (migrated from webpack extractImages)
                        if (/\.(gif|jpe?g|png|svg)$/.test(name ?? '')) {
                            return 'assets/img/[name]-[hash][extname]';
                        }

                        // CSS files - use entry point names instead of _index
                        if (/\.css$/.test(name ?? '')) {
                            // Check if this is from a CSS entry point
                            const facadeId = assetInfo.facadeModuleId;
                            if (facadeId) {
                                // Check if it's one of our CSS entries
                                if (facadeId.includes('/css/theme.scss')) return 'css/theme.css';
                                if (facadeId.includes('/css/product.scss')) return 'css/product.css';
                                if (facadeId.includes('/css/checkout.scss')) return 'css/checkout.css';
                                if (facadeId.includes('/css/listing.scss')) return 'css/listing.css';

                                // Fallback: extract from path
                                const match = facadeId.match(/\/css\/([^\/]+)\.scss$/);
                                if (match) {
                                    return `css/${match[1]}.css`;
                                }
                            }

                            // Default CSS naming
                            if (name === '_index') {
                                return 'css/styles.css'; // fallback name
                            }
                            return `css/${name}.css`;
                        }

                        // Font files (migrated from webpack extractFonts)
                        if (/\.(woff2?|ttf|eot)$/i.test(name ?? '')) {
                            return 'assets/fonts/[name]-[hash][extname]';
                        }

                        // Default
                        return 'assets/[name]-[hash][extname]';
                    },
                },

                // External dependencies (migrated from webpack externals)
                external: isDev ? [] : ['prestashop', '$', 'jquery'],
            },

            // Source maps (migrated from webpack devtool)
            sourcemap:
                mode === 'development'
                    ? 'cheap-source-map'
                    : 'hidden-source-map',

            // Target (migrated from webpack target)
            target: 'es2015',

            // Minification (migrated from webpack ESBuildMinifyPlugin)
            minify: mode === 'production' ? 'esbuild' : false,
        },

        plugins: [
            //Static copy plugin disabled for now - can be enabled after build works
            viteStaticCopy({
                targets: [
                    {
                        // Copy dist/assets to THEME_ROOT/assets (enhanced)
                        src: 'dist/assets',
                        dest: path.resolve(process.cwd(), '../'),
                    },
                ],
            }),
        ],

        // Base URL (enhanced with environment variable support)
        base: command === 'build' ? VITE_PUBLIC_PATH : '/',

        // Module resolution (migrated and enhanced from webpack resolve)
        resolve: {
            alias: {
                '@': path.resolve(__dirname),
                '~': path.resolve(__dirname, 'node_modules'),
                // Additional aliases from webpack config
                '@node_modules': path.resolve(__dirname, 'node_modules'),
                '@themeAbstract': path.resolve(__dirname, 'css/abstracts'),
                '@css': path.resolve(__dirname, 'css'),
                '@js': path.resolve(__dirname, 'js'),
            },
        },

        // Development server configuration (migrated from webpack configureDevServer)
        server: {
            host: VITE_SERVER_ADDRESS,
            port: port,
            open: true, // Migrated from webpack open: true

            // CORS headers (migrated from webpack headers)
            cors: {
                origin: '*',
                methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
                allowedHeaders: [
                    'X-Requested-With',
                    'content-type',
                    'Authorization',
                ],
            },

            // Proxy configuration (migrated from webpack proxy)
            proxy: isDev
                ? {
                      // Proxy all requests to the PrestaShop site
                      '^(?!/(@vite|node_modules|@fs)).*': {
                          target: VITE_SITE_URL,
                          changeOrigin: true,
                          secure: false,
                      },
                  }
                : undefined,

            // File watching (migrated from webpack watchFiles)
            watch: {
                usePolling: false,
                ignored: ['!**/node_modules/**'],
            },
        },

        // CSS processing configuration (migrated from webpack extractScss and postcss)
        css: {
            postcss: {
                plugins: [
                    // Autoprefixer (migrated from webpack postcss.config.js)
                    autoprefixer,
                ],
            },
            preprocessorOptions: {
                scss: {
                    // Sass implementation (migrated from webpack sass-loader)
                    implementation: 'sass',
                    // Additional SCSS options can be added here
                },
            },
        },

        // Optimization configuration
        optimizeDeps: {
            // Include dependencies that should be pre-bundled
            include: [
                'jquery',
                'bootstrap',
                'swiper',
                '@popperjs/core',
                'sprintf-js',
                'vanilla-lazyload',
            ],
            // Exclude dependencies that should not be pre-bundled
            exclude: [
                'prestashop', // Keep as external
            ],
        },

        // ESBuild configuration (migrated from webpack esbuild-loader)
        esbuild: {
            target: 'es2015',
            // Additional esbuild options
            legalComments: 'none',
        },
    };
});
