import e from"jquery";import{p as o}from"./prestashop-8nb8P3l5.js";function r(){e(o.themeSelectors.checkout.termsLink).on("click",t=>{t.preventDefault();let c=e(t.target).attr("href");c&&(c+="?content_only=1",e.get(c,n=>{e(o.themeSelectors.modal).find(o.themeSelectors.modalContent).html(e(n).find(".page-cms").contents())}).fail(n=>{o.emit("handleError",{eventType:"clickTerms",resp:n})})),e(o.themeSelectors.modal).modal("show")}),e(o.themeSelectors.checkout.giftCheckbox).on("click",()=>{e("#gift").slideToggle()})}e(document).ready(()=>{e("body#checkout").length===1&&r(),o.on("updatedDeliveryForm",t=>{typeof t.deliveryOption=="undefined"||t.deliveryOption.length===0||(e(o.themeSelectors.checkout.carrierExtraContent).hide(),t.deliveryOption.next(o.themeSelectors.checkout.carrierExtraContent).show())}),o.on("changedCheckoutStep",t=>{typeof t.event.currentTarget!="undefined"&&e(".collapse",t.event.currentTarget).not(".show").not(".collapse .collapse").collapse("show")})});e(document).on("change",'.checkout-option input[type="radio"]',t=>{const n=e(t.currentTarget).closest(".checkout-option");n.parent().find(".checkout-option").removeClass("selected"),n.addClass("selected")});e(document).on("click",".js-checkout-step-header",t=>{const c=e(t.currentTarget).data("identifier");e(`#${c}`).addClass("-current"),e(`#content-${c}`).collapse("show").scrollTop()});
//# sourceMappingURL=checkout.js.map
