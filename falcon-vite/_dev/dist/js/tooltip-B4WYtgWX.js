var L=Object.defineProperty,U=Object.defineProperties;var $=Object.getOwnPropertyDescriptors;var S=Object.getOwnPropertySymbols;var F=Object.prototype.hasOwnProperty,H=Object.prototype.propertyIsEnumerable;var w=(o,t,e)=>t in o?L(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,h=(o,t)=>{for(var e in t||(t={}))F.call(t,e)&&w(o,e,t[e]);if(S)for(var e of S(t))H.call(t,e)&&w(o,e,t[e]);return o},O=(o,t)=>U(o,$(t));import i from"jquery";import{P as D}from"./popper-BdHdNNH2.js";import{U as c}from"./util-B8s7WWwa.js";const K=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],M=/^aria-[\w-]*$/i,j={"*":["class","dir","id","lang","role",M],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},W=/^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i,k=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function V(o,t){const e=o.nodeName.toLowerCase();if(t.indexOf(e)!==-1)return K.indexOf(e)!==-1?!!(W.test(o.nodeValue)||k.test(o.nodeValue)):!0;const s=t.filter(n=>n instanceof RegExp);for(let n=0,r=s.length;n<r;n++)if(s[n].test(e))return!0;return!1}function N(o,t,e){if(o.length===0)return o;if(e&&typeof e=="function")return e(o);const n=new window.DOMParser().parseFromString(o,"text/html"),r=Object.keys(t),T=[].slice.call(n.body.querySelectorAll("*"));for(let u=0,b=T.length;u<b;u++){const a=T[u],g=a.nodeName.toLowerCase();if(r.indexOf(a.nodeName.toLowerCase())===-1){a.parentNode.removeChild(a);continue}const I=[].slice.call(a.attributes),P=[].concat(t["*"]||[],t[g]||[]);I.forEach(y=>{V(y,P)||a.removeAttribute(y.nodeName)})}return n.body.innerHTML}const f="tooltip",Y="4.6.2",C="bs.tooltip",l=`.${C}`,z=i.fn[f],R="bs-tooltip",G=new RegExp(`(^|\\s)${R}\\S+`,"g"),B=["sanitize","whiteList","sanitizeFn"],m="fade",p="show",d="show",A="out",q=".tooltip-inner",Q=".arrow",_="hover",v="focus",X="click",J="manual",Z={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},x={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",customClass:"",sanitize:!0,sanitizeFn:null,whiteList:j,popperConfig:null},tt={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",customClass:"(string|function)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},et={HIDE:`hide${l}`,HIDDEN:`hidden${l}`,SHOW:`show${l}`,SHOWN:`shown${l}`,INSERTED:`inserted${l}`,CLICK:`click${l}`,FOCUSIN:`focusin${l}`,FOCUSOUT:`focusout${l}`,MOUSEENTER:`mouseenter${l}`,MOUSELEAVE:`mouseleave${l}`};class E{constructor(t,e){if(typeof D=="undefined")throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=t,this.config=this._getConfig(e),this.tip=null,this._setListeners()}static get VERSION(){return Y}static get Default(){return x}static get NAME(){return f}static get DATA_KEY(){return C}static get Event(){return et}static get EVENT_KEY(){return l}static get DefaultType(){return tt}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(t){if(this._isEnabled)if(t){const e=this.constructor.DATA_KEY;let s=i(t.currentTarget).data(e);s||(s=new this.constructor(t.currentTarget,this._getDelegateConfig()),i(t.currentTarget).data(e,s)),s._activeTrigger.click=!s._activeTrigger.click,s._isWithActiveTrigger()?s._enter(null,s):s._leave(null,s)}else{if(i(this.getTipElement()).hasClass(p)){this._leave(null,this);return}this._enter(null,this)}}dispose(){clearTimeout(this._timeout),i.removeData(this.element,this.constructor.DATA_KEY),i(this.element).off(this.constructor.EVENT_KEY),i(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&i(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null}show(){if(i(this.element).css("display")==="none")throw new Error("Please use show on visible elements");const t=i.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){i(this.element).trigger(t);const e=c.findShadowRoot(this.element),s=i.contains(e!==null?e:this.element.ownerDocument.documentElement,this.element);if(t.isDefaultPrevented()||!s)return;const n=this.getTipElement(),r=c.getUID(this.constructor.NAME);n.setAttribute("id",r),this.element.setAttribute("aria-describedby",r),this.setContent(),this.config.animation&&i(n).addClass(m);const T=typeof this.config.placement=="function"?this.config.placement.call(this,n,this.element):this.config.placement,u=this._getAttachment(T);this.addAttachmentClass(u);const b=this._getContainer();i(n).data(this.constructor.DATA_KEY,this),i.contains(this.element.ownerDocument.documentElement,this.tip)||i(n).appendTo(b),i(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new D(this.element,n,this._getPopperConfig(u)),i(n).addClass(p),i(n).addClass(this.config.customClass),"ontouchstart"in document.documentElement&&i(document.body).children().on("mouseover",null,i.noop);const a=()=>{this.config.animation&&this._fixTransition();const g=this._hoverState;this._hoverState=null,i(this.element).trigger(this.constructor.Event.SHOWN),g===A&&this._leave(null,this)};if(i(this.tip).hasClass(m)){const g=c.getTransitionDurationFromElement(this.tip);i(this.tip).one(c.TRANSITION_END,a).emulateTransitionEnd(g)}else a()}}hide(t){const e=this.getTipElement(),s=i.Event(this.constructor.Event.HIDE),n=()=>{this._hoverState!==d&&e.parentNode&&e.parentNode.removeChild(e),this._cleanTipClass(),this.element.removeAttribute("aria-describedby"),i(this.element).trigger(this.constructor.Event.HIDDEN),this._popper!==null&&this._popper.destroy(),t&&t()};if(i(this.element).trigger(s),!s.isDefaultPrevented()){if(i(e).removeClass(p),"ontouchstart"in document.documentElement&&i(document.body).children().off("mouseover",null,i.noop),this._activeTrigger[X]=!1,this._activeTrigger[v]=!1,this._activeTrigger[_]=!1,i(this.tip).hasClass(m)){const r=c.getTransitionDurationFromElement(e);i(e).one(c.TRANSITION_END,n).emulateTransitionEnd(r)}else n();this._hoverState=""}}update(){this._popper!==null&&this._popper.scheduleUpdate()}isWithContent(){return!!this.getTitle()}addAttachmentClass(t){i(this.getTipElement()).addClass(`${R}-${t}`)}getTipElement(){return this.tip=this.tip||i(this.config.template)[0],this.tip}setContent(){const t=this.getTipElement();this.setElementContent(i(t.querySelectorAll(q)),this.getTitle()),i(t).removeClass(`${m} ${p}`)}setElementContent(t,e){if(typeof e=="object"&&(e.nodeType||e.jquery)){this.config.html?i(e).parent().is(t)||t.empty().append(e):t.text(i(e).text());return}this.config.html?(this.config.sanitize&&(e=N(e,this.config.whiteList,this.config.sanitizeFn)),t.html(e)):t.text(e)}getTitle(){let t=this.element.getAttribute("data-original-title");return t||(t=typeof this.config.title=="function"?this.config.title.call(this.element):this.config.title),t}_getPopperConfig(t){const e={placement:t,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:Q},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:s=>{s.originalPlacement!==s.placement&&this._handlePopperPlacementChange(s)},onUpdate:s=>this._handlePopperPlacementChange(s)};return h(h({},e),this.config.popperConfig)}_getOffset(){const t={};return typeof this.config.offset=="function"?t.fn=e=>(e.offsets=h(h({},e.offsets),this.config.offset(e.offsets,this.element)),e):t.offset=this.config.offset,t}_getContainer(){return this.config.container===!1?document.body:c.isElement(this.config.container)?i(this.config.container):i(document).find(this.config.container)}_getAttachment(t){return Z[t.toUpperCase()]}_setListeners(){this.config.trigger.split(" ").forEach(e=>{if(e==="click")i(this.element).on(this.constructor.Event.CLICK,this.config.selector,s=>this.toggle(s));else if(e!==J){const s=e===_?this.constructor.Event.MOUSEENTER:this.constructor.Event.FOCUSIN,n=e===_?this.constructor.Event.MOUSELEAVE:this.constructor.Event.FOCUSOUT;i(this.element).on(s,this.config.selector,r=>this._enter(r)).on(n,this.config.selector,r=>this._leave(r))}}),this._hideModalHandler=()=>{this.element&&this.hide()},i(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=O(h({},this.config),{trigger:"manual",selector:""}):this._fixTitle()}_fixTitle(){const t=typeof this.element.getAttribute("data-original-title");(this.element.getAttribute("title")||t!=="string")&&(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))}_enter(t,e){const s=this.constructor.DATA_KEY;if(e=e||i(t.currentTarget).data(s),e||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),i(t.currentTarget).data(s,e)),t&&(e._activeTrigger[t.type==="focusin"?v:_]=!0),i(e.getTipElement()).hasClass(p)||e._hoverState===d){e._hoverState=d;return}if(clearTimeout(e._timeout),e._hoverState=d,!e.config.delay||!e.config.delay.show){e.show();return}e._timeout=setTimeout(()=>{e._hoverState===d&&e.show()},e.config.delay.show)}_leave(t,e){const s=this.constructor.DATA_KEY;if(e=e||i(t.currentTarget).data(s),e||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),i(t.currentTarget).data(s,e)),t&&(e._activeTrigger[t.type==="focusout"?v:_]=!1),!e._isWithActiveTrigger()){if(clearTimeout(e._timeout),e._hoverState=A,!e.config.delay||!e.config.delay.hide){e.hide();return}e._timeout=setTimeout(()=>{e._hoverState===A&&e.hide()},e.config.delay.hide)}}_isWithActiveTrigger(){for(const t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1}_getConfig(t){const e=i(this.element).data();return Object.keys(e).forEach(s=>{B.indexOf(s)!==-1&&delete e[s]}),t=h(h(h({},this.constructor.Default),e),typeof t=="object"&&t?t:{}),typeof t.delay=="number"&&(t.delay={show:t.delay,hide:t.delay}),typeof t.title=="number"&&(t.title=t.title.toString()),typeof t.content=="number"&&(t.content=t.content.toString()),c.typeCheckConfig(f,t,this.constructor.DefaultType),t.sanitize&&(t.template=N(t.template,t.whiteList,t.sanitizeFn)),t}_getDelegateConfig(){const t={};if(this.config)for(const e in this.config)this.constructor.Default[e]!==this.config[e]&&(t[e]=this.config[e]);return t}_cleanTipClass(){const t=i(this.getTipElement()),e=t.attr("class").match(G);e!==null&&e.length&&t.removeClass(e.join(""))}_handlePopperPlacementChange(t){this.tip=t.instance.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(t.placement))}_fixTransition(){const t=this.getTipElement(),e=this.config.animation;t.getAttribute("x-placement")===null&&(i(t).removeClass(m),this.config.animation=!1,this.hide(),this.show(),this.config.animation=e)}static _jQueryInterface(t){return this.each(function(){const e=i(this);let s=e.data(C);const n=typeof t=="object"&&t;if(!(!s&&/dispose|hide/.test(t))&&(s||(s=new E(this,n),e.data(C,s)),typeof t=="string")){if(typeof s[t]=="undefined")throw new TypeError(`No method named "${t}"`);s[t]()}})}}i.fn[f]=E._jQueryInterface;i.fn[f].Constructor=E;i.fn[f].noConflict=()=>(i.fn[f]=z,E._jQueryInterface);export{E as default};
//# sourceMappingURL=tooltip-B4WYtgWX.js.map
