var S=(r,e,t)=>new Promise((l,i)=>{var a=n=>{try{d(t.next(n))}catch(c){i(c)}},o=n=>{try{d(t.throw(n))}catch(c){i(c)}},d=n=>n.done?l(n.value):Promise.resolve(n.value).then(a,o);d((t=t.apply(r,e)).next())});import s from"jquery";class u{constructor({thumbsSliderSelector:e=".js-product-thumbs",mainSliderSelector:t=".js-product-main-images",modalSliderSelector:l=".js-modal-gallery",galleryModalSelector:i=".js-product-images-modal"}={}){this.thumbsSliderSelector=e,this.mainSliderSelector=t,this.modalSliderSelector=l,this.galleryModalSelector=i,this.mainSliderSwiperInstance=null,this.modalSliderSwiperInstance=null}init(){this.mainSliderSwiperInstance=null,this.modalSliderSwiperInstance=null,this.initProductImageSlider(),this.initModalGallerySlider()}initProductImageSlider(){return S(this,null,function*(){const e=document.querySelector(this.thumbsSliderSelector),t=document.querySelector(this.mainSliderSelector);if(!e&&!t)return;const i=yield new prestashop.SwiperSlider(e,{breakpoints:{320:{slidesPerView:3},576:{slidesPerView:4}},watchSlidesVisibility:!0,watchSlidesProgress:!0}).initSlider(),o=yield new prestashop.SwiperSlider(t,{spaceBetween:10,navigation:{nextEl:t.querySelector(".swiper-button-next"),prevEl:t.querySelector(".swiper-button-prev")},thumbs:{swiper:i}}).initSlider();this.mainSliderSwiperInstance=o})}initModalGallerySlider(){const e=document.querySelector(this.modalSliderSelector);if(!e)return;const t=()=>S(this,null,function*(){if(this.modalSliderSwiperInstance)e.style.opacity=0,setTimeout(()=>{this.modalSliderSwiperInstance.update(),this.modalSliderSwiperInstance.slideTo(this.mainSliderSwiperInstance?this.mainSliderSwiperInstance.activeIndex:0,0),e.style.opacity=1},200);else{const i=yield new prestashop.SwiperSlider(e,{slidesPerView:1,spaceBetween:10,initialSlide:this.mainSliderSwiperInstance?this.mainSliderSwiperInstance.activeIndex:0,navigation:{nextEl:e.querySelector(".swiper-button-next"),prevEl:e.querySelector(".swiper-button-prev")}}).initSlider();this.modalSliderSwiperInstance=i}});$(this.galleryModalSelector).on("show.bs.modal",t)}}function p(){s(".product-tabs .nav .nav-item:first-child a").tab("show")}function m(){const r=s('[href="#product-details"]'),e=s(r.attr("href"));e.length&&r.length&&r.hasClass("active")&&e.addClass("show active")}s(()=>{p();const r=new u;r.init(),prestashop.on("updatedProductCombination",e=>{r.init();const{product_add_to_cart:t}=e;if(t){const l=document.createElement("div");l.innerHTML=t;const i=l.querySelector(".js-product-actions-buttons");i&&document.querySelector(".js-product-actions-buttons").replaceWith(i)}}),prestashop.on("updatedProduct",()=>{m()})});
//# sourceMappingURL=product.js.map
