{"version": 3, "file": "checkout.js", "sources": ["../../js/checkout/index.js"], "sourcesContent": ["/**\n * 2007-2017 PrestaShop\n *\n * NOTICE OF LICENSE\n *\n * This source file is subject to the Academic Free License 3.0 (AFL-3.0)\n * that is bundled with this package in the file LICENSE.txt.\n * It is also available through the world-wide-web at this URL:\n * https://opensource.org/licenses/AFL-3.0\n * If you did not receive a copy of the license and are unable to\n * obtain it through the world-wide-web, please send an email\n * to <EMAIL> so we can send you a copy immediately.\n *\n * DISCLAIMER\n *\n * Do not edit or add to this file if you wish to upgrade PrestaShop to newer\n * versions in the future. If you wish to customize PrestaShop for your\n * needs please refer to http://www.prestashop.com for more information.\n *\n * <AUTHOR> SA <<EMAIL>>\n * @copyright 2007-2017 PrestaShop SA\n * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License 3.0 (AFL-3.0)\n * International Registered Trademark & Property of PrestaShop SA\n */\nimport $ from 'jquery';\nimport prestashop from '@/js/theme/components/prestashop';;\n\nfunction setUpCheckout() {\n  $(prestashop.themeSelectors.checkout.termsLink).on('click', (event) => {\n    event.preventDefault();\n    let url = $(event.target).attr('href');\n\n    if (url) {\n      // TODO: Handle request if no pretty URL\n      url += '?content_only=1';\n      $.get(url, (content) => {\n        $(prestashop.themeSelectors.modal)\n          .find(prestashop.themeSelectors.modalContent)\n          .html($(content).find('.page-cms').contents());\n      }).fail((resp) => {\n        prestashop.emit('handleError', { eventType: 'clickTerms', resp });\n      });\n    }\n\n    $(prestashop.themeSelectors.modal).modal('show');\n  });\n\n  $(prestashop.themeSelectors.checkout.giftCheckbox).on('click', () => {\n    $('#gift').slideToggle();\n  });\n}\n\n$(document).ready(() => {\n  if ($('body#checkout').length === 1) {\n    setUpCheckout();\n  }\n\n  prestashop.on('updatedDeliveryForm', (params) => {\n    if (typeof params.deliveryOption === 'undefined' || params.deliveryOption.length === 0) {\n      return;\n    }\n    // Hide all carrier extra content ...\n    $(prestashop.themeSelectors.checkout.carrierExtraContent).hide();\n    // and show the one related to the selected carrier\n    params.deliveryOption.next(prestashop.themeSelectors.checkout.carrierExtraContent).show();\n  });\n  prestashop.on('changedCheckoutStep', (params) => {\n    if (typeof params.event.currentTarget !== 'undefined') {\n      $('.collapse', params.event.currentTarget).not('.show').not('.collapse .collapse').collapse('show');\n    }\n  });\n});\n\n$(document).on('change', '.checkout-option input[type=\"radio\"]', (event) => {\n  const $target = $(event.currentTarget);\n  const $block = $target.closest('.checkout-option');\n  const $relatedBlocks = $block.parent();\n\n  $relatedBlocks.find('.checkout-option').removeClass('selected');\n  $block.addClass('selected');\n});\n\n$(document).on('click', '.js-checkout-step-header', (event) => {\n  const stepIdentifier = $(event.currentTarget).data('identifier');\n  $(`#${stepIdentifier}`).addClass('-current');\n  $(`#content-${stepIdentifier}`).collapse('show').scrollTop();\n});\n"], "names": ["setUpCheckout", "$", "prestashop", "event", "url", "content", "resp", "params", "$block", "stepIdentifier"], "mappings": "mEA2BA,SAASA,GAAgB,CACvBC,EAAEC,EAAW,eAAe,SAAS,SAAS,EAAE,GAAG,QAAUC,GAAU,CACrEA,EAAM,eAAgB,EACtB,IAAIC,EAAMH,EAAEE,EAAM,MAAM,EAAE,KAAK,MAAM,EAEjCC,IAEFA,GAAO,kBACPH,EAAE,IAAIG,EAAMC,GAAY,CACtBJ,EAAEC,EAAW,eAAe,KAAK,EAC9B,KAAKA,EAAW,eAAe,YAAY,EAC3C,KAAKD,EAAEI,CAAO,EAAE,KAAK,WAAW,EAAE,UAAU,CACvD,CAAO,EAAE,KAAMC,GAAS,CAChBJ,EAAW,KAAK,cAAe,CAAE,UAAW,aAAc,KAAAI,EAAM,CACxE,CAAO,GAGHL,EAAEC,EAAW,eAAe,KAAK,EAAE,MAAM,MAAM,CACnD,CAAG,EAEDD,EAAEC,EAAW,eAAe,SAAS,YAAY,EAAE,GAAG,QAAS,IAAM,CACnED,EAAE,OAAO,EAAE,YAAa,CAC5B,CAAG,CACH,CAEAA,EAAE,QAAQ,EAAE,MAAM,IAAM,CAClBA,EAAE,eAAe,EAAE,SAAW,GAChCD,EAAe,EAGjBE,EAAW,GAAG,sBAAwBK,GAAW,CAC3C,OAAOA,EAAO,gBAAmB,aAAeA,EAAO,eAAe,SAAW,IAIrFN,EAAEC,EAAW,eAAe,SAAS,mBAAmB,EAAE,KAAM,EAEhEK,EAAO,eAAe,KAAKL,EAAW,eAAe,SAAS,mBAAmB,EAAE,KAAM,EAC7F,CAAG,EACDA,EAAW,GAAG,sBAAwBK,GAAW,CAC3C,OAAOA,EAAO,MAAM,eAAkB,aACxCN,EAAE,YAAaM,EAAO,MAAM,aAAa,EAAE,IAAI,OAAO,EAAE,IAAI,qBAAqB,EAAE,SAAS,MAAM,CAExG,CAAG,CACH,CAAC,EAEDN,EAAE,QAAQ,EAAE,GAAG,SAAU,uCAAyCE,GAAU,CAE1E,MAAMK,EADUP,EAAEE,EAAM,aAAa,EACd,QAAQ,kBAAkB,EAC1BK,EAAO,OAAQ,EAEvB,KAAK,kBAAkB,EAAE,YAAY,UAAU,EAC9DA,EAAO,SAAS,UAAU,CAC5B,CAAC,EAEDP,EAAE,QAAQ,EAAE,GAAG,QAAS,2BAA6BE,GAAU,CAC7D,MAAMM,EAAiBR,EAAEE,EAAM,aAAa,EAAE,KAAK,YAAY,EAC/DF,EAAE,IAAIQ,CAAc,EAAE,EAAE,SAAS,UAAU,EAC3CR,EAAE,YAAYQ,CAAc,EAAE,EAAE,SAAS,MAAM,EAAE,UAAW,CAC9D,CAAC"}