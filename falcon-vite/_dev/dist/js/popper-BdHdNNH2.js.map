{"version": 3, "file": "popper-BdHdNNH2.js", "sources": ["../../node_modules/.pnpm/popper.js@1.16.1/node_modules/popper.js/dist/esm/popper.js"], "sourcesContent": ["/**!\n * @fileOverview Kickass library to create and place poppers near their reference elements.\n * @version 1.16.1\n * @license\n * Copyright (c) 2016 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && typeof navigator !== 'undefined';\n\nvar timeoutDuration = function () {\n  var longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\n  for (var i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n    if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n      return 1;\n    }\n  }\n  return 0;\n}();\n\nfunction microtaskDebounce(fn) {\n  var called = false;\n  return function () {\n    if (called) {\n      return;\n    }\n    called = true;\n    window.Promise.resolve().then(function () {\n      called = false;\n      fn();\n    });\n  };\n}\n\nfunction taskDebounce(fn) {\n  var scheduled = false;\n  return function () {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(function () {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nvar supportsMicroTasks = isBrowser && window.Promise;\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nvar debounce = supportsMicroTasks ? microtaskDebounce : taskDebounce;\n\n/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nfunction isFunction(functionToCheck) {\n  var getType = {};\n  return functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n}\n\n/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nfunction getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  var window = element.ownerDocument.defaultView;\n  var css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n\n/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nfunction getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nfunction getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body;\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body;\n    case '#document':\n      return element.body;\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n\n  var _getStyleComputedProp = getStyleComputedProperty(element),\n      overflow = _getStyleComputedProp.overflow,\n      overflowX = _getStyleComputedProp.overflowX,\n      overflowY = _getStyleComputedProp.overflowY;\n\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n\n/**\n * Returns the reference node of the reference object, or the reference object itself.\n * @method\n * @memberof Popper.Utils\n * @param {Element|Object} reference - the reference element (the popper will be relative to this)\n * @returns {Element} parent\n */\nfunction getReferenceNode(reference) {\n  return reference && reference.referenceNode ? reference.referenceNode : reference;\n}\n\nvar isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode);\nvar isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);\n\n/**\n * Determines if the browser is Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @param {Number} version to check\n * @returns {Boolean} isIE\n */\nfunction isIE(version) {\n  if (version === 11) {\n    return isIE11;\n  }\n  if (version === 10) {\n    return isIE10;\n  }\n  return isIE11 || isIE10;\n}\n\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nfunction getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  var noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  var offsetParent = element.offsetParent || null;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n\n  var nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, 'position') === 'static') {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n\nfunction isOffsetContainer(element) {\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element;\n}\n\n/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nfunction getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nfunction findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  var order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING;\n  var start = order ? element1 : element2;\n  var end = order ? element2 : element1;\n\n  // Get common ancestor container\n  var range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  var commonAncestorContainer = range.commonAncestorContainer;\n\n  // Both nodes are inside #document\n\n  if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  var element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n\n/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nfunction getScroll(element) {\n  var side = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'top';\n\n  var upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    var html = element.ownerDocument.documentElement;\n    var scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nfunction includeScroll(rect, element) {\n  var subtract = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var scrollTop = getScroll(element, 'top');\n  var scrollLeft = getScroll(element, 'left');\n  var modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n\n/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nfunction getBordersSize(styles, axis) {\n  var sideA = axis === 'x' ? 'Left' : 'Top';\n  var sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return parseFloat(styles['border' + sideA + 'Width']) + parseFloat(styles['border' + sideB + 'Width']);\n}\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(body['offset' + axis], body['scroll' + axis], html['client' + axis], html['offset' + axis], html['scroll' + axis], isIE(10) ? parseInt(html['offset' + axis]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Top' : 'Left')]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Bottom' : 'Right')]) : 0);\n}\n\nfunction getWindowSizes(document) {\n  var body = document.body;\n  var html = document.documentElement;\n  var computedStyle = isIE(10) && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle)\n  };\n}\n\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\n\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\n\n\n\n\n\nvar defineProperty = function (obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n};\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\n/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nfunction getClientRect(offsets) {\n  return _extends({}, offsets, {\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height\n  });\n}\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nfunction getBoundingClientRect(element) {\n  var rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      var scrollTop = getScroll(element, 'top');\n      var scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } else {\n      rect = element.getBoundingClientRect();\n    }\n  } catch (e) {}\n\n  var result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n\n  // subtract scrollbar size from sizes\n  var sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : {};\n  var width = sizes.width || element.clientWidth || result.width;\n  var height = sizes.height || element.clientHeight || result.height;\n\n  var horizScrollbar = element.offsetWidth - width;\n  var vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    var styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n\nfunction getOffsetRectRelativeToArbitraryNode(children, parent) {\n  var fixedPosition = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var isIE10 = isIE(10);\n  var isHTML = parent.nodeName === 'HTML';\n  var childrenRect = getBoundingClientRect(children);\n  var parentRect = getBoundingClientRect(parent);\n  var scrollParent = getScrollParent(children);\n\n  var styles = getStyleComputedProperty(parent);\n  var borderTopWidth = parseFloat(styles.borderTopWidth);\n  var borderLeftWidth = parseFloat(styles.borderLeftWidth);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if (fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  var offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    var marginTop = parseFloat(styles.marginTop);\n    var marginLeft = parseFloat(styles.marginLeft);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (isIE10 && !fixedPosition ? parent.contains(scrollParent) : parent === scrollParent && scrollParent.nodeName !== 'BODY') {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n\nfunction getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  var excludeScroll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var html = element.ownerDocument.documentElement;\n  var relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  var width = Math.max(html.clientWidth, window.innerWidth || 0);\n  var height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  var scrollTop = !excludeScroll ? getScroll(html) : 0;\n  var scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n\n  var offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width: width,\n    height: height\n  };\n\n  return getClientRect(offset);\n}\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {Boolean} answer to \"isFixed?\"\n */\nfunction isFixed(element) {\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  var parentNode = getParentNode(element);\n  if (!parentNode) {\n    return false;\n  }\n  return isFixed(parentNode);\n}\n\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nfunction getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  var el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n}\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nfunction getBoundaries(popper, reference, padding, boundariesElement) {\n  var fixedPosition = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n\n  // NOTE: 1 DOM access here\n\n  var boundaries = { top: 0, left: 0 };\n  var offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    var boundariesNode = void 0;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    var offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent, fixedPosition);\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      var _getWindowSizes = getWindowSizes(popper.ownerDocument),\n          height = _getWindowSizes.height,\n          width = _getWindowSizes.width;\n\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  padding = padding || 0;\n  var isPaddingNumber = typeof padding === 'number';\n  boundaries.left += isPaddingNumber ? padding : padding.left || 0;\n  boundaries.top += isPaddingNumber ? padding : padding.top || 0;\n  boundaries.right -= isPaddingNumber ? padding : padding.right || 0;\n  boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0;\n\n  return boundaries;\n}\n\nfunction getArea(_ref) {\n  var width = _ref.width,\n      height = _ref.height;\n\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeAutoPlacement(placement, refRect, popper, reference, boundariesElement) {\n  var padding = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;\n\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  var boundaries = getBoundaries(popper, reference, padding, boundariesElement);\n\n  var rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height\n    }\n  };\n\n  var sortedAreas = Object.keys(rects).map(function (key) {\n    return _extends({\n      key: key\n    }, rects[key], {\n      area: getArea(rects[key])\n    });\n  }).sort(function (a, b) {\n    return b.area - a.area;\n  });\n\n  var filteredAreas = sortedAreas.filter(function (_ref2) {\n    var width = _ref2.width,\n        height = _ref2.height;\n    return width >= popper.clientWidth && height >= popper.clientHeight;\n  });\n\n  var computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].key : sortedAreas[0].key;\n\n  var variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? '-' + variation : '');\n}\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nfunction getReferenceOffsets(state, popper, reference) {\n  var fixedPosition = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n\n  var commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n\n/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nfunction getOuterSizes(element) {\n  var window = element.ownerDocument.defaultView;\n  var styles = window.getComputedStyle(element);\n  var x = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0);\n  var y = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0);\n  var result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x\n  };\n  return result;\n}\n\n/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nfunction getOppositePlacement(placement) {\n  var hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nfunction getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  var popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  var popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  var isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  var mainSide = isHoriz ? 'top' : 'left';\n  var secondarySide = isHoriz ? 'left' : 'top';\n  var measurement = isHoriz ? 'height' : 'width';\n  var secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] = referenceOffsets[mainSide] + referenceOffsets[measurement] / 2 - popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] = referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] = referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n\n/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(function (cur) {\n      return cur[prop] === value;\n    });\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  var match = find(arr, function (obj) {\n    return obj[prop] === value;\n  });\n  return arr.indexOf(match);\n}\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nfunction runModifiers(modifiers, data, ends) {\n  var modifiersToRun = ends === undefined ? modifiers : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(function (modifier) {\n    if (modifier['function']) {\n      // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    var fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nfunction update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  var data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {}\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(this.state, this.popper, this.reference, this.options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(this.options.placement, data.offsets.reference, this.popper, this.reference, this.options.modifiers.flip.boundariesElement, this.options.modifiers.flip.padding);\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  data.positionFixed = this.options.positionFixed;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(this.popper, data.offsets.reference, data.placement);\n\n  data.offsets.popper.position = this.options.positionFixed ? 'fixed' : 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n\n/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nfunction isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(function (_ref) {\n    var name = _ref.name,\n        enabled = _ref.enabled;\n    return enabled && name === modifierName;\n  });\n}\n\n/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nfunction getSupportedPropertyName(property) {\n  var prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  var upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (var i = 0; i < prefixes.length; i++) {\n    var prefix = prefixes[i];\n    var toCheck = prefix ? '' + prefix + upperProp : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n\n/**\n * Destroys the popper.\n * @method\n * @memberof Popper\n */\nfunction destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style.left = '';\n    this.popper.style.right = '';\n    this.popper.style.bottom = '';\n    this.popper.style.willChange = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicitly asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n\n/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nfunction getWindow(element) {\n  var ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  var isBody = scrollParent.nodeName === 'BODY';\n  var target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(getScrollParent(target.parentNode), event, callback, scrollParents);\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction setupEventListeners(reference, options, state, updateBound) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  var scrollElement = getScrollParent(reference);\n  attachToScrollParents(scrollElement, 'scroll', state.updateBound, state.scrollParents);\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nfunction enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(this.reference, this.options, this.state, this.scheduleUpdate);\n  }\n}\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(function (target) {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger `onUpdate` callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nfunction disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n\n/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nfunction isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setStyles(element, styles) {\n  Object.keys(styles).forEach(function (prop) {\n    var unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && isNumeric(styles[prop])) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n\n/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function (prop) {\n    var value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nfunction applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper\n * @param {Object} options - Popper.js options\n */\nfunction applyStyleOnLoad(reference, popper, options, modifierOptions, state) {\n  // compute reference element offsets\n  var referenceOffsets = getReferenceOffsets(state, popper, reference, options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  var placement = computeAutoPlacement(options.placement, referenceOffsets, popper, reference, options.modifiers.flip.boundariesElement, options.modifiers.flip.padding);\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: options.positionFixed ? 'fixed' : 'absolute' });\n\n  return options;\n}\n\n/**\n * @function\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Boolean} shouldRound - If the offsets should be rounded at all\n * @returns {Object} The popper's position offsets rounded\n *\n * The tale of pixel-perfect positioning. It's still not 100% perfect, but as\n * good as it can be within reason.\n * Discussion here: https://github.com/FezVrasta/popper.js/pull/715\n *\n * Low DPI screens cause a popper to be blurry if not using full pixels (Safari\n * as well on High DPI screens).\n *\n * Firefox prefers no rounding for positioning and does not have blurriness on\n * high DPI screens.\n *\n * Only horizontal placement and left/right values need to be considered.\n */\nfunction getRoundedOffsets(data, shouldRound) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n  var round = Math.round,\n      floor = Math.floor;\n\n  var noRound = function noRound(v) {\n    return v;\n  };\n\n  var referenceWidth = round(reference.width);\n  var popperWidth = round(popper.width);\n\n  var isVertical = ['left', 'right'].indexOf(data.placement) !== -1;\n  var isVariation = data.placement.indexOf('-') !== -1;\n  var sameWidthParity = referenceWidth % 2 === popperWidth % 2;\n  var bothOddWidth = referenceWidth % 2 === 1 && popperWidth % 2 === 1;\n\n  var horizontalToInteger = !shouldRound ? noRound : isVertical || isVariation || sameWidthParity ? round : floor;\n  var verticalToInteger = !shouldRound ? noRound : round;\n\n  return {\n    left: horizontalToInteger(bothOddWidth && !isVariation && shouldRound ? popper.left - 1 : popper.left),\n    top: verticalToInteger(popper.top),\n    bottom: verticalToInteger(popper.bottom),\n    right: horizontalToInteger(popper.right)\n  };\n}\n\nvar isFirefox = isBrowser && /Firefox/i.test(navigator.userAgent);\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeStyle(data, options) {\n  var x = options.x,\n      y = options.y;\n  var popper = data.offsets.popper;\n\n  // Remove this legacy support in Popper.js v2\n\n  var legacyGpuAccelerationOption = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'applyStyle';\n  }).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');\n  }\n  var gpuAcceleration = legacyGpuAccelerationOption !== undefined ? legacyGpuAccelerationOption : options.gpuAcceleration;\n\n  var offsetParent = getOffsetParent(data.instance.popper);\n  var offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  var styles = {\n    position: popper.position\n  };\n\n  var offsets = getRoundedOffsets(data, window.devicePixelRatio < 2 || !isFirefox);\n\n  var sideA = x === 'bottom' ? 'top' : 'bottom';\n  var sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  var prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  var left = void 0,\n      top = void 0;\n  if (sideA === 'bottom') {\n    // when offsetParent is <html> the positioning is relative to the bottom of the screen (excluding the scrollbar)\n    // and not the bottom of the html element\n    if (offsetParent.nodeName === 'HTML') {\n      top = -offsetParent.clientHeight + offsets.bottom;\n    } else {\n      top = -offsetParentRect.height + offsets.bottom;\n    }\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    if (offsetParent.nodeName === 'HTML') {\n      left = -offsetParent.clientWidth + offsets.right;\n    } else {\n      left = -offsetParentRect.width + offsets.right;\n    }\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    var invertTop = sideA === 'bottom' ? -1 : 1;\n    var invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = sideA + ', ' + sideB;\n  }\n\n  // Attributes\n  var attributes = {\n    'x-placement': data.placement\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = _extends({}, attributes, data.attributes);\n  data.styles = _extends({}, styles, data.styles);\n  data.arrowStyles = _extends({}, data.offsets.arrow, data.arrowStyles);\n\n  return data;\n}\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nfunction isModifierRequired(modifiers, requestingName, requestedName) {\n  var requesting = find(modifiers, function (_ref) {\n    var name = _ref.name;\n    return name === requestingName;\n  });\n\n  var isRequired = !!requesting && modifiers.some(function (modifier) {\n    return modifier.name === requestedName && modifier.enabled && modifier.order < requesting.order;\n  });\n\n  if (!isRequired) {\n    var _requesting = '`' + requestingName + '`';\n    var requested = '`' + requestedName + '`';\n    console.warn(requested + ' modifier is required by ' + _requesting + ' modifier in order to work, be sure to include it before ' + _requesting + '!');\n  }\n  return isRequired;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction arrow(data, options) {\n  var _data$offsets$arrow;\n\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  var arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn('WARNING: `arrow.element` must be child of its popper element!');\n      return data;\n    }\n  }\n\n  var placement = data.placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  var len = isVertical ? 'height' : 'width';\n  var sideCapitalized = isVertical ? 'Top' : 'Left';\n  var side = sideCapitalized.toLowerCase();\n  var altSide = isVertical ? 'left' : 'top';\n  var opSide = isVertical ? 'bottom' : 'right';\n  var arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjunction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] += reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  var center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  var css = getStyleComputedProperty(data.instance.popper);\n  var popperMarginSide = parseFloat(css['margin' + sideCapitalized]);\n  var popperBorderSide = parseFloat(css['border' + sideCapitalized + 'Width']);\n  var sideValue = center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = (_data$offsets$arrow = {}, defineProperty(_data$offsets$arrow, side, Math.round(sideValue)), defineProperty(_data$offsets$arrow, altSide, ''), _data$offsets$arrow);\n\n  return data;\n}\n\n/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nfunction getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n\n/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-end` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nvar placements = ['auto-start', 'auto', 'auto-end', 'top-start', 'top', 'top-end', 'right-start', 'right', 'right-end', 'bottom-end', 'bottom', 'bottom-start', 'left-end', 'left', 'left-start'];\n\n// Get rid of `auto` `auto-start` and `auto-end`\nvar validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nfunction clockwise(placement) {\n  var counter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var index = validPlacements.indexOf(placement);\n  var arr = validPlacements.slice(index + 1).concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n\nvar BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise'\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, options.boundariesElement, data.positionFixed);\n\n  var placement = data.placement.split('-')[0];\n  var placementOpposite = getOppositePlacement(placement);\n  var variation = data.placement.split('-')[1] || '';\n\n  var flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach(function (step, index) {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    var popperOffsets = data.offsets.popper;\n    var refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    var floor = Math.floor;\n    var overlapsRef = placement === 'left' && floor(popperOffsets.right) > floor(refOffsets.left) || placement === 'right' && floor(popperOffsets.left) < floor(refOffsets.right) || placement === 'top' && floor(popperOffsets.bottom) > floor(refOffsets.top) || placement === 'bottom' && floor(popperOffsets.top) < floor(refOffsets.bottom);\n\n    var overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    var overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    var overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    var overflowsBottom = floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    var overflowsBoundaries = placement === 'left' && overflowsLeft || placement === 'right' && overflowsRight || placement === 'top' && overflowsTop || placement === 'bottom' && overflowsBottom;\n\n    // flip the variation if required\n    var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n\n    // flips variation if reference element overflows boundaries\n    var flippedVariationByRef = !!options.flipVariations && (isVertical && variation === 'start' && overflowsLeft || isVertical && variation === 'end' && overflowsRight || !isVertical && variation === 'start' && overflowsTop || !isVertical && variation === 'end' && overflowsBottom);\n\n    // flips variation if popper content overflows boundaries\n    var flippedVariationByContent = !!options.flipVariationsByContent && (isVertical && variation === 'start' && overflowsRight || isVertical && variation === 'end' && overflowsLeft || !isVertical && variation === 'start' && overflowsBottom || !isVertical && variation === 'end' && overflowsTop);\n\n    var flippedVariation = flippedVariationByRef || flippedVariationByContent;\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = _extends({}, data.offsets.popper, getPopperOffsets(data.instance.popper, data.offsets.reference, data.placement));\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction keepTogether(data) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var placement = data.placement.split('-')[0];\n  var floor = Math.floor;\n  var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  var side = isVertical ? 'right' : 'bottom';\n  var opSide = isVertical ? 'left' : 'top';\n  var measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] = floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nfunction toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  var split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  var value = +split[1];\n  var unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    var element = void 0;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    var rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    var size = void 0;\n    if (unit === 'vh') {\n      size = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);\n    } else {\n      size = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nfunction parseOffset(offset, popperOffsets, referenceOffsets, basePlacement) {\n  var offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  var useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  var fragments = offset.split(/(\\+|\\-)/).map(function (frag) {\n    return frag.trim();\n  });\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  var divider = fragments.indexOf(find(fragments, function (frag) {\n    return frag.search(/,|\\s/) !== -1;\n  }));\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  var splitRegex = /\\s*,\\s*|\\s+/;\n  var ops = divider !== -1 ? [fragments.slice(0, divider).concat([fragments[divider].split(splitRegex)[0]]), [fragments[divider].split(splitRegex)[1]].concat(fragments.slice(divider + 1))] : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map(function (op, index) {\n    // Most of the units rely on the orientation of the popper\n    var measurement = (index === 1 ? !useHeight : useHeight) ? 'height' : 'width';\n    var mergeWithPrevious = false;\n    return op\n    // This aggregates any `+` or `-` sign that aren't considered operators\n    // e.g.: 10 + +5 => [10, +, +5]\n    .reduce(function (a, b) {\n      if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n        a[a.length - 1] = b;\n        mergeWithPrevious = true;\n        return a;\n      } else if (mergeWithPrevious) {\n        a[a.length - 1] += b;\n        mergeWithPrevious = false;\n        return a;\n      } else {\n        return a.concat(b);\n      }\n    }, [])\n    // Here we convert the string values into number values (in px)\n    .map(function (str) {\n      return toValue(str, measurement, popperOffsets, referenceOffsets);\n    });\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach(function (op, index) {\n    op.forEach(function (frag, index2) {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nfunction offset(data, _ref) {\n  var offset = _ref.offset;\n  var placement = data.placement,\n      _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var basePlacement = placement.split('-')[0];\n\n  var offsets = void 0;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction preventOverflow(data, options) {\n  var boundariesElement = options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  // NOTE: DOM access here\n  // resets the popper's position so that the document size can be calculated excluding\n  // the size of the popper element itself\n  var transformProp = getSupportedPropertyName('transform');\n  var popperStyles = data.instance.popper.style; // assignment to help minification\n  var top = popperStyles.top,\n      left = popperStyles.left,\n      transform = popperStyles[transformProp];\n\n  popperStyles.top = '';\n  popperStyles.left = '';\n  popperStyles[transformProp] = '';\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, boundariesElement, data.positionFixed);\n\n  // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n  popperStyles.top = top;\n  popperStyles.left = left;\n  popperStyles[transformProp] = transform;\n\n  options.boundaries = boundaries;\n\n  var order = options.priority;\n  var popper = data.offsets.popper;\n\n  var check = {\n    primary: function primary(placement) {\n      var value = popper[placement];\n      if (popper[placement] < boundaries[placement] && !options.escapeWithReference) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return defineProperty({}, placement, value);\n    },\n    secondary: function secondary(placement) {\n      var mainSide = placement === 'right' ? 'left' : 'top';\n      var value = popper[mainSide];\n      if (popper[placement] > boundaries[placement] && !options.escapeWithReference) {\n        value = Math.min(popper[mainSide], boundaries[placement] - (placement === 'right' ? popper.width : popper.height));\n      }\n      return defineProperty({}, mainSide, value);\n    }\n  };\n\n  order.forEach(function (placement) {\n    var side = ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = _extends({}, popper, check[side](placement));\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction shift(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    var _data$offsets = data.offsets,\n        reference = _data$offsets.reference,\n        popper = _data$offsets.popper;\n\n    var isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    var side = isVertical ? 'left' : 'top';\n    var measurement = isVertical ? 'width' : 'height';\n\n    var shiftOffsets = {\n      start: defineProperty({}, side, reference[side]),\n      end: defineProperty({}, side, reference[side] + reference[measurement] - popper[measurement])\n    };\n\n    data.offsets.popper = _extends({}, popper, shiftOffsets[shiftvariation]);\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  var refRect = data.offsets.reference;\n  var bound = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'preventOverflow';\n  }).boundaries;\n\n  if (refRect.bottom < bound.top || refRect.left > bound.right || refRect.top > bound.bottom || refRect.right < bound.left) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction inner(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  var subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] = reference[basePlacement] - (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nvar modifiers = {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unit-less, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the `height`.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > You can read more on this at this [issue](https://github.com/FezVrasta/popper.js/issues/373).\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * A scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper. This makes sure the popper always has a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier. Can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent'\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near each other\n   * without leaving any gap between the two. Especially useful when the arrow is\n   * enabled and you want to ensure that it points to its reference element.\n   * It cares only about the first axis. You can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjunction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]'\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations)\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position.\n     * The popper will never be placed outside of the defined boundaries\n     * (except if `keepTogether` is enabled)\n     */\n    boundariesElement: 'viewport',\n    /**\n     * @prop {Boolean} flipVariations=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the reference element overlaps its boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariations: false,\n    /**\n     * @prop {Boolean} flipVariationsByContent=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the popper element overlaps its reference boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariationsByContent: false\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right'\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define your own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: undefined\n  }\n};\n\n/**\n * The `dataObject` is an object containing all the information used by Popper.js.\n * This object is passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overridden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass an object with the same\n * structure of the `options` object, as the 3rd argument. For example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nvar Defaults = {\n  /**\n   * Popper's placement.\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Set this to true if you want popper to position it self in 'fixed' mode\n   * @prop {Boolean} positionFixed=false\n   */\n  positionFixed: false,\n\n  /**\n   * Whether events (resize, scroll) are initially enabled.\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: function onCreate() {},\n\n  /**\n   * Callback called when the popper is updated. This callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: function onUpdate() {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js.\n   * @prop {modifiers}\n   */\n  modifiers: modifiers\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n\n// Utils\n// Methods\nvar Popper = function () {\n  /**\n   * Creates a new Popper.js instance.\n   * @class Popper\n   * @param {Element|referenceObject} reference - The reference element used to position the popper\n   * @param {Element} popper - The HTML / XML element used as the popper\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  function Popper(reference, popper) {\n    var _this = this;\n\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    classCallCheck(this, Popper);\n\n    this.scheduleUpdate = function () {\n      return requestAnimationFrame(_this.update);\n    };\n\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = _extends({}, Popper.Defaults, options);\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: []\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys(_extends({}, Popper.Defaults.modifiers, options.modifiers)).forEach(function (name) {\n      _this.options.modifiers[name] = _extends({}, Popper.Defaults.modifiers[name] || {}, options.modifiers ? options.modifiers[name] : {});\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers).map(function (name) {\n      return _extends({\n        name: name\n      }, _this.options.modifiers[name]);\n    })\n    // sort the modifiers by order\n    .sort(function (a, b) {\n      return a.order - b.order;\n    });\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(function (modifierOptions) {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(_this.reference, _this.popper, _this.options, modifierOptions, _this.state);\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    var eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n\n\n  createClass(Popper, [{\n    key: 'update',\n    value: function update$$1() {\n      return update.call(this);\n    }\n  }, {\n    key: 'destroy',\n    value: function destroy$$1() {\n      return destroy.call(this);\n    }\n  }, {\n    key: 'enableEventListeners',\n    value: function enableEventListeners$$1() {\n      return enableEventListeners.call(this);\n    }\n  }, {\n    key: 'disableEventListeners',\n    value: function disableEventListeners$$1() {\n      return disableEventListeners.call(this);\n    }\n\n    /**\n     * Schedules an update. It will run on the next UI update available.\n     * @method scheduleUpdate\n     * @memberof Popper\n     */\n\n\n    /**\n     * Collection of utilities useful when writing custom modifiers.\n     * Starting from version 1.7, this method is available only if you\n     * include `popper-utils.js` before `popper.js`.\n     *\n     * **DEPRECATION**: This way to access PopperUtils is deprecated\n     * and will be removed in v2! Use the PopperUtils module directly instead.\n     * Due to the high instability of the methods contained in Utils, we can't\n     * guarantee them to follow semver. Use them at your own risk!\n     * @static\n     * @private\n     * @type {Object}\n     * @deprecated since version 1.8\n     * @member Utils\n     * @memberof Popper\n     */\n\n  }]);\n  return Popper;\n}();\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10.\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n\n\nPopper.Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\nPopper.placements = placements;\nPopper.Defaults = Defaults;\n\nexport default Popper;\n//# sourceMappingURL=popper.js.map\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "timeoutDuration", "longerTimeoutBrowsers", "i", "microtaskDebounce", "fn", "called", "taskDebounce", "scheduled", "supportsMicroTasks", "debounce", "isFunction", "functionToCheck", "getType", "getStyleComputedProperty", "element", "property", "window", "css", "getParentNode", "getScrollParent", "_getStyleComputedProp", "overflow", "overflowX", "overflowY", "getReferenceNode", "reference", "isIE11", "isIE10", "isIE", "version", "getOffsetParent", "noOffsetParent", "offsetParent", "nodeName", "isOffsetContainer", "getRoot", "node", "findCommonOffsetParent", "element1", "element2", "order", "start", "end", "range", "commonAncestorContainer", "element1root", "getScroll", "side", "upperSide", "html", "scrollingElement", "includeScroll", "rect", "subtract", "scrollTop", "scrollLeft", "modifier", "getBordersSize", "styles", "axis", "sideA", "sideB", "getSize", "body", "computedStyle", "getWindowSizes", "document", "classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "createClass", "defineProperties", "target", "props", "descriptor", "protoProps", "staticProps", "defineProperty", "obj", "key", "value", "_extends", "source", "getClientRect", "offsets", "getBoundingClientRect", "e", "result", "sizes", "width", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vertScrollbar", "getOffsetRectRelativeToArbitraryNode", "children", "parent", "fixedPosition", "isHTML", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "getViewportOffsetRectRelativeToArtbitraryNode", "excludeScroll", "relativeOffset", "offset", "isFixed", "parentNode", "getFixedPositionOffsetParent", "el", "getBoundaries", "popper", "padding", "boundariesElement", "boundaries", "boundariesNode", "_getWindowSizes", "isPaddingNumber", "getArea", "_ref", "computeAutoPlacement", "placement", "refRect", "rects", "sorted<PERSON>reas", "a", "b", "filtered<PERSON><PERSON>s", "_ref2", "computedPlacement", "variation", "getReferenceOffsets", "state", "commonOffsetParent", "getOuterSizes", "x", "y", "getOppositePlacement", "hash", "matched", "getPopperOffsets", "referenceOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "find", "arr", "check", "findIndex", "prop", "cur", "match", "runModifiers", "modifiers", "data", "ends", "modifiersToRun", "update", "isModifierEnabled", "modifierName", "name", "enabled", "getSupportedPropertyName", "prefixes", "upperProp", "prefix", "to<PERSON><PERSON><PERSON>", "destroy", "getWindow", "ownerDocument", "attachToScrollParents", "event", "callback", "scrollParents", "isBody", "setupEventListeners", "options", "updateBound", "scrollElement", "enableEventListeners", "removeEventListeners", "disableEventListeners", "isNumeric", "n", "setStyles", "unit", "setAttributes", "attributes", "applyStyle", "applyStyleOnLoad", "modifierOptions", "getRoundedOffsets", "shouldRound", "_data$offsets", "round", "floor", "noRound", "v", "referenceWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isVertical", "isVariation", "sameWidthParity", "bothOddWidth", "horizontalToInteger", "verticalToInteger", "isFirefox", "computeStyle", "legacyGpuAccelerationOption", "gpuAcceleration", "offsetParentRect", "prefixedProperty", "left", "top", "invertTop", "invertLeft", "isModifierRequired", "requestingName", "requested<PERSON><PERSON>", "requesting", "isRequired", "_requesting", "requested", "arrow", "_data$offsets$arrow", "arrowElement", "len", "sideCapitalized", "altSide", "opSide", "arrowElementSize", "center", "popperMarginSide", "popperBorderSide", "sideValue", "getOppositeVariation", "placements", "validPlacements", "clockwise", "counter", "index", "BEHAVIORS", "flip", "placementOpposite", "flipOrder", "step", "refOffsets", "overlapsRef", "overflowsLeft", "overflowsRight", "overflowsTop", "overflowsBottom", "overflowsBoundaries", "flippedVariationByRef", "flippedVariationByContent", "flippedVariation", "keepTogether", "toValue", "str", "split", "size", "parseOffset", "basePlacement", "useHeight", "fragments", "frag", "divider", "splitRegex", "ops", "op", "mergeWithPrevious", "index2", "preventOverflow", "transformProp", "popperStyles", "transform", "shift", "shiftvariation", "shiftOffsets", "hide", "bound", "inner", "subtractLength", "De<PERSON>ults", "<PERSON><PERSON>", "_this", "eventsEnabled"], "mappings": "AAwBA,IAAIA,EAAY,OAAO,QAAW,aAAe,OAAO,UAAa,aAAe,OAAO,WAAc,YAErGC,GAAkB,UAAY,CAEhC,QADIC,EAAwB,CAAC,OAAQ,UAAW,SAAS,EAChDC,EAAI,EAAGA,EAAID,EAAsB,OAAQC,GAAK,EACrD,GAAIH,GAAa,UAAU,UAAU,QAAQE,EAAsBC,CAAC,CAAC,GAAK,EACxE,MAAO,GAGX,MAAO,EACT,EAAG,EAEH,SAASC,GAAkBC,EAAI,CAC7B,IAAIC,EAAS,GACb,OAAO,UAAY,CACbA,IAGJA,EAAS,GACT,OAAO,QAAQ,QAAS,EAAC,KAAK,UAAY,CACxCA,EAAS,GACTD,EAAI,CACV,CAAK,EACF,CACH,CAEA,SAASE,GAAaF,EAAI,CACxB,IAAIG,EAAY,GAChB,OAAO,UAAY,CACZA,IACHA,EAAY,GACZ,WAAW,UAAY,CACrBA,EAAY,GACZH,EAAI,CACL,EAAEJ,EAAe,EAErB,CACH,CAEA,IAAIQ,GAAqBT,GAAa,OAAO,QAWzCU,GAAWD,GAAqBL,GAAoBG,GASxD,SAASI,EAAWC,EAAiB,CACnC,IAAIC,EAAU,CAAE,EAChB,OAAOD,GAAmBC,EAAQ,SAAS,KAAKD,CAAe,IAAM,mBACvE,CASA,SAASE,EAAyBC,EAASC,EAAU,CACnD,GAAID,EAAQ,WAAa,EACvB,MAAO,CAAE,EAGX,IAAIE,EAASF,EAAQ,cAAc,YAC/BG,EAAMD,EAAO,iBAAiBF,EAAS,IAAI,EAC/C,OAAOC,EAAWE,EAAIF,CAAQ,EAAIE,CACpC,CASA,SAASC,EAAcJ,EAAS,CAC9B,OAAIA,EAAQ,WAAa,OAChBA,EAEFA,EAAQ,YAAcA,EAAQ,IACvC,CASA,SAASK,EAAgBL,EAAS,CAEhC,GAAI,CAACA,EACH,OAAO,SAAS,KAGlB,OAAQA,EAAQ,SAAQ,CACtB,IAAK,OACL,IAAK,OACH,OAAOA,EAAQ,cAAc,KAC/B,IAAK,YACH,OAAOA,EAAQ,IACrB,CAIE,IAAIM,EAAwBP,EAAyBC,CAAO,EACxDO,EAAWD,EAAsB,SACjCE,EAAYF,EAAsB,UAClCG,EAAYH,EAAsB,UAEtC,MAAI,wBAAwB,KAAKC,EAAWE,EAAYD,CAAS,EACxDR,EAGFK,EAAgBD,EAAcJ,CAAO,CAAC,CAC/C,CASA,SAASU,EAAiBC,EAAW,CACnC,OAAOA,GAAaA,EAAU,cAAgBA,EAAU,cAAgBA,CAC1E,CAEA,IAAIC,EAAS3B,GAAa,CAAC,EAAE,OAAO,sBAAwB,SAAS,cACjE4B,EAAS5B,GAAa,UAAU,KAAK,UAAU,SAAS,EAS5D,SAAS6B,EAAKC,EAAS,CACrB,OAAIA,IAAY,GACPH,EAELG,IAAY,GACPF,EAEFD,GAAUC,CACnB,CASA,SAASG,EAAgBhB,EAAS,CAChC,GAAI,CAACA,EACH,OAAO,SAAS,gBAQlB,QALIiB,EAAiBH,EAAK,EAAE,EAAI,SAAS,KAAO,KAG5CI,EAAelB,EAAQ,cAAgB,KAEpCkB,IAAiBD,GAAkBjB,EAAQ,oBAChDkB,GAAgBlB,EAAUA,EAAQ,oBAAoB,aAGxD,IAAImB,EAAWD,GAAgBA,EAAa,SAE5C,MAAI,CAACC,GAAYA,IAAa,QAAUA,IAAa,OAC5CnB,EAAUA,EAAQ,cAAc,gBAAkB,SAAS,gBAKhE,CAAC,KAAM,KAAM,OAAO,EAAE,QAAQkB,EAAa,QAAQ,IAAM,IAAMnB,EAAyBmB,EAAc,UAAU,IAAM,SACjHF,EAAgBE,CAAY,EAG9BA,CACT,CAEA,SAASE,GAAkBpB,EAAS,CAClC,IAAImB,EAAWnB,EAAQ,SAEvB,OAAImB,IAAa,OACR,GAEFA,IAAa,QAAUH,EAAgBhB,EAAQ,iBAAiB,IAAMA,CAC/E,CASA,SAASqB,EAAQC,EAAM,CACrB,OAAIA,EAAK,aAAe,KACfD,EAAQC,EAAK,UAAU,EAGzBA,CACT,CAUA,SAASC,EAAuBC,EAAUC,EAAU,CAElD,GAAI,CAACD,GAAY,CAACA,EAAS,UAAY,CAACC,GAAY,CAACA,EAAS,SAC5D,OAAO,SAAS,gBAIlB,IAAIC,EAAQF,EAAS,wBAAwBC,CAAQ,EAAI,KAAK,4BAC1DE,EAAQD,EAAQF,EAAWC,EAC3BG,EAAMF,EAAQD,EAAWD,EAGzBK,EAAQ,SAAS,YAAa,EAClCA,EAAM,SAASF,EAAO,CAAC,EACvBE,EAAM,OAAOD,EAAK,CAAC,EACnB,IAAIE,EAA0BD,EAAM,wBAIpC,GAAIL,IAAaM,GAA2BL,IAAaK,GAA2BH,EAAM,SAASC,CAAG,EACpG,OAAIR,GAAkBU,CAAuB,EACpCA,EAGFd,EAAgBc,CAAuB,EAIhD,IAAIC,EAAeV,EAAQG,CAAQ,EACnC,OAAIO,EAAa,KACRR,EAAuBQ,EAAa,KAAMN,CAAQ,EAElDF,EAAuBC,EAAUH,EAAQI,CAAQ,EAAE,IAAI,CAElE,CAUA,SAASO,EAAUhC,EAAS,CAC1B,IAAIiC,EAAO,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,MAE3EC,EAAYD,IAAS,MAAQ,YAAc,aAC3Cd,EAAWnB,EAAQ,SAEvB,GAAImB,IAAa,QAAUA,IAAa,OAAQ,CAC9C,IAAIgB,EAAOnC,EAAQ,cAAc,gBAC7BoC,EAAmBpC,EAAQ,cAAc,kBAAoBmC,EACjE,OAAOC,EAAiBF,CAAS,CACrC,CAEE,OAAOlC,EAAQkC,CAAS,CAC1B,CAWA,SAASG,GAAcC,EAAMtC,EAAS,CACpC,IAAIuC,EAAW,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,GAE/EC,EAAYR,EAAUhC,EAAS,KAAK,EACpCyC,EAAaT,EAAUhC,EAAS,MAAM,EACtC0C,EAAWH,EAAW,GAAK,EAC/B,OAAAD,EAAK,KAAOE,EAAYE,EACxBJ,EAAK,QAAUE,EAAYE,EAC3BJ,EAAK,MAAQG,EAAaC,EAC1BJ,EAAK,OAASG,EAAaC,EACpBJ,CACT,CAYA,SAASK,EAAeC,EAAQC,EAAM,CACpC,IAAIC,EAAQD,IAAS,IAAM,OAAS,MAChCE,EAAQD,IAAU,OAAS,QAAU,SAEzC,OAAO,WAAWF,EAAO,SAAWE,EAAQ,OAAO,CAAC,EAAI,WAAWF,EAAO,SAAWG,EAAQ,OAAO,CAAC,CACvG,CAEA,SAASC,EAAQH,EAAMI,EAAMd,EAAMe,EAAe,CAChD,OAAO,KAAK,IAAID,EAAK,SAAWJ,CAAI,EAAGI,EAAK,SAAWJ,CAAI,EAAGV,EAAK,SAAWU,CAAI,EAAGV,EAAK,SAAWU,CAAI,EAAGV,EAAK,SAAWU,CAAI,EAAG/B,EAAK,EAAE,EAAI,SAASqB,EAAK,SAAWU,CAAI,CAAC,EAAI,SAASK,EAAc,UAAYL,IAAS,SAAW,MAAQ,OAAO,CAAC,EAAI,SAASK,EAAc,UAAYL,IAAS,SAAW,SAAW,QAAQ,CAAC,EAAI,CAAC,CAC7U,CAEA,SAASM,EAAeC,EAAU,CAChC,IAAIH,EAAOG,EAAS,KAChBjB,EAAOiB,EAAS,gBAChBF,EAAgBpC,EAAK,EAAE,GAAK,iBAAiBqB,CAAI,EAErD,MAAO,CACL,OAAQa,EAAQ,SAAUC,EAAMd,EAAMe,CAAa,EACnD,MAAOF,EAAQ,QAASC,EAAMd,EAAMe,CAAa,CAClD,CACH,CAEA,IAAIG,GAAiB,SAAUC,EAAUC,EAAa,CACpD,GAAI,EAAED,aAAoBC,GACxB,MAAM,IAAI,UAAU,mCAAmC,CAE3D,EAEIC,GAAc,UAAY,CAC5B,SAASC,EAAiBC,EAAQC,EAAO,CACvC,QAASvE,EAAI,EAAGA,EAAIuE,EAAM,OAAQvE,IAAK,CACrC,IAAIwE,EAAaD,EAAMvE,CAAC,EACxBwE,EAAW,WAAaA,EAAW,YAAc,GACjDA,EAAW,aAAe,GACtB,UAAWA,IAAYA,EAAW,SAAW,IACjD,OAAO,eAAeF,EAAQE,EAAW,IAAKA,CAAU,CAC9D,CACA,CAEE,OAAO,SAAUL,EAAaM,EAAYC,EAAa,CACrD,OAAID,GAAYJ,EAAiBF,EAAY,UAAWM,CAAU,EAC9DC,GAAaL,EAAiBF,EAAaO,CAAW,EACnDP,CACR,CACH,EAAG,EAMCQ,EAAiB,SAAUC,EAAKC,EAAKC,EAAO,CAC9C,OAAID,KAAOD,EACT,OAAO,eAAeA,EAAKC,EAAK,CAC9B,MAAOC,EACP,WAAY,GACZ,aAAc,GACd,SAAU,EAChB,CAAK,EAEDF,EAAIC,CAAG,EAAIC,EAGNF,CACT,EAEIG,EAAW,OAAO,QAAU,SAAUT,EAAQ,CAChD,QAAStE,EAAI,EAAGA,EAAI,UAAU,OAAQA,IAAK,CACzC,IAAIgF,EAAS,UAAUhF,CAAC,EAExB,QAAS6E,KAAOG,EACV,OAAO,UAAU,eAAe,KAAKA,EAAQH,CAAG,IAClDP,EAAOO,CAAG,EAAIG,EAAOH,CAAG,EAGhC,CAEE,OAAOP,CACT,EASA,SAASW,EAAcC,EAAS,CAC9B,OAAOH,EAAS,CAAE,EAAEG,EAAS,CAC3B,MAAOA,EAAQ,KAAOA,EAAQ,MAC9B,OAAQA,EAAQ,IAAMA,EAAQ,MAClC,CAAG,CACH,CASA,SAASC,EAAsBvE,EAAS,CACtC,IAAIsC,EAAO,CAAE,EAKb,GAAI,CACF,GAAIxB,EAAK,EAAE,EAAG,CACZwB,EAAOtC,EAAQ,sBAAuB,EACtC,IAAIwC,EAAYR,EAAUhC,EAAS,KAAK,EACpCyC,EAAaT,EAAUhC,EAAS,MAAM,EAC1CsC,EAAK,KAAOE,EACZF,EAAK,MAAQG,EACbH,EAAK,QAAUE,EACfF,EAAK,OAASG,CACpB,MACMH,EAAOtC,EAAQ,sBAAuB,CAEzC,OAAQwE,EAAG,CAAA,CAEZ,IAAIC,EAAS,CACX,KAAMnC,EAAK,KACX,IAAKA,EAAK,IACV,MAAOA,EAAK,MAAQA,EAAK,KACzB,OAAQA,EAAK,OAASA,EAAK,GAC5B,EAGGoC,EAAQ1E,EAAQ,WAAa,OAASmD,EAAenD,EAAQ,aAAa,EAAI,CAAE,EAChF2E,EAAQD,EAAM,OAAS1E,EAAQ,aAAeyE,EAAO,MACrDG,EAASF,EAAM,QAAU1E,EAAQ,cAAgByE,EAAO,OAExDI,EAAiB7E,EAAQ,YAAc2E,EACvCG,EAAgB9E,EAAQ,aAAe4E,EAI3C,GAAIC,GAAkBC,EAAe,CACnC,IAAIlC,EAAS7C,EAAyBC,CAAO,EAC7C6E,GAAkBlC,EAAeC,EAAQ,GAAG,EAC5CkC,GAAiBnC,EAAeC,EAAQ,GAAG,EAE3C6B,EAAO,OAASI,EAChBJ,EAAO,QAAUK,CACrB,CAEE,OAAOT,EAAcI,CAAM,CAC7B,CAEA,SAASM,EAAqCC,EAAUC,EAAQ,CAC9D,IAAIC,EAAgB,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,GAEpFrE,EAASC,EAAK,EAAE,EAChBqE,EAASF,EAAO,WAAa,OAC7BG,EAAeb,EAAsBS,CAAQ,EAC7CK,EAAad,EAAsBU,CAAM,EACzCK,EAAejF,EAAgB2E,CAAQ,EAEvCpC,EAAS7C,EAAyBkF,CAAM,EACxCM,EAAiB,WAAW3C,EAAO,cAAc,EACjD4C,EAAkB,WAAW5C,EAAO,eAAe,EAGnDsC,GAAiBC,IACnBE,EAAW,IAAM,KAAK,IAAIA,EAAW,IAAK,CAAC,EAC3CA,EAAW,KAAO,KAAK,IAAIA,EAAW,KAAM,CAAC,GAE/C,IAAIf,EAAUD,EAAc,CAC1B,IAAKe,EAAa,IAAMC,EAAW,IAAME,EACzC,KAAMH,EAAa,KAAOC,EAAW,KAAOG,EAC5C,MAAOJ,EAAa,MACpB,OAAQA,EAAa,MACzB,CAAG,EAQD,GAPAd,EAAQ,UAAY,EACpBA,EAAQ,WAAa,EAMjB,CAACzD,GAAUsE,EAAQ,CACrB,IAAIM,EAAY,WAAW7C,EAAO,SAAS,EACvC8C,EAAa,WAAW9C,EAAO,UAAU,EAE7C0B,EAAQ,KAAOiB,EAAiBE,EAChCnB,EAAQ,QAAUiB,EAAiBE,EACnCnB,EAAQ,MAAQkB,EAAkBE,EAClCpB,EAAQ,OAASkB,EAAkBE,EAGnCpB,EAAQ,UAAYmB,EACpBnB,EAAQ,WAAaoB,CACzB,CAEE,OAAI7E,GAAU,CAACqE,EAAgBD,EAAO,SAASK,CAAY,EAAIL,IAAWK,GAAgBA,EAAa,WAAa,UAClHhB,EAAUjC,GAAciC,EAASW,CAAM,GAGlCX,CACT,CAEA,SAASqB,GAA8C3F,EAAS,CAC9D,IAAI4F,EAAgB,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,GAEpFzD,EAAOnC,EAAQ,cAAc,gBAC7B6F,EAAiBd,EAAqC/E,EAASmC,CAAI,EACnEwC,EAAQ,KAAK,IAAIxC,EAAK,YAAa,OAAO,YAAc,CAAC,EACzDyC,EAAS,KAAK,IAAIzC,EAAK,aAAc,OAAO,aAAe,CAAC,EAE5DK,EAAaoD,EAAkC,EAAlB5D,EAAUG,CAAI,EAC3CM,EAAcmD,EAA0C,EAA1B5D,EAAUG,EAAM,MAAM,EAEpD2D,EAAS,CACX,IAAKtD,EAAYqD,EAAe,IAAMA,EAAe,UACrD,KAAMpD,EAAaoD,EAAe,KAAOA,EAAe,WACxD,MAAOlB,EACP,OAAQC,CACT,EAED,OAAOP,EAAcyB,CAAM,CAC7B,CAUA,SAASC,EAAQ/F,EAAS,CACxB,IAAImB,EAAWnB,EAAQ,SACvB,GAAImB,IAAa,QAAUA,IAAa,OACtC,MAAO,GAET,GAAIpB,EAAyBC,EAAS,UAAU,IAAM,QACpD,MAAO,GAET,IAAIgG,EAAa5F,EAAcJ,CAAO,EACtC,OAAKgG,EAGED,EAAQC,CAAU,EAFhB,EAGX,CAUA,SAASC,EAA6BjG,EAAS,CAE7C,GAAI,CAACA,GAAW,CAACA,EAAQ,eAAiBc,EAAI,EAC5C,OAAO,SAAS,gBAGlB,QADIoF,EAAKlG,EAAQ,cACVkG,GAAMnG,EAAyBmG,EAAI,WAAW,IAAM,QACzDA,EAAKA,EAAG,cAEV,OAAOA,GAAM,SAAS,eACxB,CAaA,SAASC,EAAcC,EAAQzF,EAAW0F,EAASC,EAAmB,CACpE,IAAIpB,EAAgB,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,GAIpFqB,EAAa,CAAE,IAAK,EAAG,KAAM,CAAG,EAChCrF,EAAegE,EAAgBe,EAA6BG,CAAM,EAAI7E,EAAuB6E,EAAQ1F,EAAiBC,CAAS,CAAC,EAGpI,GAAI2F,IAAsB,WACxBC,EAAaZ,GAA8CzE,EAAcgE,CAAa,MACjF,CAEL,IAAIsB,EAAiB,OACjBF,IAAsB,gBACxBE,EAAiBnG,EAAgBD,EAAcO,CAAS,CAAC,EACrD6F,EAAe,WAAa,SAC9BA,EAAiBJ,EAAO,cAAc,kBAE/BE,IAAsB,SAC/BE,EAAiBJ,EAAO,cAAc,gBAEtCI,EAAiBF,EAGnB,IAAIhC,EAAUS,EAAqCyB,EAAgBtF,EAAcgE,CAAa,EAG9F,GAAIsB,EAAe,WAAa,QAAU,CAACT,EAAQ7E,CAAY,EAAG,CAChE,IAAIuF,EAAkBtD,EAAeiD,EAAO,aAAa,EACrDxB,EAAS6B,EAAgB,OACzB9B,EAAQ8B,EAAgB,MAE5BF,EAAW,KAAOjC,EAAQ,IAAMA,EAAQ,UACxCiC,EAAW,OAAS3B,EAASN,EAAQ,IACrCiC,EAAW,MAAQjC,EAAQ,KAAOA,EAAQ,WAC1CiC,EAAW,MAAQ5B,EAAQL,EAAQ,IACzC,MAEMiC,EAAajC,CAEnB,CAGE+B,EAAUA,GAAW,EACrB,IAAIK,EAAkB,OAAOL,GAAY,SACzC,OAAAE,EAAW,MAAQG,EAAkBL,EAAUA,EAAQ,MAAQ,EAC/DE,EAAW,KAAOG,EAAkBL,EAAUA,EAAQ,KAAO,EAC7DE,EAAW,OAASG,EAAkBL,EAAUA,EAAQ,OAAS,EACjEE,EAAW,QAAUG,EAAkBL,EAAUA,EAAQ,QAAU,EAE5DE,CACT,CAEA,SAASI,GAAQC,EAAM,CACrB,IAAIjC,EAAQiC,EAAK,MACbhC,EAASgC,EAAK,OAElB,OAAOjC,EAAQC,CACjB,CAWA,SAASiC,GAAqBC,EAAWC,EAASX,EAAQzF,EAAW2F,EAAmB,CACtF,IAAID,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,EAElF,GAAIS,EAAU,QAAQ,MAAM,IAAM,GAChC,OAAOA,EAGT,IAAIP,EAAaJ,EAAcC,EAAQzF,EAAW0F,EAASC,CAAiB,EAExEU,EAAQ,CACV,IAAK,CACH,MAAOT,EAAW,MAClB,OAAQQ,EAAQ,IAAMR,EAAW,GAClC,EACD,MAAO,CACL,MAAOA,EAAW,MAAQQ,EAAQ,MAClC,OAAQR,EAAW,MACpB,EACD,OAAQ,CACN,MAAOA,EAAW,MAClB,OAAQA,EAAW,OAASQ,EAAQ,MACrC,EACD,KAAM,CACJ,MAAOA,EAAQ,KAAOR,EAAW,KACjC,OAAQA,EAAW,MACzB,CACG,EAEGU,EAAc,OAAO,KAAKD,CAAK,EAAE,IAAI,SAAU/C,EAAK,CACtD,OAAOE,EAAS,CACd,IAAKF,CACX,EAAO+C,EAAM/C,CAAG,EAAG,CACb,KAAM0C,GAAQK,EAAM/C,CAAG,CAAC,CAC9B,CAAK,CACF,CAAA,EAAE,KAAK,SAAUiD,EAAGC,EAAG,CACtB,OAAOA,EAAE,KAAOD,EAAE,IACtB,CAAG,EAEGE,EAAgBH,EAAY,OAAO,SAAUI,EAAO,CACtD,IAAI1C,EAAQ0C,EAAM,MACdzC,EAASyC,EAAM,OACnB,OAAO1C,GAASyB,EAAO,aAAexB,GAAUwB,EAAO,YAC3D,CAAG,EAEGkB,EAAoBF,EAAc,OAAS,EAAIA,EAAc,CAAC,EAAE,IAAMH,EAAY,CAAC,EAAE,IAErFM,EAAYT,EAAU,MAAM,GAAG,EAAE,CAAC,EAEtC,OAAOQ,GAAqBC,EAAY,IAAMA,EAAY,GAC5D,CAYA,SAASC,GAAoBC,EAAOrB,EAAQzF,EAAW,CACrD,IAAIuE,EAAgB,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,KAEpFwC,EAAqBxC,EAAgBe,EAA6BG,CAAM,EAAI7E,EAAuB6E,EAAQ1F,EAAiBC,CAAS,CAAC,EAC1I,OAAOoE,EAAqCpE,EAAW+G,EAAoBxC,CAAa,CAC1F,CASA,SAASyC,GAAc3H,EAAS,CAC9B,IAAIE,EAASF,EAAQ,cAAc,YAC/B4C,EAAS1C,EAAO,iBAAiBF,CAAO,EACxC4H,EAAI,WAAWhF,EAAO,WAAa,CAAC,EAAI,WAAWA,EAAO,cAAgB,CAAC,EAC3EiF,EAAI,WAAWjF,EAAO,YAAc,CAAC,EAAI,WAAWA,EAAO,aAAe,CAAC,EAC3E6B,EAAS,CACX,MAAOzE,EAAQ,YAAc6H,EAC7B,OAAQ7H,EAAQ,aAAe4H,CAChC,EACD,OAAOnD,CACT,CASA,SAASqD,EAAqBhB,EAAW,CACvC,IAAIiB,EAAO,CAAE,KAAM,QAAS,MAAO,OAAQ,OAAQ,MAAO,IAAK,QAAU,EACzE,OAAOjB,EAAU,QAAQ,yBAA0B,SAAUkB,EAAS,CACpE,OAAOD,EAAKC,CAAO,CACvB,CAAG,CACH,CAYA,SAASC,GAAiB7B,EAAQ8B,EAAkBpB,EAAW,CAC7DA,EAAYA,EAAU,MAAM,GAAG,EAAE,CAAC,EAGlC,IAAIqB,EAAaR,GAAcvB,CAAM,EAGjCgC,EAAgB,CAClB,MAAOD,EAAW,MAClB,OAAQA,EAAW,MACpB,EAGGE,EAAU,CAAC,QAAS,MAAM,EAAE,QAAQvB,CAAS,IAAM,GACnDwB,EAAWD,EAAU,MAAQ,OAC7BE,EAAgBF,EAAU,OAAS,MACnCG,EAAcH,EAAU,SAAW,QACnCI,EAAwBJ,EAAqB,QAAX,SAEtC,OAAAD,EAAcE,CAAQ,EAAIJ,EAAiBI,CAAQ,EAAIJ,EAAiBM,CAAW,EAAI,EAAIL,EAAWK,CAAW,EAAI,EACjH1B,IAAcyB,EAChBH,EAAcG,CAAa,EAAIL,EAAiBK,CAAa,EAAIJ,EAAWM,CAAoB,EAEhGL,EAAcG,CAAa,EAAIL,EAAiBJ,EAAqBS,CAAa,CAAC,EAG9EH,CACT,CAWA,SAASM,EAAKC,EAAKC,EAAO,CAExB,OAAI,MAAM,UAAU,KACXD,EAAI,KAAKC,CAAK,EAIhBD,EAAI,OAAOC,CAAK,EAAE,CAAC,CAC5B,CAWA,SAASC,GAAUF,EAAKG,EAAM5E,EAAO,CAEnC,GAAI,MAAM,UAAU,UAClB,OAAOyE,EAAI,UAAU,SAAUI,EAAK,CAClC,OAAOA,EAAID,CAAI,IAAM5E,CAC3B,CAAK,EAIH,IAAI8E,EAAQN,EAAKC,EAAK,SAAU3E,EAAK,CACnC,OAAOA,EAAI8E,CAAI,IAAM5E,CACzB,CAAG,EACD,OAAOyE,EAAI,QAAQK,CAAK,CAC1B,CAYA,SAASC,GAAaC,EAAWC,EAAMC,EAAM,CAC3C,IAAIC,EAAiBD,IAAS,OAAYF,EAAYA,EAAU,MAAM,EAAGL,GAAUK,EAAW,OAAQE,CAAI,CAAC,EAE3G,OAAAC,EAAe,QAAQ,SAAU3G,EAAU,CACrCA,EAAS,UAEX,QAAQ,KAAK,uDAAuD,EAEtE,IAAIpD,EAAKoD,EAAS,UAAeA,EAAS,GACtCA,EAAS,SAAW9C,EAAWN,CAAE,IAInC6J,EAAK,QAAQ,OAAS9E,EAAc8E,EAAK,QAAQ,MAAM,EACvDA,EAAK,QAAQ,UAAY9E,EAAc8E,EAAK,QAAQ,SAAS,EAE7DA,EAAO7J,EAAG6J,EAAMzG,CAAQ,EAE9B,CAAG,EAEMyG,CACT,CASA,SAASG,IAAS,CAEhB,GAAI,MAAK,MAAM,YAIf,KAAIH,EAAO,CACT,SAAU,KACV,OAAQ,CAAE,EACV,YAAa,CAAE,EACf,WAAY,CAAE,EACd,QAAS,GACT,QAAS,CAAA,CACV,EAGDA,EAAK,QAAQ,UAAY3B,GAAoB,KAAK,MAAO,KAAK,OAAQ,KAAK,UAAW,KAAK,QAAQ,aAAa,EAKhH2B,EAAK,UAAYtC,GAAqB,KAAK,QAAQ,UAAWsC,EAAK,QAAQ,UAAW,KAAK,OAAQ,KAAK,UAAW,KAAK,QAAQ,UAAU,KAAK,kBAAmB,KAAK,QAAQ,UAAU,KAAK,OAAO,EAGrMA,EAAK,kBAAoBA,EAAK,UAE9BA,EAAK,cAAgB,KAAK,QAAQ,cAGlCA,EAAK,QAAQ,OAASlB,GAAiB,KAAK,OAAQkB,EAAK,QAAQ,UAAWA,EAAK,SAAS,EAE1FA,EAAK,QAAQ,OAAO,SAAW,KAAK,QAAQ,cAAgB,QAAU,WAGtEA,EAAOF,GAAa,KAAK,UAAWE,CAAI,EAInC,KAAK,MAAM,UAId,KAAK,QAAQ,SAASA,CAAI,GAH1B,KAAK,MAAM,UAAY,GACvB,KAAK,QAAQ,SAASA,CAAI,GAI9B,CAQA,SAASI,GAAkBL,EAAWM,EAAc,CAClD,OAAON,EAAU,KAAK,SAAUtC,EAAM,CACpC,IAAI6C,EAAO7C,EAAK,KACZ8C,EAAU9C,EAAK,QACnB,OAAO8C,GAAWD,IAASD,CAC/B,CAAG,CACH,CASA,SAASG,EAAyB1J,EAAU,CAI1C,QAHI2J,EAAW,CAAC,GAAO,KAAM,SAAU,MAAO,GAAG,EAC7CC,EAAY5J,EAAS,OAAO,CAAC,EAAE,cAAgBA,EAAS,MAAM,CAAC,EAE1Db,EAAI,EAAGA,EAAIwK,EAAS,OAAQxK,IAAK,CACxC,IAAI0K,EAASF,EAASxK,CAAC,EACnB2K,EAAUD,EAAS,GAAKA,EAASD,EAAY5J,EACjD,GAAI,OAAO,SAAS,KAAK,MAAM8J,CAAO,GAAM,YAC1C,OAAOA,CAEb,CACE,OAAO,IACT,CAOA,SAASC,IAAU,CACjB,YAAK,MAAM,YAAc,GAGrBT,GAAkB,KAAK,UAAW,YAAY,IAChD,KAAK,OAAO,gBAAgB,aAAa,EACzC,KAAK,OAAO,MAAM,SAAW,GAC7B,KAAK,OAAO,MAAM,IAAM,GACxB,KAAK,OAAO,MAAM,KAAO,GACzB,KAAK,OAAO,MAAM,MAAQ,GAC1B,KAAK,OAAO,MAAM,OAAS,GAC3B,KAAK,OAAO,MAAM,WAAa,GAC/B,KAAK,OAAO,MAAMI,EAAyB,WAAW,CAAC,EAAI,IAG7D,KAAK,sBAAuB,EAIxB,KAAK,QAAQ,iBACf,KAAK,OAAO,WAAW,YAAY,KAAK,MAAM,EAEzC,IACT,CAOA,SAASM,GAAUjK,EAAS,CAC1B,IAAIkK,EAAgBlK,EAAQ,cAC5B,OAAOkK,EAAgBA,EAAc,YAAc,MACrD,CAEA,SAASC,GAAsB7E,EAAc8E,EAAOC,EAAUC,EAAe,CAC3E,IAAIC,EAASjF,EAAa,WAAa,OACnC5B,EAAS6G,EAASjF,EAAa,cAAc,YAAcA,EAC/D5B,EAAO,iBAAiB0G,EAAOC,EAAU,CAAE,QAAS,GAAM,EAErDE,GACHJ,GAAsB9J,EAAgBqD,EAAO,UAAU,EAAG0G,EAAOC,EAAUC,CAAa,EAE1FA,EAAc,KAAK5G,CAAM,CAC3B,CAQA,SAAS8G,GAAoB7J,EAAW8J,EAAShD,EAAOiD,EAAa,CAEnEjD,EAAM,YAAciD,EACpBT,GAAUtJ,CAAS,EAAE,iBAAiB,SAAU8G,EAAM,YAAa,CAAE,QAAS,GAAM,EAGpF,IAAIkD,EAAgBtK,EAAgBM,CAAS,EAC7C,OAAAwJ,GAAsBQ,EAAe,SAAUlD,EAAM,YAAaA,EAAM,aAAa,EACrFA,EAAM,cAAgBkD,EACtBlD,EAAM,cAAgB,GAEfA,CACT,CAQA,SAASmD,IAAuB,CACzB,KAAK,MAAM,gBACd,KAAK,MAAQJ,GAAoB,KAAK,UAAW,KAAK,QAAS,KAAK,MAAO,KAAK,cAAc,EAElG,CAQA,SAASK,GAAqBlK,EAAW8G,EAAO,CAE9C,OAAAwC,GAAUtJ,CAAS,EAAE,oBAAoB,SAAU8G,EAAM,WAAW,EAGpEA,EAAM,cAAc,QAAQ,SAAU/D,EAAQ,CAC5CA,EAAO,oBAAoB,SAAU+D,EAAM,WAAW,CAC1D,CAAG,EAGDA,EAAM,YAAc,KACpBA,EAAM,cAAgB,CAAE,EACxBA,EAAM,cAAgB,KACtBA,EAAM,cAAgB,GACfA,CACT,CASA,SAASqD,IAAwB,CAC3B,KAAK,MAAM,gBACb,qBAAqB,KAAK,cAAc,EACxC,KAAK,MAAQD,GAAqB,KAAK,UAAW,KAAK,KAAK,EAEhE,CASA,SAASE,EAAUC,EAAG,CACpB,OAAOA,IAAM,IAAM,CAAC,MAAM,WAAWA,CAAC,CAAC,GAAK,SAASA,CAAC,CACxD,CAUA,SAASC,EAAUjL,EAAS4C,EAAQ,CAClC,OAAO,KAAKA,CAAM,EAAE,QAAQ,SAAUkG,EAAM,CAC1C,IAAIoC,EAAO,GAEP,CAAC,QAAS,SAAU,MAAO,QAAS,SAAU,MAAM,EAAE,QAAQpC,CAAI,IAAM,IAAMiC,EAAUnI,EAAOkG,CAAI,CAAC,IACtGoC,EAAO,MAETlL,EAAQ,MAAM8I,CAAI,EAAIlG,EAAOkG,CAAI,EAAIoC,CACzC,CAAG,CACH,CAUA,SAASC,GAAcnL,EAASoL,EAAY,CAC1C,OAAO,KAAKA,CAAU,EAAE,QAAQ,SAAUtC,EAAM,CAC9C,IAAI5E,EAAQkH,EAAWtC,CAAI,EACvB5E,IAAU,GACZlE,EAAQ,aAAa8I,EAAMsC,EAAWtC,CAAI,CAAC,EAE3C9I,EAAQ,gBAAgB8I,CAAI,CAElC,CAAG,CACH,CAWA,SAASuC,GAAWlC,EAAM,CAKxB,OAAA8B,EAAU9B,EAAK,SAAS,OAAQA,EAAK,MAAM,EAI3CgC,GAAchC,EAAK,SAAS,OAAQA,EAAK,UAAU,EAG/CA,EAAK,cAAgB,OAAO,KAAKA,EAAK,WAAW,EAAE,QACrD8B,EAAU9B,EAAK,aAAcA,EAAK,WAAW,EAGxCA,CACT,CAYA,SAASmC,GAAiB3K,EAAWyF,EAAQqE,EAASc,EAAiB9D,EAAO,CAE5E,IAAIS,EAAmBV,GAAoBC,EAAOrB,EAAQzF,EAAW8J,EAAQ,aAAa,EAKtF3D,EAAYD,GAAqB4D,EAAQ,UAAWvC,EAAkB9B,EAAQzF,EAAW8J,EAAQ,UAAU,KAAK,kBAAmBA,EAAQ,UAAU,KAAK,OAAO,EAErK,OAAArE,EAAO,aAAa,cAAeU,CAAS,EAI5CmE,EAAU7E,EAAQ,CAAE,SAAUqE,EAAQ,cAAgB,QAAU,WAAY,EAErEA,CACT,CAqBA,SAASe,GAAkBrC,EAAMsC,EAAa,CAC5C,IAAIC,EAAgBvC,EAAK,QACrB/C,EAASsF,EAAc,OACvB/K,EAAY+K,EAAc,UAC1BC,EAAQ,KAAK,MACbC,EAAQ,KAAK,MAEbC,EAAU,SAAiBC,EAAG,CAChC,OAAOA,CACR,EAEGC,EAAiBJ,EAAMhL,EAAU,KAAK,EACtCqL,EAAcL,EAAMvF,EAAO,KAAK,EAEhC6F,EAAa,CAAC,OAAQ,OAAO,EAAE,QAAQ9C,EAAK,SAAS,IAAM,GAC3D+C,EAAc/C,EAAK,UAAU,QAAQ,GAAG,IAAM,GAC9CgD,EAAkBJ,EAAiB,IAAMC,EAAc,EACvDI,EAAeL,EAAiB,IAAM,GAAKC,EAAc,IAAM,EAE/DK,EAAuBZ,EAAwBQ,GAAcC,GAAeC,EAAkBR,EAAQC,EAAjEC,EACrCS,EAAqBb,EAAwBE,EAAVE,EAEvC,MAAO,CACL,KAAMQ,EAAoBD,GAAgB,CAACF,GAAeT,EAAcrF,EAAO,KAAO,EAAIA,EAAO,IAAI,EACrG,IAAKkG,EAAkBlG,EAAO,GAAG,EACjC,OAAQkG,EAAkBlG,EAAO,MAAM,EACvC,MAAOiG,EAAoBjG,EAAO,KAAK,CACxC,CACH,CAEA,IAAImG,GAAYtN,GAAa,WAAW,KAAK,UAAU,SAAS,EAShE,SAASuN,GAAarD,EAAMsB,EAAS,CACnC,IAAI7C,EAAI6C,EAAQ,EACZ5C,EAAI4C,EAAQ,EACZrE,EAAS+C,EAAK,QAAQ,OAItBsD,EAA8B/D,EAAKS,EAAK,SAAS,UAAW,SAAUzG,EAAU,CAClF,OAAOA,EAAS,OAAS,YAC1B,CAAA,EAAE,gBACC+J,IAAgC,QAClC,QAAQ,KAAK,+HAA+H,EAE9I,IAAIC,EAAkBD,IAAgC,OAAYA,EAA8BhC,EAAQ,gBAEpGvJ,EAAeF,EAAgBmI,EAAK,SAAS,MAAM,EACnDwD,EAAmBpI,EAAsBrD,CAAY,EAGrD0B,EAAS,CACX,SAAUwD,EAAO,QAClB,EAEG9B,EAAUkH,GAAkBrC,EAAM,OAAO,iBAAmB,GAAK,CAACoD,EAAS,EAE3EzJ,EAAQ8E,IAAM,SAAW,MAAQ,SACjC7E,EAAQ8E,IAAM,QAAU,OAAS,QAKjC+E,EAAmBjD,EAAyB,WAAW,EAWvDkD,EAAO,OACPC,EAAM,OAqBV,GApBIhK,IAAU,SAGR5B,EAAa,WAAa,OAC5B4L,EAAM,CAAC5L,EAAa,aAAeoD,EAAQ,OAE3CwI,EAAM,CAACH,EAAiB,OAASrI,EAAQ,OAG3CwI,EAAMxI,EAAQ,IAEZvB,IAAU,QACR7B,EAAa,WAAa,OAC5B2L,EAAO,CAAC3L,EAAa,YAAcoD,EAAQ,MAE3CuI,EAAO,CAACF,EAAiB,MAAQrI,EAAQ,MAG3CuI,EAAOvI,EAAQ,KAEboI,GAAmBE,EACrBhK,EAAOgK,CAAgB,EAAI,eAAiBC,EAAO,OAASC,EAAM,SAClElK,EAAOE,CAAK,EAAI,EAChBF,EAAOG,CAAK,EAAI,EAChBH,EAAO,WAAa,gBACf,CAEL,IAAImK,EAAYjK,IAAU,SAAW,GAAK,EACtCkK,EAAajK,IAAU,QAAU,GAAK,EAC1CH,EAAOE,CAAK,EAAIgK,EAAMC,EACtBnK,EAAOG,CAAK,EAAI8J,EAAOG,EACvBpK,EAAO,WAAaE,EAAQ,KAAOC,CACvC,CAGE,IAAIqI,EAAa,CACf,cAAejC,EAAK,SACrB,EAGD,OAAAA,EAAK,WAAahF,EAAS,CAAE,EAAEiH,EAAYjC,EAAK,UAAU,EAC1DA,EAAK,OAAShF,EAAS,CAAE,EAAEvB,EAAQuG,EAAK,MAAM,EAC9CA,EAAK,YAAchF,EAAS,CAAE,EAAEgF,EAAK,QAAQ,MAAOA,EAAK,WAAW,EAE7DA,CACT,CAYA,SAAS8D,GAAmB/D,EAAWgE,EAAgBC,EAAe,CACpE,IAAIC,EAAa1E,EAAKQ,EAAW,SAAUtC,EAAM,CAC/C,IAAI6C,EAAO7C,EAAK,KAChB,OAAO6C,IAASyD,CACpB,CAAG,EAEGG,EAAa,CAAC,CAACD,GAAclE,EAAU,KAAK,SAAUxG,EAAU,CAClE,OAAOA,EAAS,OAASyK,GAAiBzK,EAAS,SAAWA,EAAS,MAAQ0K,EAAW,KAC9F,CAAG,EAED,GAAI,CAACC,EAAY,CACf,IAAIC,EAAc,IAAMJ,EAAiB,IACrCK,EAAY,IAAMJ,EAAgB,IACtC,QAAQ,KAAKI,EAAY,4BAA8BD,EAAc,4DAA8DA,EAAc,GAAG,CACxJ,CACE,OAAOD,CACT,CASA,SAASG,GAAMrE,EAAMsB,EAAS,CAC5B,IAAIgD,EAGJ,GAAI,CAACR,GAAmB9D,EAAK,SAAS,UAAW,QAAS,cAAc,EACtE,OAAOA,EAGT,IAAIuE,EAAejD,EAAQ,QAG3B,GAAI,OAAOiD,GAAiB,UAI1B,GAHAA,EAAevE,EAAK,SAAS,OAAO,cAAcuE,CAAY,EAG1D,CAACA,EACH,OAAOvE,UAKL,CAACA,EAAK,SAAS,OAAO,SAASuE,CAAY,EAC7C,eAAQ,KAAK,+DAA+D,EACrEvE,EAIX,IAAIrC,EAAYqC,EAAK,UAAU,MAAM,GAAG,EAAE,CAAC,EACvCuC,EAAgBvC,EAAK,QACrB/C,EAASsF,EAAc,OACvB/K,EAAY+K,EAAc,UAE1BO,EAAa,CAAC,OAAQ,OAAO,EAAE,QAAQnF,CAAS,IAAM,GAEtD6G,EAAM1B,EAAa,SAAW,QAC9B2B,EAAkB3B,EAAa,MAAQ,OACvChK,EAAO2L,EAAgB,YAAa,EACpCC,EAAU5B,EAAa,OAAS,MAChC6B,EAAS7B,EAAa,SAAW,QACjC8B,EAAmBpG,GAAc+F,CAAY,EAAEC,CAAG,EAQlDhN,EAAUmN,CAAM,EAAIC,EAAmB3H,EAAOnE,CAAI,IACpDkH,EAAK,QAAQ,OAAOlH,CAAI,GAAKmE,EAAOnE,CAAI,GAAKtB,EAAUmN,CAAM,EAAIC,IAG/DpN,EAAUsB,CAAI,EAAI8L,EAAmB3H,EAAO0H,CAAM,IACpD3E,EAAK,QAAQ,OAAOlH,CAAI,GAAKtB,EAAUsB,CAAI,EAAI8L,EAAmB3H,EAAO0H,CAAM,GAEjF3E,EAAK,QAAQ,OAAS9E,EAAc8E,EAAK,QAAQ,MAAM,EAGvD,IAAI6E,EAASrN,EAAUsB,CAAI,EAAItB,EAAUgN,CAAG,EAAI,EAAII,EAAmB,EAInE5N,EAAMJ,EAAyBoJ,EAAK,SAAS,MAAM,EACnD8E,EAAmB,WAAW9N,EAAI,SAAWyN,CAAe,CAAC,EAC7DM,EAAmB,WAAW/N,EAAI,SAAWyN,EAAkB,OAAO,CAAC,EACvEO,EAAYH,EAAS7E,EAAK,QAAQ,OAAOlH,CAAI,EAAIgM,EAAmBC,EAGxE,OAAAC,EAAY,KAAK,IAAI,KAAK,IAAI/H,EAAOuH,CAAG,EAAII,EAAkBI,CAAS,EAAG,CAAC,EAE3EhF,EAAK,aAAeuE,EACpBvE,EAAK,QAAQ,OAASsE,EAAsB,CAAA,EAAI1J,EAAe0J,EAAqBxL,EAAM,KAAK,MAAMkM,CAAS,CAAC,EAAGpK,EAAe0J,EAAqBI,EAAS,EAAE,EAAGJ,GAE7JtE,CACT,CASA,SAASiF,GAAqB7G,EAAW,CACvC,OAAIA,IAAc,MACT,QACEA,IAAc,QAChB,MAEFA,CACT,CAiCA,IAAI8G,GAAa,CAAC,aAAc,OAAQ,WAAY,YAAa,MAAO,UAAW,cAAe,QAAS,YAAa,aAAc,SAAU,eAAgB,WAAY,OAAQ,YAAY,EAG5LC,EAAkBD,GAAW,MAAM,CAAC,EAYxC,SAASE,EAAUzH,EAAW,CAC5B,IAAI0H,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,GAE9EC,EAAQH,EAAgB,QAAQxH,CAAS,EACzC6B,EAAM2F,EAAgB,MAAMG,EAAQ,CAAC,EAAE,OAAOH,EAAgB,MAAM,EAAGG,CAAK,CAAC,EACjF,OAAOD,EAAU7F,EAAI,QAAO,EAAKA,CACnC,CAEA,IAAI+F,EAAY,CACd,KAAM,OACN,UAAW,YACX,iBAAkB,kBACpB,EASA,SAASC,GAAKxF,EAAMsB,EAAS,CAM3B,GAJIlB,GAAkBJ,EAAK,SAAS,UAAW,OAAO,GAIlDA,EAAK,SAAWA,EAAK,YAAcA,EAAK,kBAE1C,OAAOA,EAGT,IAAI5C,EAAaJ,EAAcgD,EAAK,SAAS,OAAQA,EAAK,SAAS,UAAWsB,EAAQ,QAASA,EAAQ,kBAAmBtB,EAAK,aAAa,EAExIrC,EAAYqC,EAAK,UAAU,MAAM,GAAG,EAAE,CAAC,EACvCyF,EAAoB9G,EAAqBhB,CAAS,EAClDS,EAAY4B,EAAK,UAAU,MAAM,GAAG,EAAE,CAAC,GAAK,GAE5C0F,EAAY,CAAE,EAElB,OAAQpE,EAAQ,SAAQ,CACtB,KAAKiE,EAAU,KACbG,EAAY,CAAC/H,EAAW8H,CAAiB,EACzC,MACF,KAAKF,EAAU,UACbG,EAAYN,EAAUzH,CAAS,EAC/B,MACF,KAAK4H,EAAU,iBACbG,EAAYN,EAAUzH,EAAW,EAAI,EACrC,MACF,QACE+H,EAAYpE,EAAQ,QAC1B,CAEE,OAAAoE,EAAU,QAAQ,SAAUC,EAAML,EAAO,CACvC,GAAI3H,IAAcgI,GAAQD,EAAU,SAAWJ,EAAQ,EACrD,OAAOtF,EAGTrC,EAAYqC,EAAK,UAAU,MAAM,GAAG,EAAE,CAAC,EACvCyF,EAAoB9G,EAAqBhB,CAAS,EAElD,IAAIsB,EAAgBe,EAAK,QAAQ,OAC7B4F,EAAa5F,EAAK,QAAQ,UAG1ByC,EAAQ,KAAK,MACboD,EAAclI,IAAc,QAAU8E,EAAMxD,EAAc,KAAK,EAAIwD,EAAMmD,EAAW,IAAI,GAAKjI,IAAc,SAAW8E,EAAMxD,EAAc,IAAI,EAAIwD,EAAMmD,EAAW,KAAK,GAAKjI,IAAc,OAAS8E,EAAMxD,EAAc,MAAM,EAAIwD,EAAMmD,EAAW,GAAG,GAAKjI,IAAc,UAAY8E,EAAMxD,EAAc,GAAG,EAAIwD,EAAMmD,EAAW,MAAM,EAEvUE,EAAgBrD,EAAMxD,EAAc,IAAI,EAAIwD,EAAMrF,EAAW,IAAI,EACjE2I,EAAiBtD,EAAMxD,EAAc,KAAK,EAAIwD,EAAMrF,EAAW,KAAK,EACpE4I,EAAevD,EAAMxD,EAAc,GAAG,EAAIwD,EAAMrF,EAAW,GAAG,EAC9D6I,EAAkBxD,EAAMxD,EAAc,MAAM,EAAIwD,EAAMrF,EAAW,MAAM,EAEvE8I,EAAsBvI,IAAc,QAAUmI,GAAiBnI,IAAc,SAAWoI,GAAkBpI,IAAc,OAASqI,GAAgBrI,IAAc,UAAYsI,EAG3KnD,EAAa,CAAC,MAAO,QAAQ,EAAE,QAAQnF,CAAS,IAAM,GAGtDwI,EAAwB,CAAC,CAAC7E,EAAQ,iBAAmBwB,GAAc1E,IAAc,SAAW0H,GAAiBhD,GAAc1E,IAAc,OAAS2H,GAAkB,CAACjD,GAAc1E,IAAc,SAAW4H,GAAgB,CAAClD,GAAc1E,IAAc,OAAS6H,GAGlQG,GAA4B,CAAC,CAAC9E,EAAQ,0BAA4BwB,GAAc1E,IAAc,SAAW2H,GAAkBjD,GAAc1E,IAAc,OAAS0H,GAAiB,CAAChD,GAAc1E,IAAc,SAAW6H,GAAmB,CAACnD,GAAc1E,IAAc,OAAS4H,GAElRK,EAAmBF,GAAyBC,IAE5CP,GAAeK,GAAuBG,KAExCrG,EAAK,QAAU,IAEX6F,GAAeK,KACjBvI,EAAY+H,EAAUJ,EAAQ,CAAC,GAG7Be,IACFjI,EAAY6G,GAAqB7G,CAAS,GAG5C4B,EAAK,UAAYrC,GAAaS,EAAY,IAAMA,EAAY,IAI5D4B,EAAK,QAAQ,OAAShF,EAAS,CAAA,EAAIgF,EAAK,QAAQ,OAAQlB,GAAiBkB,EAAK,SAAS,OAAQA,EAAK,QAAQ,UAAWA,EAAK,SAAS,CAAC,EAEtIA,EAAOF,GAAaE,EAAK,SAAS,UAAWA,EAAM,MAAM,EAE/D,CAAG,EACMA,CACT,CASA,SAASsG,GAAatG,EAAM,CAC1B,IAAIuC,EAAgBvC,EAAK,QACrB/C,EAASsF,EAAc,OACvB/K,EAAY+K,EAAc,UAE1B5E,EAAYqC,EAAK,UAAU,MAAM,GAAG,EAAE,CAAC,EACvCyC,EAAQ,KAAK,MACbK,EAAa,CAAC,MAAO,QAAQ,EAAE,QAAQnF,CAAS,IAAM,GACtD7E,EAAOgK,EAAa,QAAU,SAC9B6B,EAAS7B,EAAa,OAAS,MAC/BzD,EAAcyD,EAAa,QAAU,SAEzC,OAAI7F,EAAOnE,CAAI,EAAI2J,EAAMjL,EAAUmN,CAAM,CAAC,IACxC3E,EAAK,QAAQ,OAAO2E,CAAM,EAAIlC,EAAMjL,EAAUmN,CAAM,CAAC,EAAI1H,EAAOoC,CAAW,GAEzEpC,EAAO0H,CAAM,EAAIlC,EAAMjL,EAAUsB,CAAI,CAAC,IACxCkH,EAAK,QAAQ,OAAO2E,CAAM,EAAIlC,EAAMjL,EAAUsB,CAAI,CAAC,GAG9CkH,CACT,CAcA,SAASuG,GAAQC,EAAKnH,EAAaJ,EAAeF,EAAkB,CAElE,IAAI0H,EAAQD,EAAI,MAAM,2BAA2B,EAC7CzL,EAAQ,CAAC0L,EAAM,CAAC,EAChB1E,EAAO0E,EAAM,CAAC,EAGlB,GAAI,CAAC1L,EACH,OAAOyL,EAGT,GAAIzE,EAAK,QAAQ,GAAG,IAAM,EAAG,CAC3B,IAAIlL,EAAU,OACd,OAAQkL,EAAI,CACV,IAAK,KACHlL,EAAUoI,EACV,MACF,IAAK,IACL,IAAK,KACL,QACEpI,EAAUkI,CAClB,CAEI,IAAI5F,EAAO+B,EAAcrE,CAAO,EAChC,OAAOsC,EAAKkG,CAAW,EAAI,IAAMtE,CAClC,SAAUgH,IAAS,MAAQA,IAAS,KAAM,CAEzC,IAAI2E,EAAO,OACX,OAAI3E,IAAS,KACX2E,EAAO,KAAK,IAAI,SAAS,gBAAgB,aAAc,OAAO,aAAe,CAAC,EAE9EA,EAAO,KAAK,IAAI,SAAS,gBAAgB,YAAa,OAAO,YAAc,CAAC,EAEvEA,EAAO,IAAM3L,CACxB,KAGI,QAAOA,CAEX,CAaA,SAAS4L,GAAYhK,EAAQsC,EAAeF,EAAkB6H,EAAe,CAC3E,IAAIzL,EAAU,CAAC,EAAG,CAAC,EAKf0L,EAAY,CAAC,QAAS,MAAM,EAAE,QAAQD,CAAa,IAAM,GAIzDE,EAAYnK,EAAO,MAAM,SAAS,EAAE,IAAI,SAAUoK,EAAM,CAC1D,OAAOA,EAAK,KAAM,CACtB,CAAG,EAIGC,EAAUF,EAAU,QAAQvH,EAAKuH,EAAW,SAAUC,EAAM,CAC9D,OAAOA,EAAK,OAAO,MAAM,IAAM,EACnC,CAAG,CAAC,EAEED,EAAUE,CAAO,GAAKF,EAAUE,CAAO,EAAE,QAAQ,GAAG,IAAM,IAC5D,QAAQ,KAAK,8EAA8E,EAK7F,IAAIC,EAAa,cACbC,EAAMF,IAAY,GAAK,CAACF,EAAU,MAAM,EAAGE,CAAO,EAAE,OAAO,CAACF,EAAUE,CAAO,EAAE,MAAMC,CAAU,EAAE,CAAC,CAAC,CAAC,EAAG,CAACH,EAAUE,CAAO,EAAE,MAAMC,CAAU,EAAE,CAAC,CAAC,EAAE,OAAOH,EAAU,MAAME,EAAU,CAAC,CAAC,CAAC,EAAI,CAACF,CAAS,EAGvM,OAAAI,EAAMA,EAAI,IAAI,SAAUC,EAAI7B,EAAO,CAEjC,IAAIjG,GAAeiG,IAAU,EAAI,CAACuB,EAAYA,GAAa,SAAW,QAClEO,EAAoB,GACxB,OAAOD,EAGN,OAAO,SAAUpJ,EAAGC,EAAG,CACtB,OAAID,EAAEA,EAAE,OAAS,CAAC,IAAM,IAAM,CAAC,IAAK,GAAG,EAAE,QAAQC,CAAC,IAAM,IACtDD,EAAEA,EAAE,OAAS,CAAC,EAAIC,EAClBoJ,EAAoB,GACbrJ,GACEqJ,GACTrJ,EAAEA,EAAE,OAAS,CAAC,GAAKC,EACnBoJ,EAAoB,GACbrJ,GAEAA,EAAE,OAAOC,CAAC,CAEzB,EAAO,CAAE,CAAA,EAEJ,IAAI,SAAUwI,EAAK,CAClB,OAAOD,GAAQC,EAAKnH,EAAaJ,EAAeF,CAAgB,CACtE,CAAK,CACL,CAAG,EAGDmI,EAAI,QAAQ,SAAUC,EAAI7B,EAAO,CAC/B6B,EAAG,QAAQ,SAAUJ,EAAMM,EAAQ,CAC7BzF,EAAUmF,CAAI,IAChB5L,EAAQmK,CAAK,GAAKyB,GAAQI,EAAGE,EAAS,CAAC,IAAM,IAAM,GAAK,GAEhE,CAAK,CACL,CAAG,EACMlM,CACT,CAWA,SAASwB,GAAOqD,EAAMvC,EAAM,CAC1B,IAAId,EAASc,EAAK,OACdE,EAAYqC,EAAK,UACjBuC,EAAgBvC,EAAK,QACrB/C,EAASsF,EAAc,OACvB/K,EAAY+K,EAAc,UAE1BqE,EAAgBjJ,EAAU,MAAM,GAAG,EAAE,CAAC,EAEtCxC,EAAU,OACd,OAAIyG,EAAU,CAACjF,CAAM,EACnBxB,EAAU,CAAC,CAACwB,EAAQ,CAAC,EAErBxB,EAAUwL,GAAYhK,EAAQM,EAAQzF,EAAWoP,CAAa,EAG5DA,IAAkB,QACpB3J,EAAO,KAAO9B,EAAQ,CAAC,EACvB8B,EAAO,MAAQ9B,EAAQ,CAAC,GACfyL,IAAkB,SAC3B3J,EAAO,KAAO9B,EAAQ,CAAC,EACvB8B,EAAO,MAAQ9B,EAAQ,CAAC,GACfyL,IAAkB,OAC3B3J,EAAO,MAAQ9B,EAAQ,CAAC,EACxB8B,EAAO,KAAO9B,EAAQ,CAAC,GACdyL,IAAkB,WAC3B3J,EAAO,MAAQ9B,EAAQ,CAAC,EACxB8B,EAAO,KAAO9B,EAAQ,CAAC,GAGzB6E,EAAK,OAAS/C,EACP+C,CACT,CASA,SAASsH,GAAgBtH,EAAMsB,EAAS,CACtC,IAAInE,EAAoBmE,EAAQ,mBAAqBzJ,EAAgBmI,EAAK,SAAS,MAAM,EAKrFA,EAAK,SAAS,YAAc7C,IAC9BA,EAAoBtF,EAAgBsF,CAAiB,GAMvD,IAAIoK,EAAgB/G,EAAyB,WAAW,EACpDgH,EAAexH,EAAK,SAAS,OAAO,MACpC2D,EAAM6D,EAAa,IACnB9D,EAAO8D,EAAa,KACpBC,EAAYD,EAAaD,CAAa,EAE1CC,EAAa,IAAM,GACnBA,EAAa,KAAO,GACpBA,EAAaD,CAAa,EAAI,GAE9B,IAAInK,EAAaJ,EAAcgD,EAAK,SAAS,OAAQA,EAAK,SAAS,UAAWsB,EAAQ,QAASnE,EAAmB6C,EAAK,aAAa,EAIpIwH,EAAa,IAAM7D,EACnB6D,EAAa,KAAO9D,EACpB8D,EAAaD,CAAa,EAAIE,EAE9BnG,EAAQ,WAAalE,EAErB,IAAI7E,EAAQ+I,EAAQ,SAChBrE,EAAS+C,EAAK,QAAQ,OAEtBP,EAAQ,CACV,QAAS,SAAiB9B,EAAW,CACnC,IAAI5C,EAAQkC,EAAOU,CAAS,EAC5B,OAAIV,EAAOU,CAAS,EAAIP,EAAWO,CAAS,GAAK,CAAC2D,EAAQ,sBACxDvG,EAAQ,KAAK,IAAIkC,EAAOU,CAAS,EAAGP,EAAWO,CAAS,CAAC,GAEpD/C,EAAe,CAAA,EAAI+C,EAAW5C,CAAK,CAC3C,EACD,UAAW,SAAmB4C,EAAW,CACvC,IAAIwB,EAAWxB,IAAc,QAAU,OAAS,MAC5C5C,EAAQkC,EAAOkC,CAAQ,EAC3B,OAAIlC,EAAOU,CAAS,EAAIP,EAAWO,CAAS,GAAK,CAAC2D,EAAQ,sBACxDvG,EAAQ,KAAK,IAAIkC,EAAOkC,CAAQ,EAAG/B,EAAWO,CAAS,GAAKA,IAAc,QAAUV,EAAO,MAAQA,EAAO,OAAO,GAE5GrC,EAAe,CAAA,EAAIuE,EAAUpE,CAAK,CAC/C,CACG,EAED,OAAAxC,EAAM,QAAQ,SAAUoF,EAAW,CACjC,IAAI7E,EAAO,CAAC,OAAQ,KAAK,EAAE,QAAQ6E,CAAS,IAAM,GAAK,UAAY,YACnEV,EAASjC,EAAS,GAAIiC,EAAQwC,EAAM3G,CAAI,EAAE6E,CAAS,CAAC,CACxD,CAAG,EAEDqC,EAAK,QAAQ,OAAS/C,EAEf+C,CACT,CASA,SAAS0H,GAAM1H,EAAM,CACnB,IAAIrC,EAAYqC,EAAK,UACjB4G,EAAgBjJ,EAAU,MAAM,GAAG,EAAE,CAAC,EACtCgK,EAAiBhK,EAAU,MAAM,GAAG,EAAE,CAAC,EAG3C,GAAIgK,EAAgB,CAClB,IAAIpF,EAAgBvC,EAAK,QACrBxI,EAAY+K,EAAc,UAC1BtF,EAASsF,EAAc,OAEvBO,EAAa,CAAC,SAAU,KAAK,EAAE,QAAQ8D,CAAa,IAAM,GAC1D9N,EAAOgK,EAAa,OAAS,MAC7BzD,EAAcyD,EAAa,QAAU,SAErC8E,EAAe,CACjB,MAAOhN,EAAe,CAAE,EAAE9B,EAAMtB,EAAUsB,CAAI,CAAC,EAC/C,IAAK8B,EAAe,GAAI9B,EAAMtB,EAAUsB,CAAI,EAAItB,EAAU6H,CAAW,EAAIpC,EAAOoC,CAAW,CAAC,CAC7F,EAEDW,EAAK,QAAQ,OAAShF,EAAS,CAAE,EAAEiC,EAAQ2K,EAAaD,CAAc,CAAC,CAC3E,CAEE,OAAO3H,CACT,CASA,SAAS6H,GAAK7H,EAAM,CAClB,GAAI,CAAC8D,GAAmB9D,EAAK,SAAS,UAAW,OAAQ,iBAAiB,EACxE,OAAOA,EAGT,IAAIpC,EAAUoC,EAAK,QAAQ,UACvB8H,EAAQvI,EAAKS,EAAK,SAAS,UAAW,SAAUzG,EAAU,CAC5D,OAAOA,EAAS,OAAS,iBAC1B,CAAA,EAAE,WAEH,GAAIqE,EAAQ,OAASkK,EAAM,KAAOlK,EAAQ,KAAOkK,EAAM,OAASlK,EAAQ,IAAMkK,EAAM,QAAUlK,EAAQ,MAAQkK,EAAM,KAAM,CAExH,GAAI9H,EAAK,OAAS,GAChB,OAAOA,EAGTA,EAAK,KAAO,GACZA,EAAK,WAAW,qBAAqB,EAAI,EAC7C,KAAS,CAEL,GAAIA,EAAK,OAAS,GAChB,OAAOA,EAGTA,EAAK,KAAO,GACZA,EAAK,WAAW,qBAAqB,EAAI,EAC7C,CAEE,OAAOA,CACT,CASA,SAAS+H,GAAM/H,EAAM,CACnB,IAAIrC,EAAYqC,EAAK,UACjB4G,EAAgBjJ,EAAU,MAAM,GAAG,EAAE,CAAC,EACtC4E,EAAgBvC,EAAK,QACrB/C,EAASsF,EAAc,OACvB/K,EAAY+K,EAAc,UAE1BrD,EAAU,CAAC,OAAQ,OAAO,EAAE,QAAQ0H,CAAa,IAAM,GAEvDoB,EAAiB,CAAC,MAAO,MAAM,EAAE,QAAQpB,CAAa,IAAM,GAEhE,OAAA3J,EAAOiC,EAAU,OAAS,KAAK,EAAI1H,EAAUoP,CAAa,GAAKoB,EAAiB/K,EAAOiC,EAAU,QAAU,QAAQ,EAAI,GAEvHc,EAAK,UAAYrB,EAAqBhB,CAAS,EAC/CqC,EAAK,QAAQ,OAAS9E,EAAc+B,CAAM,EAEnC+C,CACT,CAuBA,IAAID,GAAY,CASd,MAAO,CAEL,MAAO,IAEP,QAAS,GAET,GAAI2H,EACL,EAwCD,OAAQ,CAEN,MAAO,IAEP,QAAS,GAET,GAAI/K,GAIJ,OAAQ,CACT,EAmBD,gBAAiB,CAEf,MAAO,IAEP,QAAS,GAET,GAAI2K,GAMJ,SAAU,CAAC,OAAQ,QAAS,MAAO,QAAQ,EAO3C,QAAS,EAMT,kBAAmB,cACpB,EAWD,aAAc,CAEZ,MAAO,IAEP,QAAS,GAET,GAAIhB,EACL,EAYD,MAAO,CAEL,MAAO,IAEP,QAAS,GAET,GAAIjC,GAEJ,QAAS,WACV,EAaD,KAAM,CAEJ,MAAO,IAEP,QAAS,GAET,GAAImB,GAOJ,SAAU,OAKV,QAAS,EAOT,kBAAmB,WAQnB,eAAgB,GAQhB,wBAAyB,EAC1B,EASD,MAAO,CAEL,MAAO,IAEP,QAAS,GAET,GAAIuC,EACL,EAYD,KAAM,CAEJ,MAAO,IAEP,QAAS,GAET,GAAIF,EACL,EAiBD,aAAc,CAEZ,MAAO,IAEP,QAAS,GAET,GAAIxE,GAMJ,gBAAiB,GAMjB,EAAG,SAMH,EAAG,OACJ,EAiBD,WAAY,CAEV,MAAO,IAEP,QAAS,GAET,GAAInB,GAEJ,OAAQC,GAOR,gBAAiB,MACrB,CACA,EAqCI8F,GAAW,CAKb,UAAW,SAMX,cAAe,GAMf,cAAe,GAOf,gBAAiB,GAQjB,SAAU,UAAoB,CAAE,EAUhC,SAAU,UAAoB,CAAE,EAOhC,UAAWlI,EACb,EAcImI,EAAS,UAAY,CASvB,SAASA,EAAO1Q,EAAWyF,EAAQ,CACjC,IAAIkL,EAAQ,KAER7G,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAE,EACpFpH,GAAe,KAAMgO,CAAM,EAE3B,KAAK,eAAiB,UAAY,CAChC,OAAO,sBAAsBC,EAAM,MAAM,CAC1C,EAGD,KAAK,OAAS3R,GAAS,KAAK,OAAO,KAAK,IAAI,CAAC,EAG7C,KAAK,QAAUwE,EAAS,CAAE,EAAEkN,EAAO,SAAU5G,CAAO,EAGpD,KAAK,MAAQ,CACX,YAAa,GACb,UAAW,GACX,cAAe,CAAA,CAChB,EAGD,KAAK,UAAY9J,GAAaA,EAAU,OAASA,EAAU,CAAC,EAAIA,EAChE,KAAK,OAASyF,GAAUA,EAAO,OAASA,EAAO,CAAC,EAAIA,EAGpD,KAAK,QAAQ,UAAY,CAAE,EAC3B,OAAO,KAAKjC,EAAS,CAAE,EAAEkN,EAAO,SAAS,UAAW5G,EAAQ,SAAS,CAAC,EAAE,QAAQ,SAAUhB,EAAM,CAC9F6H,EAAM,QAAQ,UAAU7H,CAAI,EAAItF,EAAS,CAAE,EAAEkN,EAAO,SAAS,UAAU5H,CAAI,GAAK,CAAA,EAAIgB,EAAQ,UAAYA,EAAQ,UAAUhB,CAAI,EAAI,EAAE,CAC1I,CAAK,EAGD,KAAK,UAAY,OAAO,KAAK,KAAK,QAAQ,SAAS,EAAE,IAAI,SAAUA,EAAM,CACvE,OAAOtF,EAAS,CACd,KAAMsF,CACP,EAAE6H,EAAM,QAAQ,UAAU7H,CAAI,CAAC,CACjC,CAAA,EAEA,KAAK,SAAUvC,EAAGC,EAAG,CACpB,OAAOD,EAAE,MAAQC,EAAE,KACzB,CAAK,EAMD,KAAK,UAAU,QAAQ,SAAUoE,EAAiB,CAC5CA,EAAgB,SAAW3L,EAAW2L,EAAgB,MAAM,GAC9DA,EAAgB,OAAO+F,EAAM,UAAWA,EAAM,OAAQA,EAAM,QAAS/F,EAAiB+F,EAAM,KAAK,CAEzG,CAAK,EAGD,KAAK,OAAQ,EAEb,IAAIC,EAAgB,KAAK,QAAQ,cAC7BA,GAEF,KAAK,qBAAsB,EAG7B,KAAK,MAAM,cAAgBA,CAC/B,CAME,OAAA/N,GAAY6N,EAAQ,CAAC,CACnB,IAAK,SACL,MAAO,UAAqB,CAC1B,OAAO/H,GAAO,KAAK,IAAI,CAC7B,CACA,EAAK,CACD,IAAK,UACL,MAAO,UAAsB,CAC3B,OAAOU,GAAQ,KAAK,IAAI,CAC9B,CACA,EAAK,CACD,IAAK,uBACL,MAAO,UAAmC,CACxC,OAAOY,GAAqB,KAAK,IAAI,CAC3C,CACA,EAAK,CACD,IAAK,wBACL,MAAO,UAAoC,CACzC,OAAOE,GAAsB,KAAK,IAAI,CAC5C,CA0BA,CAAG,CAAC,EACKuG,CACT,EAAC,EAuBDA,EAAO,OAAS,OAAO,QAAW,YAAc,OAAS,QAAQ,YACjEA,EAAO,WAAahD,GACpBgD,EAAO,SAAWD", "x_google_ignoreList": [0]}