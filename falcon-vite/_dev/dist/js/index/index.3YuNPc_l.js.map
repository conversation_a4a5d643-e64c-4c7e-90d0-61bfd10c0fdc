{"version": 3, "file": "index.3YuNPc_l.js", "sources": ["../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/enums.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/math.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/userAgent.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/within.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getVariation.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/flip.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/hide.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/offset.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/debounce.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/createPopper.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/popper-lite.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/popper.js"], "sourcesContent": ["export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "export default function getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}", "import getUAString from \"../utils/userAgent.js\";\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}", "import { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nimport getWindow from \"./getWindow.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = isElement(element) ? getWindow(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getUAString from \"../utils/userAgent.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}\nexport function withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport { within } from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref, win) {\n  var x = _ref.x,\n      y = _ref.y;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }, getWindow(popper)) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$strategy = _options.strategy,\n      strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport { within, withinMaxClamp } from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { min as mathMin, max as mathMax } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = offset + overflow[mainSide];\n    var max = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport { round } from \"../utils/math.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        });\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref) {\n        var name = _ref.name,\n            _ref$options = _ref.options,\n            options = _ref$options === void 0 ? {} : _ref$options,\n            effect = _ref.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";"], "names": ["top", "bottom", "right", "left", "auto", "basePlacements", "start", "end", "clippingParents", "viewport", "popper", "reference", "variationPlacements", "acc", "placement", "placements", "beforeRead", "read", "afterRead", "<PERSON><PERSON><PERSON>", "main", "<PERSON><PERSON><PERSON>", "beforeWrite", "write", "afterWrite", "modifierPhases", "getNodeName", "element", "getWindow", "node", "ownerDocument", "isElement", "OwnElement", "isHTMLElement", "isShadowRoot", "applyStyles", "_ref", "state", "name", "style", "attributes", "value", "effect", "_ref2", "initialStyles", "styleProperties", "property", "attribute", "applyStyles$1", "getBasePlacement", "max", "min", "round", "getUAString", "uaData", "item", "isLayoutViewport", "getBoundingClientRect", "includeScale", "isFixedStrategy", "clientRect", "scaleX", "scaleY", "visualViewport", "addVisualOffsets", "x", "y", "width", "height", "getLayoutRect", "contains", "parent", "child", "rootNode", "next", "getComputedStyle", "isTableElement", "getDocumentElement", "getParentNode", "getTrueOffsetParent", "getContainingBlock", "isFirefox", "isIE", "elementCss", "currentNode", "css", "getOffsetParent", "window", "offsetParent", "getMainAxisFromPlacement", "within", "mathMax", "mathMin", "withinMaxClamp", "v", "getFreshSideObject", "mergePaddingObject", "paddingObject", "expandToHashMap", "keys", "hashMap", "key", "toPaddingObject", "padding", "arrow", "_state$modifiersData$", "options", "arrowElement", "popperOffsets", "basePlacement", "axis", "isVertical", "len", "arrowRect", "minProp", "maxProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "centerToReference", "center", "offset", "axisProp", "_options$element", "arrow$1", "getVariation", "unsetSides", "roundOffsetsByDPR", "win", "dpr", "mapToStyles", "_Object$assign2", "popperRect", "variation", "offsets", "position", "gpuAcceleration", "adaptive", "roundOffsets", "isFixed", "_offsets$x", "_offsets$y", "_ref3", "hasX", "hasY", "sideX", "sideY", "heightProp", "widthProp", "offsetY", "offsetX", "commonStyles", "_ref4", "_Object$assign", "computeStyles", "_ref5", "_options$gpuAccelerat", "_options$adaptive", "_options$roundOffsets", "computeStyles$1", "passive", "instance", "_options$scroll", "scroll", "_options$resize", "resize", "scrollParents", "scrollParent", "eventListeners", "hash", "getOppositePlacement", "matched", "getOppositeVariationPlacement", "getWindowScroll", "scrollLeft", "scrollTop", "getWindowScrollBarX", "getViewportRect", "strategy", "html", "layoutViewport", "getDocumentRect", "_element$ownerDocumen", "winScroll", "body", "isScrollParent", "_getComputedStyle", "overflow", "overflowX", "overflowY", "getScrollParent", "listScrollParents", "list", "isBody", "target", "updatedList", "rectToClientRect", "rect", "getInnerBoundingClientRect", "getClientRectFromMixedType", "clippingParent", "getClippingParents", "canEscapeClipping", "clipperElement", "getClippingRect", "boundary", "rootBoundary", "mainClippingParents", "firstClippingParent", "clippingRect", "accRect", "computeOffsets", "commonX", "commonY", "mainAxis", "detectOverflow", "_options", "_options$placement", "_options$strategy", "_options$boundary", "_options$rootBoundary", "_options$elementConte", "elementContext", "_options$altBoundary", "altBoundary", "_options$padding", "altContext", "clippingClientRect", "referenceClientRect", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "multiply", "computeAutoPlacement", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "a", "b", "getExpandedFallbackPlacements", "oppositePlacement", "flip", "_options$mainAxis", "checkMainAxis", "_options$altAxis", "checkAltAxis", "specifiedFallbackPlacements", "_options$flipVariatio", "preferredPlacement", "isBasePlacement", "fallbackPlacements", "referenceRect", "checksMap", "makeFallbackChecks", "firstFittingPlacement", "i", "_basePlacement", "isStartVariation", "mainVariationSide", "altVariationSide", "checks", "check", "numberOfChecks", "_loop", "_i", "fittingPlacement", "_ret", "flip$1", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "side", "hide", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "hide$1", "distanceAndSkiddingToXY", "rects", "invertDistance", "skidding", "distance", "_options$offset", "data", "_data$state$placement", "offset$1", "popperOffsets$1", "getAltAxis", "preventOverflow", "_options$tether", "tether", "_options$tetherOffset", "tetherOffset", "altAxis", "tetherOffsetValue", "normalizedTetherOffsetValue", "offsetModifierState", "_offsetModifierState$", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "clientOffset", "offsetModifierValue", "tetherMin", "tetherMax", "preventedOffset", "_offsetModifierState$2", "_mainSide", "_altSide", "_offset", "_len", "_min", "_max", "isOriginSide", "_offsetModifierValue", "_tetherMin", "_tetherMax", "_preventedOffset", "preventOverflow$1", "getHTMLElementScroll", "getNodeScroll", "isElementScaled", "getCompositeRect", "elementOrVirtualElement", "isOffsetParentAnElement", "offsetParentIsScaled", "documentElement", "order", "modifiers", "map", "visited", "result", "modifier", "sort", "requires", "dep", "depModifier", "orderModifiers", "orderedModifiers", "phase", "debounce", "fn", "pending", "resolve", "mergeByName", "merged", "current", "existing", "DEFAULT_OPTIONS", "areValidElements", "args", "_key", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "effectCleanupFns", "isDestroyed", "setOptionsAction", "cleanupModifierEffects", "m", "runModifierEffects", "_state$elements", "index", "_state$orderedModifie", "_state$orderedModifie2", "_ref$options", "cleanupFn", "noopFn", "createPopper"], "mappings": "AAAO,IAAIA,EAAM,MACNC,EAAS,SACTC,EAAQ,QACRC,EAAO,OACPC,GAAO,OACPC,GAAiB,CAACL,EAAKC,EAAQC,EAAOC,CAAI,EAC1CG,EAAQ,QACRC,EAAM,MACNC,GAAkB,kBAClBC,GAAW,WACXC,EAAS,SACTC,GAAY,YACZC,GAAmCP,GAAe,OAAO,SAAUQ,EAAKC,EAAW,CAC5F,OAAOD,EAAI,OAAO,CAACC,EAAY,IAAMR,EAAOQ,EAAY,IAAMP,CAAG,CAAC,CACpE,EAAG,EAAE,EACMQ,GAA0B,CAAA,EAAG,OAAOV,GAAgB,CAACD,EAAI,CAAC,EAAE,OAAO,SAAUS,EAAKC,EAAW,CACtG,OAAOD,EAAI,OAAO,CAACC,EAAWA,EAAY,IAAMR,EAAOQ,EAAY,IAAMP,CAAG,CAAC,CAC/E,EAAG,CAAE,CAAA,EAEMS,GAAa,aACbC,GAAO,OACPC,GAAY,YAEZC,GAAa,aACbC,GAAO,OACPC,GAAY,YAEZC,GAAc,cACdC,GAAQ,QACRC,GAAa,aACbC,GAAiB,CAACT,GAAYC,GAAMC,GAAWC,GAAYC,GAAMC,GAAWC,GAAaC,GAAOC,EAAU,EC9BtG,SAASE,EAAYC,EAAS,CAC3C,OAAOA,GAAWA,EAAQ,UAAY,IAAI,YAAW,EAAK,IAC5D,CCFe,SAASC,EAAUC,EAAM,CACtC,GAAIA,GAAQ,KACV,OAAO,OAGT,GAAIA,EAAK,SAAU,IAAK,kBAAmB,CACzC,IAAIC,EAAgBD,EAAK,cACzB,OAAOC,GAAgBA,EAAc,aAAe,MACxD,CAEE,OAAOD,CACT,CCTA,SAASE,EAAUF,EAAM,CACvB,IAAIG,EAAaJ,EAAUC,CAAI,EAAE,QACjC,OAAOA,aAAgBG,GAAcH,aAAgB,OACvD,CAEA,SAASI,EAAcJ,EAAM,CAC3B,IAAIG,EAAaJ,EAAUC,CAAI,EAAE,YACjC,OAAOA,aAAgBG,GAAcH,aAAgB,WACvD,CAEA,SAASK,GAAaL,EAAM,CAE1B,GAAI,OAAO,YAAe,YACxB,MAAO,GAGT,IAAIG,EAAaJ,EAAUC,CAAI,EAAE,WACjC,OAAOA,aAAgBG,GAAcH,aAAgB,UACvD,CChBA,SAASM,GAAYC,EAAM,CACzB,IAAIC,EAAQD,EAAK,MACjB,OAAO,KAAKC,EAAM,QAAQ,EAAE,QAAQ,SAAUC,EAAM,CAClD,IAAIC,EAAQF,EAAM,OAAOC,CAAI,GAAK,CAAE,EAChCE,EAAaH,EAAM,WAAWC,CAAI,GAAK,CAAE,EACzCX,EAAUU,EAAM,SAASC,CAAI,EAE7B,CAACL,EAAcN,CAAO,GAAK,CAACD,EAAYC,CAAO,IAOnD,OAAO,OAAOA,EAAQ,MAAOY,CAAK,EAClC,OAAO,KAAKC,CAAU,EAAE,QAAQ,SAAUF,EAAM,CAC9C,IAAIG,EAAQD,EAAWF,CAAI,EAEvBG,IAAU,GACZd,EAAQ,gBAAgBW,CAAI,EAE5BX,EAAQ,aAAaW,EAAMG,IAAU,GAAO,GAAKA,CAAK,CAE9D,CAAK,EACL,CAAG,CACH,CAEA,SAASC,GAAOC,EAAO,CACrB,IAAIN,EAAQM,EAAM,MACdC,EAAgB,CAClB,OAAQ,CACN,SAAUP,EAAM,QAAQ,SACxB,KAAM,IACN,IAAK,IACL,OAAQ,GACT,EACD,MAAO,CACL,SAAU,UACX,EACD,UAAW,CAAA,CACZ,EACD,cAAO,OAAOA,EAAM,SAAS,OAAO,MAAOO,EAAc,MAAM,EAC/DP,EAAM,OAASO,EAEXP,EAAM,SAAS,OACjB,OAAO,OAAOA,EAAM,SAAS,MAAM,MAAOO,EAAc,KAAK,EAGxD,UAAY,CACjB,OAAO,KAAKP,EAAM,QAAQ,EAAE,QAAQ,SAAUC,EAAM,CAClD,IAAIX,EAAUU,EAAM,SAASC,CAAI,EAC7BE,EAAaH,EAAM,WAAWC,CAAI,GAAK,CAAE,EACzCO,EAAkB,OAAO,KAAKR,EAAM,OAAO,eAAeC,CAAI,EAAID,EAAM,OAAOC,CAAI,EAAIM,EAAcN,CAAI,CAAC,EAE1GC,EAAQM,EAAgB,OAAO,SAAUN,EAAOO,EAAU,CAC5D,OAAAP,EAAMO,CAAQ,EAAI,GACXP,CACR,EAAE,CAAE,CAAA,EAED,CAACN,EAAcN,CAAO,GAAK,CAACD,EAAYC,CAAO,IAInD,OAAO,OAAOA,EAAQ,MAAOY,CAAK,EAClC,OAAO,KAAKC,CAAU,EAAE,QAAQ,SAAUO,EAAW,CACnDpB,EAAQ,gBAAgBoB,CAAS,CACzC,CAAO,EACP,CAAK,CACF,CACH,CAGA,MAAeC,GAAA,CACb,KAAM,cACN,QAAS,GACT,MAAO,QACP,GAAIb,GACJ,OAAQO,GACR,SAAU,CAAC,eAAe,CAC5B,EClFe,SAASO,EAAiBnC,EAAW,CAClD,OAAOA,EAAU,MAAM,GAAG,EAAE,CAAC,CAC/B,CCHO,IAAIoC,EAAM,KAAK,IACXC,GAAM,KAAK,IACXC,GAAQ,KAAK,MCFT,SAASC,IAAc,CACpC,IAAIC,EAAS,UAAU,cAEvB,OAAIA,GAAU,MAAQA,EAAO,QAAU,MAAM,QAAQA,EAAO,MAAM,EACzDA,EAAO,OAAO,IAAI,SAAUC,EAAM,CACvC,OAAOA,EAAK,MAAQ,IAAMA,EAAK,OACrC,CAAK,EAAE,KAAK,GAAG,EAGN,UAAU,SACnB,CCTe,SAASC,IAAmB,CACzC,MAAO,CAAC,iCAAiC,KAAKH,IAAa,CAC7D,CCCe,SAASI,GAAsB9B,EAAS+B,EAAcC,EAAiB,CAChFD,IAAiB,SACnBA,EAAe,IAGbC,IAAoB,SACtBA,EAAkB,IAGpB,IAAIC,EAAajC,EAAQ,sBAAuB,EAC5CkC,EAAS,EACTC,EAAS,EAETJ,GAAgBzB,EAAcN,CAAO,IACvCkC,EAASlC,EAAQ,YAAc,GAAIyB,GAAMQ,EAAW,KAAK,EAAIjC,EAAQ,aAAe,EACpFmC,EAASnC,EAAQ,aAAe,GAAIyB,GAAMQ,EAAW,MAAM,EAAIjC,EAAQ,cAAgB,GAGzF,IAAIS,EAAOL,EAAUJ,CAAO,EAAIC,EAAUD,CAAO,EAAI,OACjDoC,EAAiB3B,EAAK,eAEtB4B,EAAmB,CAACR,GAAgB,GAAMG,EAC1CM,GAAKL,EAAW,MAAQI,GAAoBD,EAAiBA,EAAe,WAAa,IAAMF,EAC/FK,GAAKN,EAAW,KAAOI,GAAoBD,EAAiBA,EAAe,UAAY,IAAMD,EAC7FK,EAAQP,EAAW,MAAQC,EAC3BO,EAASR,EAAW,OAASE,EACjC,MAAO,CACL,MAAOK,EACP,OAAQC,EACR,IAAKF,EACL,MAAOD,EAAIE,EACX,OAAQD,EAAIE,EACZ,KAAMH,EACN,EAAGA,EACH,EAAGC,CACJ,CACH,CCrCe,SAASG,GAAc1C,EAAS,CAC7C,IAAIiC,EAAaH,GAAsB9B,CAAO,EAG1CwC,EAAQxC,EAAQ,YAChByC,EAASzC,EAAQ,aAErB,OAAI,KAAK,IAAIiC,EAAW,MAAQO,CAAK,GAAK,IACxCA,EAAQP,EAAW,OAGjB,KAAK,IAAIA,EAAW,OAASQ,CAAM,GAAK,IAC1CA,EAASR,EAAW,QAGf,CACL,EAAGjC,EAAQ,WACX,EAAGA,EAAQ,UACX,MAAOwC,EACP,OAAQC,CACT,CACH,CCvBe,SAASE,GAASC,EAAQC,EAAO,CAC9C,IAAIC,EAAWD,EAAM,aAAeA,EAAM,YAAW,EAErD,GAAID,EAAO,SAASC,CAAK,EACvB,MAAO,GAEJ,GAAIC,GAAYvC,GAAauC,CAAQ,EAAG,CACzC,IAAIC,EAAOF,EAEX,EAAG,CACD,GAAIE,GAAQH,EAAO,WAAWG,CAAI,EAChC,MAAO,GAITA,EAAOA,EAAK,YAAcA,EAAK,IACvC,OAAeA,EACV,CAGH,MAAO,EACT,CCrBe,SAASC,EAAiBhD,EAAS,CAChD,OAAOC,EAAUD,CAAO,EAAE,iBAAiBA,CAAO,CACpD,CCFe,SAASiD,GAAejD,EAAS,CAC9C,MAAO,CAAC,QAAS,KAAM,IAAI,EAAE,QAAQD,EAAYC,CAAO,CAAC,GAAK,CAChE,CCFe,SAASkD,EAAmBlD,EAAS,CAElD,QAASI,EAAUJ,CAAO,EAAIA,EAAQ,cACtCA,EAAQ,WAAa,OAAO,UAAU,eACxC,CCFe,SAASmD,GAAcnD,EAAS,CAC7C,OAAID,EAAYC,CAAO,IAAM,OACpBA,EAMPA,EAAQ,cACRA,EAAQ,aACRO,GAAaP,CAAO,EAAIA,EAAQ,KAAO,OAEvCkD,EAAmBlD,CAAO,CAG9B,CCVA,SAASoD,GAAoBpD,EAAS,CACpC,MAAI,CAACM,EAAcN,CAAO,GAC1BgD,EAAiBhD,CAAO,EAAE,WAAa,QAC9B,KAGFA,EAAQ,YACjB,CAIA,SAASqD,GAAmBrD,EAAS,CACnC,IAAIsD,EAAY,WAAW,KAAK5B,GAAW,CAAE,EACzC6B,EAAO,WAAW,KAAK7B,GAAW,CAAE,EAExC,GAAI6B,GAAQjD,EAAcN,CAAO,EAAG,CAElC,IAAIwD,EAAaR,EAAiBhD,CAAO,EAEzC,GAAIwD,EAAW,WAAa,QAC1B,OAAO,IAEb,CAEE,IAAIC,EAAcN,GAAcnD,CAAO,EAMvC,IAJIO,GAAakD,CAAW,IAC1BA,EAAcA,EAAY,MAGrBnD,EAAcmD,CAAW,GAAK,CAAC,OAAQ,MAAM,EAAE,QAAQ1D,EAAY0D,CAAW,CAAC,EAAI,GAAG,CAC3F,IAAIC,EAAMV,EAAiBS,CAAW,EAItC,GAAIC,EAAI,YAAc,QAAUA,EAAI,cAAgB,QAAUA,EAAI,UAAY,SAAW,CAAC,YAAa,aAAa,EAAE,QAAQA,EAAI,UAAU,IAAM,IAAMJ,GAAaI,EAAI,aAAe,UAAYJ,GAAaI,EAAI,QAAUA,EAAI,SAAW,OAC5O,OAAOD,EAEPA,EAAcA,EAAY,UAEhC,CAEE,OAAO,IACT,CAIe,SAASE,GAAgB3D,EAAS,CAI/C,QAHI4D,EAAS3D,EAAUD,CAAO,EAC1B6D,EAAeT,GAAoBpD,CAAO,EAEvC6D,GAAgBZ,GAAeY,CAAY,GAAKb,EAAiBa,CAAY,EAAE,WAAa,UACjGA,EAAeT,GAAoBS,CAAY,EAGjD,OAAIA,IAAiB9D,EAAY8D,CAAY,IAAM,QAAU9D,EAAY8D,CAAY,IAAM,QAAUb,EAAiBa,CAAY,EAAE,WAAa,UACxID,EAGFC,GAAgBR,GAAmBrD,CAAO,GAAK4D,CACxD,CCpEe,SAASE,GAAyB3E,EAAW,CAC1D,MAAO,CAAC,MAAO,QAAQ,EAAE,QAAQA,CAAS,GAAK,EAAI,IAAM,GAC3D,CCDO,SAAS4E,GAAOvC,EAAKV,EAAOS,EAAK,CACtC,OAAOyC,EAAQxC,EAAKyC,GAAQnD,EAAOS,CAAG,CAAC,CACzC,CACO,SAAS2C,GAAe1C,EAAKV,EAAOS,EAAK,CAC9C,IAAI4C,EAAIJ,GAAOvC,EAAKV,EAAOS,CAAG,EAC9B,OAAO4C,EAAI5C,EAAMA,EAAM4C,CACzB,CCPe,SAASC,IAAqB,CAC3C,MAAO,CACL,IAAK,EACL,MAAO,EACP,OAAQ,EACR,KAAM,CACP,CACH,CCNe,SAASC,GAAmBC,EAAe,CACxD,OAAO,OAAO,OAAO,CAAE,EAAEF,GAAkB,EAAIE,CAAa,CAC9D,CCHe,SAASC,GAAgBzD,EAAO0D,EAAM,CACnD,OAAOA,EAAK,OAAO,SAAUC,EAASC,EAAK,CACzC,OAAAD,EAAQC,CAAG,EAAI5D,EACR2D,CACR,EAAE,EAAE,CACP,CCKA,IAAIE,GAAkB,SAAyBC,EAASlE,EAAO,CAC7D,OAAAkE,EAAU,OAAOA,GAAY,WAAaA,EAAQ,OAAO,OAAO,CAAA,EAAIlE,EAAM,MAAO,CAC/E,UAAWA,EAAM,SAClB,CAAA,CAAC,EAAIkE,EACCP,GAAmB,OAAOO,GAAY,SAAWA,EAAUL,GAAgBK,EAASlG,EAAc,CAAC,CAC5G,EAEA,SAASmG,GAAMpE,EAAM,CACnB,IAAIqE,EAEApE,EAAQD,EAAK,MACbE,EAAOF,EAAK,KACZsE,EAAUtE,EAAK,QACfuE,EAAetE,EAAM,SAAS,MAC9BuE,EAAgBvE,EAAM,cAAc,cACpCwE,EAAgB5D,EAAiBZ,EAAM,SAAS,EAChDyE,EAAOrB,GAAyBoB,CAAa,EAC7CE,EAAa,CAAC5G,EAAMD,CAAK,EAAE,QAAQ2G,CAAa,GAAK,EACrDG,EAAMD,EAAa,SAAW,QAElC,GAAI,GAACJ,GAAgB,CAACC,GAItB,KAAIX,EAAgBK,GAAgBI,EAAQ,QAASrE,CAAK,EACtD4E,EAAY5C,GAAcsC,CAAY,EACtCO,EAAUJ,IAAS,IAAM9G,EAAMG,EAC/BgH,EAAUL,IAAS,IAAM7G,EAASC,EAClCkH,EAAU/E,EAAM,MAAM,UAAU2E,CAAG,EAAI3E,EAAM,MAAM,UAAUyE,CAAI,EAAIF,EAAcE,CAAI,EAAIzE,EAAM,MAAM,OAAO2E,CAAG,EACjHK,EAAYT,EAAcE,CAAI,EAAIzE,EAAM,MAAM,UAAUyE,CAAI,EAC5DQ,EAAoBhC,GAAgBqB,CAAY,EAChDY,EAAaD,EAAoBR,IAAS,IAAMQ,EAAkB,cAAgB,EAAIA,EAAkB,aAAe,EAAI,EAC3HE,EAAoBJ,EAAU,EAAIC,EAAY,EAG9ClE,EAAM8C,EAAciB,CAAO,EAC3BhE,EAAMqE,EAAaN,EAAUD,CAAG,EAAIf,EAAckB,CAAO,EACzDM,EAASF,EAAa,EAAIN,EAAUD,CAAG,EAAI,EAAIQ,EAC/CE,EAAShC,GAAOvC,EAAKsE,EAAQvE,CAAG,EAEhCyE,EAAWb,EACfzE,EAAM,cAAcC,CAAI,GAAKmE,EAAwB,CAAE,EAAEA,EAAsBkB,CAAQ,EAAID,EAAQjB,EAAsB,aAAeiB,EAASD,EAAQhB,GAC3J,CAEA,SAAS/D,GAAOC,EAAO,CACrB,IAAIN,EAAQM,EAAM,MACd+D,EAAU/D,EAAM,QAChBiF,EAAmBlB,EAAQ,QAC3BC,EAAeiB,IAAqB,OAAS,sBAAwBA,EAErEjB,GAAgB,OAKhB,OAAOA,GAAiB,WAC1BA,EAAetE,EAAM,SAAS,OAAO,cAAcsE,CAAY,EAE3D,CAACA,IAKFrC,GAASjC,EAAM,SAAS,OAAQsE,CAAY,IAIjDtE,EAAM,SAAS,MAAQsE,GACzB,CAGA,MAAekB,GAAA,CACb,KAAM,QACN,QAAS,GACT,MAAO,OACP,GAAIrB,GACJ,OAAQ9D,GACR,SAAU,CAAC,eAAe,EAC1B,iBAAkB,CAAC,iBAAiB,CACtC,ECzFe,SAASoF,GAAahH,EAAW,CAC9C,OAAOA,EAAU,MAAM,GAAG,EAAE,CAAC,CAC/B,CCOA,IAAIiH,GAAa,CACf,IAAK,OACL,MAAO,OACP,OAAQ,OACR,KAAM,MACR,EAIA,SAASC,GAAkB5F,EAAM6F,EAAK,CACpC,IAAIhE,EAAI7B,EAAK,EACT8B,EAAI9B,EAAK,EACT8F,EAAMD,EAAI,kBAAoB,EAClC,MAAO,CACL,EAAG7E,GAAMa,EAAIiE,CAAG,EAAIA,GAAO,EAC3B,EAAG9E,GAAMc,EAAIgE,CAAG,EAAIA,GAAO,CAC5B,CACH,CAEO,SAASC,GAAYxF,EAAO,CACjC,IAAIyF,EAEA1H,EAASiC,EAAM,OACf0F,EAAa1F,EAAM,WACnB7B,EAAY6B,EAAM,UAClB2F,EAAY3F,EAAM,UAClB4F,EAAU5F,EAAM,QAChB6F,EAAW7F,EAAM,SACjB8F,EAAkB9F,EAAM,gBACxB+F,EAAW/F,EAAM,SACjBgG,EAAehG,EAAM,aACrBiG,EAAUjG,EAAM,QAChBkG,EAAaN,EAAQ,EACrBtE,EAAI4E,IAAe,OAAS,EAAIA,EAChCC,EAAaP,EAAQ,EACrBrE,EAAI4E,IAAe,OAAS,EAAIA,EAEhCC,EAAQ,OAAOJ,GAAiB,WAAaA,EAAa,CAC5D,EAAG1E,EACH,EAAGC,CACP,CAAG,EAAI,CACH,EAAGD,EACH,EAAGC,CACJ,EAEDD,EAAI8E,EAAM,EACV7E,EAAI6E,EAAM,EACV,IAAIC,EAAOT,EAAQ,eAAe,GAAG,EACjCU,EAAOV,EAAQ,eAAe,GAAG,EACjCW,EAAQ/I,EACRgJ,EAAQnJ,EACRiI,EAAM,OAEV,GAAIS,EAAU,CACZ,IAAIlD,EAAeF,GAAgB5E,CAAM,EACrC0I,EAAa,eACbC,EAAY,cAchB,GAZI7D,IAAiB5D,EAAUlB,CAAM,IACnC8E,EAAeX,EAAmBnE,CAAM,EAEpCiE,EAAiBa,CAAY,EAAE,WAAa,UAAYgD,IAAa,aACvEY,EAAa,eACbC,EAAY,gBAKhB7D,EAAeA,EAEX1E,IAAcd,IAAQc,IAAcX,GAAQW,IAAcZ,IAAUoI,IAAc/H,EAAK,CACzF4I,EAAQlJ,EACR,IAAIqJ,EAAUV,GAAWpD,IAAiByC,GAAOA,EAAI,eAAiBA,EAAI,eAAe,OACzFzC,EAAa4D,CAAU,EACvBlF,GAAKoF,EAAUjB,EAAW,OAC1BnE,GAAKuE,EAAkB,EAAI,EACjC,CAEI,GAAI3H,IAAcX,IAASW,IAAcd,GAAOc,IAAcb,IAAWqI,IAAc/H,EAAK,CAC1F2I,EAAQhJ,EACR,IAAIqJ,EAAUX,GAAWpD,IAAiByC,GAAOA,EAAI,eAAiBA,EAAI,eAAe,MACzFzC,EAAa6D,CAAS,EACtBpF,GAAKsF,EAAUlB,EAAW,MAC1BpE,GAAKwE,EAAkB,EAAI,EACjC,CACA,CAEE,IAAIe,EAAe,OAAO,OAAO,CAC/B,SAAUhB,CACd,EAAKE,GAAYX,EAAU,EAErB0B,EAAQd,IAAiB,GAAOX,GAAkB,CACpD,EAAG/D,EACH,EAAGC,CACP,EAAKtC,EAAUlB,CAAM,CAAC,EAAI,CACtB,EAAGuD,EACH,EAAGC,CACJ,EAKD,GAHAD,EAAIwF,EAAM,EACVvF,EAAIuF,EAAM,EAENhB,EAAiB,CACnB,IAAIiB,EAEJ,OAAO,OAAO,OAAO,CAAE,EAAEF,GAAeE,EAAiB,CAAE,EAAEA,EAAeP,CAAK,EAAIF,EAAO,IAAM,GAAIS,EAAeR,CAAK,EAAIF,EAAO,IAAM,GAAIU,EAAe,WAAazB,EAAI,kBAAoB,IAAM,EAAI,aAAehE,EAAI,OAASC,EAAI,MAAQ,eAAiBD,EAAI,OAASC,EAAI,SAAUwF,EAAgB,CACrT,CAEE,OAAO,OAAO,OAAO,CAAE,EAAEF,GAAepB,EAAkB,CAAE,EAAEA,EAAgBe,CAAK,EAAIF,EAAO/E,EAAI,KAAO,GAAIkE,EAAgBc,CAAK,EAAIF,EAAO/E,EAAI,KAAO,GAAImE,EAAgB,UAAY,GAAIA,EAAiB,CAC/M,CAEA,SAASuB,GAAcC,EAAO,CAC5B,IAAIvH,EAAQuH,EAAM,MACdlD,EAAUkD,EAAM,QAChBC,EAAwBnD,EAAQ,gBAChC+B,EAAkBoB,IAA0B,OAAS,GAAOA,EAC5DC,EAAoBpD,EAAQ,SAC5BgC,EAAWoB,IAAsB,OAAS,GAAOA,EACjDC,EAAwBrD,EAAQ,aAChCiC,EAAeoB,IAA0B,OAAS,GAAOA,EACzDP,EAAe,CACjB,UAAWvG,EAAiBZ,EAAM,SAAS,EAC3C,UAAWyF,GAAazF,EAAM,SAAS,EACvC,OAAQA,EAAM,SAAS,OACvB,WAAYA,EAAM,MAAM,OACxB,gBAAiBoG,EACjB,QAASpG,EAAM,QAAQ,WAAa,OACrC,EAEGA,EAAM,cAAc,eAAiB,OACvCA,EAAM,OAAO,OAAS,OAAO,OAAO,CAAA,EAAIA,EAAM,OAAO,OAAQ8F,GAAY,OAAO,OAAO,CAAA,EAAIqB,EAAc,CACvG,QAASnH,EAAM,cAAc,cAC7B,SAAUA,EAAM,QAAQ,SACxB,SAAUqG,EACV,aAAcC,CACf,CAAA,CAAC,CAAC,GAGDtG,EAAM,cAAc,OAAS,OAC/BA,EAAM,OAAO,MAAQ,OAAO,OAAO,CAAA,EAAIA,EAAM,OAAO,MAAO8F,GAAY,OAAO,OAAO,CAAA,EAAIqB,EAAc,CACrG,QAASnH,EAAM,cAAc,MAC7B,SAAU,WACV,SAAU,GACV,aAAcsG,CACf,CAAA,CAAC,CAAC,GAGLtG,EAAM,WAAW,OAAS,OAAO,OAAO,GAAIA,EAAM,WAAW,OAAQ,CACnE,wBAAyBA,EAAM,SACnC,CAAG,CACH,CAGA,MAAe2H,GAAA,CACb,KAAM,gBACN,QAAS,GACT,MAAO,cACP,GAAIL,GACJ,KAAM,CAAA,CACR,ECtKA,IAAIM,GAAU,CACZ,QAAS,EACX,EAEA,SAASvH,GAAON,EAAM,CACpB,IAAIC,EAAQD,EAAK,MACb8H,EAAW9H,EAAK,SAChBsE,EAAUtE,EAAK,QACf+H,EAAkBzD,EAAQ,OAC1B0D,EAASD,IAAoB,OAAS,GAAOA,EAC7CE,EAAkB3D,EAAQ,OAC1B4D,EAASD,IAAoB,OAAS,GAAOA,EAC7C9E,EAAS3D,EAAUS,EAAM,SAAS,MAAM,EACxCkI,EAAgB,GAAG,OAAOlI,EAAM,cAAc,UAAWA,EAAM,cAAc,MAAM,EAEvF,OAAI+H,GACFG,EAAc,QAAQ,SAAUC,EAAc,CAC5CA,EAAa,iBAAiB,SAAUN,EAAS,OAAQD,EAAO,CACtE,CAAK,EAGCK,GACF/E,EAAO,iBAAiB,SAAU2E,EAAS,OAAQD,EAAO,EAGrD,UAAY,CACbG,GACFG,EAAc,QAAQ,SAAUC,EAAc,CAC5CA,EAAa,oBAAoB,SAAUN,EAAS,OAAQD,EAAO,CAC3E,CAAO,EAGCK,GACF/E,EAAO,oBAAoB,SAAU2E,EAAS,OAAQD,EAAO,CAEhE,CACH,CAGA,MAAeQ,GAAA,CACb,KAAM,iBACN,QAAS,GACT,MAAO,QACP,GAAI,UAAc,CAAE,EACpB,OAAQ/H,GACR,KAAM,CAAA,CACR,EChDA,IAAIgI,GAAO,CACT,KAAM,QACN,MAAO,OACP,OAAQ,MACR,IAAK,QACP,EACe,SAASC,GAAqB7J,EAAW,CACtD,OAAOA,EAAU,QAAQ,yBAA0B,SAAU8J,EAAS,CACpE,OAAOF,GAAKE,CAAO,CACvB,CAAG,CACH,CCVA,IAAIF,GAAO,CACT,MAAO,MACP,IAAK,OACP,EACe,SAASG,GAA8B/J,EAAW,CAC/D,OAAOA,EAAU,QAAQ,aAAc,SAAU8J,EAAS,CACxD,OAAOF,GAAKE,CAAO,CACvB,CAAG,CACH,CCPe,SAASE,GAAgBjJ,EAAM,CAC5C,IAAIoG,EAAMrG,EAAUC,CAAI,EACpBkJ,EAAa9C,EAAI,YACjB+C,EAAY/C,EAAI,YACpB,MAAO,CACL,WAAY8C,EACZ,UAAWC,CACZ,CACH,CCNe,SAASC,GAAoBtJ,EAAS,CAQnD,OAAO8B,GAAsBoB,EAAmBlD,CAAO,CAAC,EAAE,KAAOmJ,GAAgBnJ,CAAO,EAAE,UAC5F,CCRe,SAASuJ,GAAgBvJ,EAASwJ,EAAU,CACzD,IAAIlD,EAAMrG,EAAUD,CAAO,EACvByJ,EAAOvG,EAAmBlD,CAAO,EACjCoC,EAAiBkE,EAAI,eACrB9D,EAAQiH,EAAK,YACbhH,EAASgH,EAAK,aACdnH,EAAI,EACJC,EAAI,EAER,GAAIH,EAAgB,CAClBI,EAAQJ,EAAe,MACvBK,EAASL,EAAe,OACxB,IAAIsH,EAAiB7H,GAAkB,GAEnC6H,GAAkB,CAACA,GAAkBF,IAAa,WACpDlH,EAAIF,EAAe,WACnBG,EAAIH,EAAe,UAEzB,CAEE,MAAO,CACL,MAAOI,EACP,OAAQC,EACR,EAAGH,EAAIgH,GAAoBtJ,CAAO,EAClC,EAAGuC,CACJ,CACH,CCvBe,SAASoH,GAAgB3J,EAAS,CAC/C,IAAI4J,EAEAH,EAAOvG,EAAmBlD,CAAO,EACjC6J,EAAYV,GAAgBnJ,CAAO,EACnC8J,GAAQF,EAAwB5J,EAAQ,gBAAkB,KAAO,OAAS4J,EAAsB,KAChGpH,EAAQjB,EAAIkI,EAAK,YAAaA,EAAK,YAAaK,EAAOA,EAAK,YAAc,EAAGA,EAAOA,EAAK,YAAc,CAAC,EACxGrH,EAASlB,EAAIkI,EAAK,aAAcA,EAAK,aAAcK,EAAOA,EAAK,aAAe,EAAGA,EAAOA,EAAK,aAAe,CAAC,EAC7GxH,EAAI,CAACuH,EAAU,WAAaP,GAAoBtJ,CAAO,EACvDuC,EAAI,CAACsH,EAAU,UAEnB,OAAI7G,EAAiB8G,GAAQL,CAAI,EAAE,YAAc,QAC/CnH,GAAKf,EAAIkI,EAAK,YAAaK,EAAOA,EAAK,YAAc,CAAC,EAAItH,GAGrD,CACL,MAAOA,EACP,OAAQC,EACR,EAAGH,EACH,EAAGC,CACJ,CACH,CC3Be,SAASwH,GAAe/J,EAAS,CAE9C,IAAIgK,EAAoBhH,EAAiBhD,CAAO,EAC5CiK,EAAWD,EAAkB,SAC7BE,EAAYF,EAAkB,UAC9BG,EAAYH,EAAkB,UAElC,MAAO,6BAA6B,KAAKC,EAAWE,EAAYD,CAAS,CAC3E,CCLe,SAASE,GAAgBlK,EAAM,CAC5C,MAAI,CAAC,OAAQ,OAAQ,WAAW,EAAE,QAAQH,EAAYG,CAAI,CAAC,GAAK,EAEvDA,EAAK,cAAc,KAGxBI,EAAcJ,CAAI,GAAK6J,GAAe7J,CAAI,EACrCA,EAGFkK,GAAgBjH,GAAcjD,CAAI,CAAC,CAC5C,CCJe,SAASmK,GAAkBrK,EAASsK,EAAM,CACvD,IAAIV,EAEAU,IAAS,SACXA,EAAO,CAAE,GAGX,IAAIzB,EAAeuB,GAAgBpK,CAAO,EACtCuK,EAAS1B,MAAmBe,EAAwB5J,EAAQ,gBAAkB,KAAO,OAAS4J,EAAsB,MACpHtD,EAAMrG,EAAU4I,CAAY,EAC5B2B,EAASD,EAAS,CAACjE,CAAG,EAAE,OAAOA,EAAI,gBAAkB,CAAE,EAAEyD,GAAelB,CAAY,EAAIA,EAAe,CAAA,CAAE,EAAIA,EAC7G4B,EAAcH,EAAK,OAAOE,CAAM,EACpC,OAAOD,EAASE,EAChBA,EAAY,OAAOJ,GAAkBlH,GAAcqH,CAAM,CAAC,CAAC,CAC7D,CCzBe,SAASE,GAAiBC,EAAM,CAC7C,OAAO,OAAO,OAAO,CAAE,EAAEA,EAAM,CAC7B,KAAMA,EAAK,EACX,IAAKA,EAAK,EACV,MAAOA,EAAK,EAAIA,EAAK,MACrB,OAAQA,EAAK,EAAIA,EAAK,MAC1B,CAAG,CACH,CCQA,SAASC,GAA2B5K,EAASwJ,EAAU,CACrD,IAAImB,EAAO7I,GAAsB9B,EAAS,GAAOwJ,IAAa,OAAO,EACrE,OAAAmB,EAAK,IAAMA,EAAK,IAAM3K,EAAQ,UAC9B2K,EAAK,KAAOA,EAAK,KAAO3K,EAAQ,WAChC2K,EAAK,OAASA,EAAK,IAAM3K,EAAQ,aACjC2K,EAAK,MAAQA,EAAK,KAAO3K,EAAQ,YACjC2K,EAAK,MAAQ3K,EAAQ,YACrB2K,EAAK,OAAS3K,EAAQ,aACtB2K,EAAK,EAAIA,EAAK,KACdA,EAAK,EAAIA,EAAK,IACPA,CACT,CAEA,SAASE,GAA2B7K,EAAS8K,EAAgBtB,EAAU,CACrE,OAAOsB,IAAmBhM,GAAW4L,GAAiBnB,GAAgBvJ,EAASwJ,CAAQ,CAAC,EAAIpJ,EAAU0K,CAAc,EAAIF,GAA2BE,EAAgBtB,CAAQ,EAAIkB,GAAiBf,GAAgBzG,EAAmBlD,CAAO,CAAC,CAAC,CAC9O,CAKA,SAAS+K,GAAmB/K,EAAS,CACnC,IAAInB,EAAkBwL,GAAkBlH,GAAcnD,CAAO,CAAC,EAC1DgL,EAAoB,CAAC,WAAY,OAAO,EAAE,QAAQhI,EAAiBhD,CAAO,EAAE,QAAQ,GAAK,EACzFiL,EAAiBD,GAAqB1K,EAAcN,CAAO,EAAI2D,GAAgB3D,CAAO,EAAIA,EAE9F,OAAKI,EAAU6K,CAAc,EAKtBpM,EAAgB,OAAO,SAAUiM,EAAgB,CACtD,OAAO1K,EAAU0K,CAAc,GAAKnI,GAASmI,EAAgBG,CAAc,GAAKlL,EAAY+K,CAAc,IAAM,MACpH,CAAG,EANQ,CAAE,CAOb,CAIe,SAASI,GAAgBlL,EAASmL,EAAUC,EAAc5B,EAAU,CACjF,IAAI6B,EAAsBF,IAAa,kBAAoBJ,GAAmB/K,CAAO,EAAI,CAAA,EAAG,OAAOmL,CAAQ,EACvGtM,EAAkB,CAAA,EAAG,OAAOwM,EAAqB,CAACD,CAAY,CAAC,EAC/DE,EAAsBzM,EAAgB,CAAC,EACvC0M,EAAe1M,EAAgB,OAAO,SAAU2M,EAASV,EAAgB,CAC3E,IAAIH,EAAOE,GAA2B7K,EAAS8K,EAAgBtB,CAAQ,EACvE,OAAAgC,EAAQ,IAAMjK,EAAIoJ,EAAK,IAAKa,EAAQ,GAAG,EACvCA,EAAQ,MAAQhK,GAAImJ,EAAK,MAAOa,EAAQ,KAAK,EAC7CA,EAAQ,OAAShK,GAAImJ,EAAK,OAAQa,EAAQ,MAAM,EAChDA,EAAQ,KAAOjK,EAAIoJ,EAAK,KAAMa,EAAQ,IAAI,EACnCA,CACR,EAAEX,GAA2B7K,EAASsL,EAAqB9B,CAAQ,CAAC,EACrE,OAAA+B,EAAa,MAAQA,EAAa,MAAQA,EAAa,KACvDA,EAAa,OAASA,EAAa,OAASA,EAAa,IACzDA,EAAa,EAAIA,EAAa,KAC9BA,EAAa,EAAIA,EAAa,IACvBA,CACT,CCjEe,SAASE,GAAehL,EAAM,CAC3C,IAAIzB,EAAYyB,EAAK,UACjBT,EAAUS,EAAK,QACftB,EAAYsB,EAAK,UACjByE,EAAgB/F,EAAYmC,EAAiBnC,CAAS,EAAI,KAC1DwH,EAAYxH,EAAYgH,GAAahH,CAAS,EAAI,KAClDuM,EAAU1M,EAAU,EAAIA,EAAU,MAAQ,EAAIgB,EAAQ,MAAQ,EAC9D2L,EAAU3M,EAAU,EAAIA,EAAU,OAAS,EAAIgB,EAAQ,OAAS,EAChE4G,EAEJ,OAAQ1B,EAAa,CACnB,KAAK7G,EACHuI,EAAU,CACR,EAAG8E,EACH,EAAG1M,EAAU,EAAIgB,EAAQ,MAC1B,EACD,MAEF,KAAK1B,EACHsI,EAAU,CACR,EAAG8E,EACH,EAAG1M,EAAU,EAAIA,EAAU,MAC5B,EACD,MAEF,KAAKT,EACHqI,EAAU,CACR,EAAG5H,EAAU,EAAIA,EAAU,MAC3B,EAAG2M,CACJ,EACD,MAEF,KAAKnN,EACHoI,EAAU,CACR,EAAG5H,EAAU,EAAIgB,EAAQ,MACzB,EAAG2L,CACJ,EACD,MAEF,QACE/E,EAAU,CACR,EAAG5H,EAAU,EACb,EAAGA,EAAU,CACd,CACP,CAEE,IAAI4M,EAAW1G,EAAgBpB,GAAyBoB,CAAa,EAAI,KAEzE,GAAI0G,GAAY,KAAM,CACpB,IAAIvG,EAAMuG,IAAa,IAAM,SAAW,QAExC,OAAQjF,EAAS,CACf,KAAKhI,EACHiI,EAAQgF,CAAQ,EAAIhF,EAAQgF,CAAQ,GAAK5M,EAAUqG,CAAG,EAAI,EAAIrF,EAAQqF,CAAG,EAAI,GAC7E,MAEF,KAAKzG,EACHgI,EAAQgF,CAAQ,EAAIhF,EAAQgF,CAAQ,GAAK5M,EAAUqG,CAAG,EAAI,EAAIrF,EAAQqF,CAAG,EAAI,GAC7E,KAGR,CACA,CAEE,OAAOuB,CACT,CC3De,SAASiF,GAAenL,EAAOqE,EAAS,CACjDA,IAAY,SACdA,EAAU,CAAE,GAGd,IAAI+G,EAAW/G,EACXgH,EAAqBD,EAAS,UAC9B3M,EAAY4M,IAAuB,OAASrL,EAAM,UAAYqL,EAC9DC,EAAoBF,EAAS,SAC7BtC,EAAWwC,IAAsB,OAAStL,EAAM,SAAWsL,EAC3DC,EAAoBH,EAAS,SAC7BX,EAAWc,IAAsB,OAASpN,GAAkBoN,EAC5DC,EAAwBJ,EAAS,aACjCV,EAAec,IAA0B,OAASpN,GAAWoN,EAC7DC,EAAwBL,EAAS,eACjCM,EAAiBD,IAA0B,OAASpN,EAASoN,EAC7DE,EAAuBP,EAAS,YAChCQ,EAAcD,IAAyB,OAAS,GAAQA,EACxDE,EAAmBT,EAAS,QAC5BlH,EAAU2H,IAAqB,OAAS,EAAIA,EAC5CjI,EAAgBD,GAAmB,OAAOO,GAAY,SAAWA,EAAUL,GAAgBK,EAASlG,EAAc,CAAC,EACnH8N,EAAaJ,IAAmBrN,EAASC,GAAYD,EACrD2H,EAAahG,EAAM,MAAM,OACzBV,EAAUU,EAAM,SAAS4L,EAAcE,EAAaJ,CAAc,EAClEK,EAAqBvB,GAAgB9K,EAAUJ,CAAO,EAAIA,EAAUA,EAAQ,gBAAkBkD,EAAmBxC,EAAM,SAAS,MAAM,EAAGyK,EAAUC,EAAc5B,CAAQ,EACzKkD,EAAsB5K,GAAsBpB,EAAM,SAAS,SAAS,EACpEuE,EAAgBwG,GAAe,CACjC,UAAWiB,EACX,QAAShG,EAET,UAAWvH,CACf,CAAG,EACGwN,EAAmBjC,GAAiB,OAAO,OAAO,GAAIhE,EAAYzB,CAAa,CAAC,EAChF2H,EAAoBR,IAAmBrN,EAAS4N,EAAmBD,EAGnEG,EAAkB,CACpB,IAAKJ,EAAmB,IAAMG,EAAkB,IAAMtI,EAAc,IACpE,OAAQsI,EAAkB,OAASH,EAAmB,OAASnI,EAAc,OAC7E,KAAMmI,EAAmB,KAAOG,EAAkB,KAAOtI,EAAc,KACvE,MAAOsI,EAAkB,MAAQH,EAAmB,MAAQnI,EAAc,KAC3E,EACGwI,EAAapM,EAAM,cAAc,OAErC,GAAI0L,IAAmBrN,GAAU+N,EAAY,CAC3C,IAAI/G,EAAS+G,EAAW3N,CAAS,EACjC,OAAO,KAAK0N,CAAe,EAAE,QAAQ,SAAUnI,EAAK,CAClD,IAAIqI,EAAW,CAACxO,EAAOD,CAAM,EAAE,QAAQoG,CAAG,GAAK,EAAI,EAAI,GACnDS,EAAO,CAAC9G,EAAKC,CAAM,EAAE,QAAQoG,CAAG,GAAK,EAAI,IAAM,IACnDmI,EAAgBnI,CAAG,GAAKqB,EAAOZ,CAAI,EAAI4H,CAC7C,CAAK,CACL,CAEE,OAAOF,CACT,CC5De,SAASG,GAAqBtM,EAAOqE,EAAS,CACvDA,IAAY,SACdA,EAAU,CAAE,GAGd,IAAI+G,EAAW/G,EACX5F,EAAY2M,EAAS,UACrBX,EAAWW,EAAS,SACpBV,EAAeU,EAAS,aACxBlH,EAAUkH,EAAS,QACnBmB,EAAiBnB,EAAS,eAC1BoB,EAAwBpB,EAAS,sBACjCqB,EAAwBD,IAA0B,OAASE,GAAgBF,EAC3EvG,EAAYR,GAAahH,CAAS,EAClCC,EAAauH,EAAYsG,EAAiBhO,GAAsBA,GAAoB,OAAO,SAAUE,EAAW,CAClH,OAAOgH,GAAahH,CAAS,IAAMwH,CACpC,CAAA,EAAIjI,GACD2O,EAAoBjO,EAAW,OAAO,SAAUD,EAAW,CAC7D,OAAOgO,EAAsB,QAAQhO,CAAS,GAAK,CACvD,CAAG,EAEGkO,EAAkB,SAAW,IAC/BA,EAAoBjO,GAItB,IAAIkO,EAAYD,EAAkB,OAAO,SAAUnO,EAAKC,EAAW,CACjE,OAAAD,EAAIC,CAAS,EAAI0M,GAAenL,EAAO,CACrC,UAAWvB,EACX,SAAUgM,EACV,aAAcC,EACd,QAASxG,CACf,CAAK,EAAEtD,EAAiBnC,CAAS,CAAC,EACvBD,CACR,EAAE,EAAE,EACL,OAAO,OAAO,KAAKoO,CAAS,EAAE,KAAK,SAAUC,EAAGC,EAAG,CACjD,OAAOF,EAAUC,CAAC,EAAID,EAAUE,CAAC,CACrC,CAAG,CACH,CClCA,SAASC,GAA8BtO,EAAW,CAChD,GAAImC,EAAiBnC,CAAS,IAAMV,GAClC,MAAO,CAAE,EAGX,IAAIiP,EAAoB1E,GAAqB7J,CAAS,EACtD,MAAO,CAAC+J,GAA8B/J,CAAS,EAAGuO,EAAmBxE,GAA8BwE,CAAiB,CAAC,CACvH,CAEA,SAASC,GAAKlN,EAAM,CAClB,IAAIC,EAAQD,EAAK,MACbsE,EAAUtE,EAAK,QACfE,EAAOF,EAAK,KAEhB,GAAI,CAAAC,EAAM,cAAcC,CAAI,EAAE,MAoC9B,SAhCIiN,EAAoB7I,EAAQ,SAC5B8I,EAAgBD,IAAsB,OAAS,GAAOA,EACtDE,EAAmB/I,EAAQ,QAC3BgJ,EAAeD,IAAqB,OAAS,GAAOA,EACpDE,EAA8BjJ,EAAQ,mBACtCH,EAAUG,EAAQ,QAClBoG,EAAWpG,EAAQ,SACnBqG,EAAerG,EAAQ,aACvBuH,EAAcvH,EAAQ,YACtBkJ,EAAwBlJ,EAAQ,eAChCkI,EAAiBgB,IAA0B,OAAS,GAAOA,EAC3Dd,EAAwBpI,EAAQ,sBAChCmJ,EAAqBxN,EAAM,QAAQ,UACnCwE,EAAgB5D,EAAiB4M,CAAkB,EACnDC,EAAkBjJ,IAAkBgJ,EACpCE,EAAqBJ,IAAgCG,GAAmB,CAAClB,EAAiB,CAACjE,GAAqBkF,CAAkB,CAAC,EAAIT,GAA8BS,CAAkB,GACvL9O,EAAa,CAAC8O,CAAkB,EAAE,OAAOE,CAAkB,EAAE,OAAO,SAAUlP,EAAKC,EAAW,CAChG,OAAOD,EAAI,OAAOoC,EAAiBnC,CAAS,IAAMV,GAAOuO,GAAqBtM,EAAO,CACnF,UAAWvB,EACX,SAAUgM,EACV,aAAcC,EACd,QAASxG,EACT,eAAgBqI,EAChB,sBAAuBE,CACxB,CAAA,EAAIhO,CAAS,CACf,EAAE,EAAE,EACDkP,EAAgB3N,EAAM,MAAM,UAC5BgG,EAAahG,EAAM,MAAM,OACzB4N,EAAY,IAAI,IAChBC,EAAqB,GACrBC,EAAwBpP,EAAW,CAAC,EAE/BqP,EAAI,EAAGA,EAAIrP,EAAW,OAAQqP,IAAK,CAC1C,IAAItP,EAAYC,EAAWqP,CAAC,EAExBC,EAAiBpN,EAAiBnC,CAAS,EAE3CwP,EAAmBxI,GAAahH,CAAS,IAAMR,EAC/CyG,EAAa,CAAC/G,EAAKC,CAAM,EAAE,QAAQoQ,CAAc,GAAK,EACtDrJ,EAAMD,EAAa,QAAU,SAC7B6E,EAAW4B,GAAenL,EAAO,CACnC,UAAWvB,EACX,SAAUgM,EACV,aAAcC,EACd,YAAakB,EACb,QAAS1H,CACf,CAAK,EACGgK,EAAoBxJ,EAAauJ,EAAmBpQ,EAAQC,EAAOmQ,EAAmBrQ,EAASD,EAE/FgQ,EAAchJ,CAAG,EAAIqB,EAAWrB,CAAG,IACrCuJ,EAAoB5F,GAAqB4F,CAAiB,GAG5D,IAAIC,GAAmB7F,GAAqB4F,CAAiB,EACzDE,EAAS,CAAE,EAUf,GARIjB,GACFiB,EAAO,KAAK7E,EAASyE,CAAc,GAAK,CAAC,EAGvCX,GACFe,EAAO,KAAK7E,EAAS2E,CAAiB,GAAK,EAAG3E,EAAS4E,EAAgB,GAAK,CAAC,EAG3EC,EAAO,MAAM,SAAUC,EAAO,CAChC,OAAOA,CACb,CAAK,EAAG,CACFP,EAAwBrP,EACxBoP,EAAqB,GACrB,KACN,CAEID,EAAU,IAAInP,EAAW2P,CAAM,CACnC,CAEE,GAAIP,EAqBF,QAnBIS,GAAiB/B,EAAiB,EAAI,EAEtCgC,GAAQ,SAAeC,EAAI,CAC7B,IAAIC,GAAmB/P,EAAW,KAAK,SAAUD,GAAW,CAC1D,IAAI2P,EAASR,EAAU,IAAInP,EAAS,EAEpC,GAAI2P,EACF,OAAOA,EAAO,MAAM,EAAGI,CAAE,EAAE,MAAM,SAAUH,GAAO,CAChD,OAAOA,EACnB,CAAW,CAEX,CAAO,EAED,GAAII,GACF,OAAAX,EAAwBW,GACjB,OAEV,EAEQD,GAAKF,GAAgBE,GAAK,EAAGA,KAAM,CAC1C,IAAIE,GAAOH,GAAMC,EAAE,EAEnB,GAAIE,KAAS,QAAS,KAC5B,CAGM1O,EAAM,YAAc8N,IACtB9N,EAAM,cAAcC,CAAI,EAAE,MAAQ,GAClCD,EAAM,UAAY8N,EAClB9N,EAAM,MAAQ,IAElB,CAGA,MAAe2O,GAAA,CACb,KAAM,OACN,QAAS,GACT,MAAO,OACP,GAAI1B,GACJ,iBAAkB,CAAC,QAAQ,EAC3B,KAAM,CACJ,MAAO,EACX,CACA,EC/IA,SAAS2B,GAAerF,EAAUU,EAAM4E,EAAkB,CACxD,OAAIA,IAAqB,SACvBA,EAAmB,CACjB,EAAG,EACH,EAAG,CACJ,GAGI,CACL,IAAKtF,EAAS,IAAMU,EAAK,OAAS4E,EAAiB,EACnD,MAAOtF,EAAS,MAAQU,EAAK,MAAQ4E,EAAiB,EACtD,OAAQtF,EAAS,OAASU,EAAK,OAAS4E,EAAiB,EACzD,KAAMtF,EAAS,KAAOU,EAAK,MAAQ4E,EAAiB,CACrD,CACH,CAEA,SAASC,GAAsBvF,EAAU,CACvC,MAAO,CAAC5L,EAAKE,EAAOD,EAAQE,CAAI,EAAE,KAAK,SAAUiR,EAAM,CACrD,OAAOxF,EAASwF,CAAI,GAAK,CAC7B,CAAG,CACH,CAEA,SAASC,GAAKjP,EAAM,CAClB,IAAIC,EAAQD,EAAK,MACbE,EAAOF,EAAK,KACZ4N,EAAgB3N,EAAM,MAAM,UAC5BgG,EAAahG,EAAM,MAAM,OACzB6O,EAAmB7O,EAAM,cAAc,gBACvCiP,EAAoB9D,GAAenL,EAAO,CAC5C,eAAgB,WACpB,CAAG,EACGkP,EAAoB/D,GAAenL,EAAO,CAC5C,YAAa,EACjB,CAAG,EACGmP,EAA2BP,GAAeK,EAAmBtB,CAAa,EAC1EyB,EAAsBR,GAAeM,EAAmBlJ,EAAY6I,CAAgB,EACpFQ,EAAoBP,GAAsBK,CAAwB,EAClEG,EAAmBR,GAAsBM,CAAmB,EAChEpP,EAAM,cAAcC,CAAI,EAAI,CAC1B,yBAA0BkP,EAC1B,oBAAqBC,EACrB,kBAAmBC,EACnB,iBAAkBC,CACnB,EACDtP,EAAM,WAAW,OAAS,OAAO,OAAO,GAAIA,EAAM,WAAW,OAAQ,CACnE,+BAAgCqP,EAChC,sBAAuBC,CAC3B,CAAG,CACH,CAGA,MAAeC,GAAA,CACb,KAAM,OACN,QAAS,GACT,MAAO,OACP,iBAAkB,CAAC,iBAAiB,EACpC,GAAIP,EACN,ECzDO,SAASQ,GAAwB/Q,EAAWgR,EAAOpK,EAAQ,CAChE,IAAIb,EAAgB5D,EAAiBnC,CAAS,EAC1CiR,EAAiB,CAAC5R,EAAMH,CAAG,EAAE,QAAQ6G,CAAa,GAAK,EAAI,GAAK,EAEhEzE,EAAO,OAAOsF,GAAW,WAAaA,EAAO,OAAO,OAAO,CAAE,EAAEoK,EAAO,CACxE,UAAWhR,CACZ,CAAA,CAAC,EAAI4G,EACFsK,EAAW5P,EAAK,CAAC,EACjB6P,EAAW7P,EAAK,CAAC,EAErB,OAAA4P,EAAWA,GAAY,EACvBC,GAAYA,GAAY,GAAKF,EACtB,CAAC5R,EAAMD,CAAK,EAAE,QAAQ2G,CAAa,GAAK,EAAI,CACjD,EAAGoL,EACH,EAAGD,CACP,EAAM,CACF,EAAGA,EACH,EAAGC,CACJ,CACH,CAEA,SAASvK,GAAO/E,EAAO,CACrB,IAAIN,EAAQM,EAAM,MACd+D,EAAU/D,EAAM,QAChBL,EAAOK,EAAM,KACbuP,EAAkBxL,EAAQ,OAC1BgB,EAASwK,IAAoB,OAAS,CAAC,EAAG,CAAC,EAAIA,EAC/CC,EAAOpR,GAAW,OAAO,SAAUF,EAAKC,EAAW,CACrD,OAAAD,EAAIC,CAAS,EAAI+Q,GAAwB/Q,EAAWuB,EAAM,MAAOqF,CAAM,EAChE7G,CACR,EAAE,EAAE,EACDuR,EAAwBD,EAAK9P,EAAM,SAAS,EAC5C4B,EAAImO,EAAsB,EAC1BlO,EAAIkO,EAAsB,EAE1B/P,EAAM,cAAc,eAAiB,OACvCA,EAAM,cAAc,cAAc,GAAK4B,EACvC5B,EAAM,cAAc,cAAc,GAAK6B,GAGzC7B,EAAM,cAAcC,CAAI,EAAI6P,CAC9B,CAGA,MAAeE,GAAA,CACb,KAAM,SACN,QAAS,GACT,MAAO,OACP,SAAU,CAAC,eAAe,EAC1B,GAAI3K,EACN,ECnDA,SAASd,GAAcxE,EAAM,CAC3B,IAAIC,EAAQD,EAAK,MACbE,EAAOF,EAAK,KAKhBC,EAAM,cAAcC,CAAI,EAAI8K,GAAe,CACzC,UAAW/K,EAAM,MAAM,UACvB,QAASA,EAAM,MAAM,OAErB,UAAWA,EAAM,SACrB,CAAG,CACH,CAGA,MAAeiQ,GAAA,CACb,KAAM,gBACN,QAAS,GACT,MAAO,OACP,GAAI1L,GACJ,KAAM,CAAA,CACR,ECxBe,SAAS2L,GAAWzL,EAAM,CACvC,OAAOA,IAAS,IAAM,IAAM,GAC9B,CCUA,SAAS0L,GAAgBpQ,EAAM,CAC7B,IAAIC,EAAQD,EAAK,MACbsE,EAAUtE,EAAK,QACfE,EAAOF,EAAK,KACZmN,EAAoB7I,EAAQ,SAC5B8I,EAAgBD,IAAsB,OAAS,GAAOA,EACtDE,EAAmB/I,EAAQ,QAC3BgJ,EAAeD,IAAqB,OAAS,GAAQA,EACrD3C,EAAWpG,EAAQ,SACnBqG,EAAerG,EAAQ,aACvBuH,EAAcvH,EAAQ,YACtBH,EAAUG,EAAQ,QAClB+L,EAAkB/L,EAAQ,OAC1BgM,EAASD,IAAoB,OAAS,GAAOA,EAC7CE,EAAwBjM,EAAQ,aAChCkM,EAAeD,IAA0B,OAAS,EAAIA,EACtD/G,EAAW4B,GAAenL,EAAO,CACnC,SAAUyK,EACV,aAAcC,EACd,QAASxG,EACT,YAAa0H,CACjB,CAAG,EACGpH,EAAgB5D,EAAiBZ,EAAM,SAAS,EAChDiG,EAAYR,GAAazF,EAAM,SAAS,EACxCyN,EAAkB,CAACxH,EACnBiF,EAAW9H,GAAyBoB,CAAa,EACjDgM,EAAUN,GAAWhF,CAAQ,EAC7B3G,EAAgBvE,EAAM,cAAc,cACpC2N,EAAgB3N,EAAM,MAAM,UAC5BgG,EAAahG,EAAM,MAAM,OACzByQ,EAAoB,OAAOF,GAAiB,WAAaA,EAAa,OAAO,OAAO,CAAA,EAAIvQ,EAAM,MAAO,CACvG,UAAWA,EAAM,SAClB,CAAA,CAAC,EAAIuQ,EACFG,EAA8B,OAAOD,GAAsB,SAAW,CACxE,SAAUA,EACV,QAASA,CACb,EAAM,OAAO,OAAO,CAChB,SAAU,EACV,QAAS,CACV,EAAEA,CAAiB,EAChBE,EAAsB3Q,EAAM,cAAc,OAASA,EAAM,cAAc,OAAOA,EAAM,SAAS,EAAI,KACjG8P,EAAO,CACT,EAAG,EACH,EAAG,CACJ,EAED,GAAKvL,EAIL,IAAI4I,EAAe,CACjB,IAAIyD,EAEAC,EAAW3F,IAAa,IAAMvN,EAAMG,EACpCgT,EAAU5F,IAAa,IAAMtN,EAASC,EACtC8G,EAAMuG,IAAa,IAAM,SAAW,QACpC7F,EAASd,EAAc2G,CAAQ,EAC/BpK,GAAMuE,EAASkE,EAASsH,CAAQ,EAChChQ,EAAMwE,EAASkE,EAASuH,CAAO,EAC/BC,GAAWV,EAAS,CAACrK,EAAWrB,CAAG,EAAI,EAAI,EAC3CqM,GAAS/K,IAAchI,EAAQ0P,EAAchJ,CAAG,EAAIqB,EAAWrB,CAAG,EAClEsM,GAAShL,IAAchI,EAAQ,CAAC+H,EAAWrB,CAAG,EAAI,CAACgJ,EAAchJ,CAAG,EAGpEL,GAAetE,EAAM,SAAS,MAC9B4E,EAAYyL,GAAU/L,GAAetC,GAAcsC,EAAY,EAAI,CACrE,MAAO,EACP,OAAQ,CACT,EACG4M,EAAqBlR,EAAM,cAAc,kBAAkB,EAAIA,EAAM,cAAc,kBAAkB,EAAE,QAAU0D,GAAoB,EACrIyN,GAAkBD,EAAmBL,CAAQ,EAC7CO,GAAkBF,EAAmBJ,CAAO,EAM5CO,EAAWhO,GAAO,EAAGsK,EAAchJ,CAAG,EAAGC,EAAUD,CAAG,CAAC,EACvD2M,GAAY7D,EAAkBE,EAAchJ,CAAG,EAAI,EAAIoM,GAAWM,EAAWF,GAAkBT,EAA4B,SAAWM,GAASK,EAAWF,GAAkBT,EAA4B,SACxMa,GAAY9D,EAAkB,CAACE,EAAchJ,CAAG,EAAI,EAAIoM,GAAWM,EAAWD,GAAkBV,EAA4B,SAAWO,GAASI,EAAWD,GAAkBV,EAA4B,SACzMzL,GAAoBjF,EAAM,SAAS,OAASiD,GAAgBjD,EAAM,SAAS,KAAK,EAChFwR,GAAevM,GAAoBiG,IAAa,IAAMjG,GAAkB,WAAa,EAAIA,GAAkB,YAAc,EAAI,EAC7HwM,IAAuBb,EAAwBD,GAAuB,KAAO,OAASA,EAAoBzF,CAAQ,IAAM,KAAO0F,EAAwB,EACvJc,GAAYrM,EAASiM,GAAYG,GAAsBD,GACvDG,GAAYtM,EAASkM,GAAYE,GACjCG,GAAkBvO,GAAOgN,EAAS9M,GAAQzC,GAAK4Q,EAAS,EAAI5Q,GAAKuE,EAAQgL,EAAS/M,EAAQzC,EAAK8Q,EAAS,EAAI9Q,CAAG,EACnH0D,EAAc2G,CAAQ,EAAI0G,GAC1B9B,EAAK5E,CAAQ,EAAI0G,GAAkBvM,CACvC,CAEE,GAAIgI,EAAc,CAChB,IAAIwE,GAEAC,GAAY5G,IAAa,IAAMvN,EAAMG,EAErCiU,GAAW7G,IAAa,IAAMtN,EAASC,EAEvCmU,EAAUzN,EAAciM,CAAO,EAE/ByB,GAAOzB,IAAY,IAAM,SAAW,QAEpC0B,GAAOF,EAAUzI,EAASuI,EAAS,EAEnCK,GAAOH,EAAUzI,EAASwI,EAAQ,EAElCK,GAAe,CAACzU,EAAKG,CAAI,EAAE,QAAQ0G,CAAa,IAAM,GAEtD6N,IAAwBR,GAAyBlB,GAAuB,KAAO,OAASA,EAAoBH,CAAO,IAAM,KAAOqB,GAAyB,EAEzJS,GAAaF,GAAeF,GAAOF,EAAUrE,EAAcsE,EAAI,EAAIjM,EAAWiM,EAAI,EAAII,GAAuB3B,EAA4B,QAEzI6B,GAAaH,GAAeJ,EAAUrE,EAAcsE,EAAI,EAAIjM,EAAWiM,EAAI,EAAII,GAAuB3B,EAA4B,QAAUyB,GAE5IK,GAAmBnC,GAAU+B,GAAe5O,GAAe8O,GAAYN,EAASO,EAAU,EAAIlP,GAAOgN,EAASiC,GAAaJ,GAAMF,EAAS3B,EAASkC,GAAaJ,EAAI,EAExK5N,EAAciM,CAAO,EAAIgC,GACzB1C,EAAKU,CAAO,EAAIgC,GAAmBR,CACvC,CAEEhS,EAAM,cAAcC,CAAI,EAAI6P,EAC9B,CAGA,MAAe2C,GAAA,CACb,KAAM,kBACN,QAAS,GACT,MAAO,OACP,GAAItC,GACJ,iBAAkB,CAAC,QAAQ,CAC7B,EC7Ie,SAASuC,GAAqBpT,EAAS,CACpD,MAAO,CACL,WAAYA,EAAQ,WACpB,UAAWA,EAAQ,SACpB,CACH,CCDe,SAASqT,GAAcnT,EAAM,CAC1C,OAAIA,IAASD,EAAUC,CAAI,GAAK,CAACI,EAAcJ,CAAI,EAC1CiJ,GAAgBjJ,CAAI,EAEpBkT,GAAqBlT,CAAI,CAEpC,CCDA,SAASoT,GAAgBtT,EAAS,CAChC,IAAI2K,EAAO3K,EAAQ,sBAAuB,EACtCkC,EAAST,GAAMkJ,EAAK,KAAK,EAAI3K,EAAQ,aAAe,EACpDmC,EAASV,GAAMkJ,EAAK,MAAM,EAAI3K,EAAQ,cAAgB,EAC1D,OAAOkC,IAAW,GAAKC,IAAW,CACpC,CAIe,SAASoR,GAAiBC,EAAyB3P,EAAcoD,EAAS,CACnFA,IAAY,SACdA,EAAU,IAGZ,IAAIwM,EAA0BnT,EAAcuD,CAAY,EACpD6P,EAAuBpT,EAAcuD,CAAY,GAAKyP,GAAgBzP,CAAY,EAClF8P,EAAkBzQ,EAAmBW,CAAY,EACjD8G,EAAO7I,GAAsB0R,EAAyBE,EAAsBzM,CAAO,EACnFwB,EAAS,CACX,WAAY,EACZ,UAAW,CACZ,EACG7B,EAAU,CACZ,EAAG,EACH,EAAG,CACJ,EAED,OAAI6M,GAA2B,CAACA,GAA2B,CAACxM,MACtDlH,EAAY8D,CAAY,IAAM,QAClCkG,GAAe4J,CAAe,KAC5BlL,EAAS4K,GAAcxP,CAAY,GAGjCvD,EAAcuD,CAAY,GAC5B+C,EAAU9E,GAAsB+B,EAAc,EAAI,EAClD+C,EAAQ,GAAK/C,EAAa,WAC1B+C,EAAQ,GAAK/C,EAAa,WACjB8P,IACT/M,EAAQ,EAAI0C,GAAoBqK,CAAe,IAI5C,CACL,EAAGhJ,EAAK,KAAOlC,EAAO,WAAa7B,EAAQ,EAC3C,EAAG+D,EAAK,IAAMlC,EAAO,UAAY7B,EAAQ,EACzC,MAAO+D,EAAK,MACZ,OAAQA,EAAK,MACd,CACH,CCvDA,SAASiJ,GAAMC,EAAW,CACxB,IAAIC,EAAM,IAAI,IACVC,EAAU,IAAI,IACdC,EAAS,CAAE,EACfH,EAAU,QAAQ,SAAUI,EAAU,CACpCH,EAAI,IAAIG,EAAS,KAAMA,CAAQ,CACnC,CAAG,EAED,SAASC,EAAKD,EAAU,CACtBF,EAAQ,IAAIE,EAAS,IAAI,EACzB,IAAIE,EAAW,GAAG,OAAOF,EAAS,UAAY,GAAIA,EAAS,kBAAoB,EAAE,EACjFE,EAAS,QAAQ,SAAUC,EAAK,CAC9B,GAAI,CAACL,EAAQ,IAAIK,CAAG,EAAG,CACrB,IAAIC,EAAcP,EAAI,IAAIM,CAAG,EAEzBC,GACFH,EAAKG,CAAW,CAE1B,CACA,CAAK,EACDL,EAAO,KAAKC,CAAQ,CACxB,CAEE,OAAAJ,EAAU,QAAQ,SAAUI,EAAU,CAC/BF,EAAQ,IAAIE,EAAS,IAAI,GAE5BC,EAAKD,CAAQ,CAEnB,CAAG,EACMD,CACT,CAEe,SAASM,GAAeT,EAAW,CAEhD,IAAIU,EAAmBX,GAAMC,CAAS,EAEtC,OAAO/T,GAAe,OAAO,SAAUZ,EAAKsV,EAAO,CACjD,OAAOtV,EAAI,OAAOqV,EAAiB,OAAO,SAAUN,EAAU,CAC5D,OAAOA,EAAS,QAAUO,CAChC,CAAK,CAAC,CACH,EAAE,EAAE,CACP,CC3Ce,SAASC,GAASC,EAAI,CACnC,IAAIC,EACJ,OAAO,UAAY,CACjB,OAAKA,IACHA,EAAU,IAAI,QAAQ,SAAUC,EAAS,CACvC,QAAQ,UAAU,KAAK,UAAY,CACjCD,EAAU,OACVC,EAAQF,EAAE,CAAE,CACtB,CAAS,CACT,CAAO,GAGIC,CACR,CACH,CCde,SAASE,GAAYhB,EAAW,CAC7C,IAAIiB,EAASjB,EAAU,OAAO,SAAUiB,EAAQC,EAAS,CACvD,IAAIC,EAAWF,EAAOC,EAAQ,IAAI,EAClC,OAAAD,EAAOC,EAAQ,IAAI,EAAIC,EAAW,OAAO,OAAO,CAAA,EAAIA,EAAUD,EAAS,CACrE,QAAS,OAAO,OAAO,CAAA,EAAIC,EAAS,QAASD,EAAQ,OAAO,EAC5D,KAAM,OAAO,OAAO,CAAA,EAAIC,EAAS,KAAMD,EAAQ,IAAI,CACpD,CAAA,EAAIA,EACED,CACR,EAAE,CAAE,CAAA,EAEL,OAAO,OAAO,KAAKA,CAAM,EAAE,IAAI,SAAUpQ,EAAK,CAC5C,OAAOoQ,EAAOpQ,CAAG,CACrB,CAAG,CACH,CCJA,IAAIuQ,GAAkB,CACpB,UAAW,SACX,UAAW,CAAE,EACb,SAAU,UACZ,EAEA,SAASC,IAAmB,CAC1B,QAASvC,EAAO,UAAU,OAAQwC,EAAO,IAAI,MAAMxC,CAAI,EAAGyC,EAAO,EAAGA,EAAOzC,EAAMyC,IAC/ED,EAAKC,CAAI,EAAI,UAAUA,CAAI,EAG7B,MAAO,CAACD,EAAK,KAAK,SAAUnV,EAAS,CACnC,MAAO,EAAEA,GAAW,OAAOA,EAAQ,uBAA0B,WACjE,CAAG,CACH,CAEO,SAASqV,GAAgBC,EAAkB,CAC5CA,IAAqB,SACvBA,EAAmB,CAAE,GAGvB,IAAIC,EAAoBD,EACpBE,EAAwBD,EAAkB,iBAC1CE,EAAmBD,IAA0B,OAAS,CAAA,EAAKA,EAC3DE,EAAyBH,EAAkB,eAC3CI,EAAiBD,IAA2B,OAAST,GAAkBS,EAC3E,OAAO,SAAsB1W,EAAWD,EAAQgG,EAAS,CACnDA,IAAY,SACdA,EAAU4Q,GAGZ,IAAIjV,EAAQ,CACV,UAAW,SACX,iBAAkB,CAAE,EACpB,QAAS,OAAO,OAAO,CAAA,EAAIuU,GAAiBU,CAAc,EAC1D,cAAe,CAAE,EACjB,SAAU,CACR,UAAW3W,EACX,OAAQD,CACT,EACD,WAAY,CAAE,EACd,OAAQ,CAAA,CACT,EACG6W,EAAmB,CAAE,EACrBC,EAAc,GACdtN,EAAW,CACb,MAAO7H,EACP,WAAY,SAAoBoV,EAAkB,CAChD,IAAI/Q,EAAU,OAAO+Q,GAAqB,WAAaA,EAAiBpV,EAAM,OAAO,EAAIoV,EACzFC,EAAwB,EACxBrV,EAAM,QAAU,OAAO,OAAO,CAAE,EAAEiV,EAAgBjV,EAAM,QAASqE,CAAO,EACxErE,EAAM,cAAgB,CACpB,UAAWN,EAAUpB,CAAS,EAAIqL,GAAkBrL,CAAS,EAAIA,EAAU,eAAiBqL,GAAkBrL,EAAU,cAAc,EAAI,CAAE,EAC5I,OAAQqL,GAAkBtL,CAAM,CAC1C,EAGQ,IAAIwV,EAAmBD,GAAeO,GAAY,GAAG,OAAOY,EAAkB/U,EAAM,QAAQ,SAAS,CAAC,CAAC,EAEvG,OAAAA,EAAM,iBAAmB6T,EAAiB,OAAO,SAAUyB,EAAG,CAC5D,OAAOA,EAAE,OACnB,CAAS,EACDC,EAAoB,EACb1N,EAAS,OAAQ,CACzB,EAMD,YAAa,UAAuB,CAClC,GAAI,CAAAsN,EAIJ,KAAIK,EAAkBxV,EAAM,SACxB1B,EAAYkX,EAAgB,UAC5BnX,EAASmX,EAAgB,OAG7B,GAAKhB,GAAiBlW,EAAWD,CAAM,EAKvC,CAAA2B,EAAM,MAAQ,CACZ,UAAW6S,GAAiBvU,EAAW2E,GAAgB5E,CAAM,EAAG2B,EAAM,QAAQ,WAAa,OAAO,EAClG,OAAQgC,GAAc3D,CAAM,CACtC,EAMQ2B,EAAM,MAAQ,GACdA,EAAM,UAAYA,EAAM,QAAQ,UAKhCA,EAAM,iBAAiB,QAAQ,SAAUuT,EAAU,CACjD,OAAOvT,EAAM,cAAcuT,EAAS,IAAI,EAAI,OAAO,OAAO,CAAA,EAAIA,EAAS,IAAI,CACrF,CAAS,EAED,QAASkC,EAAQ,EAAGA,EAAQzV,EAAM,iBAAiB,OAAQyV,IAAS,CAClE,GAAIzV,EAAM,QAAU,GAAM,CACxBA,EAAM,MAAQ,GACdyV,EAAQ,GACR,QACZ,CAEU,IAAIC,EAAwB1V,EAAM,iBAAiByV,CAAK,EACpDzB,EAAK0B,EAAsB,GAC3BC,EAAyBD,EAAsB,QAC/CtK,EAAWuK,IAA2B,OAAS,CAAA,EAAKA,EACpD1V,EAAOyV,EAAsB,KAE7B,OAAO1B,GAAO,aAChBhU,EAAQgU,EAAG,CACT,MAAOhU,EACP,QAASoL,EACT,KAAMnL,EACN,SAAU4H,CACX,CAAA,GAAK7H,EAElB,GACO,EAGD,OAAQ+T,GAAS,UAAY,CAC3B,OAAO,IAAI,QAAQ,SAAUG,EAAS,CACpCrM,EAAS,YAAa,EACtBqM,EAAQlU,CAAK,CACvB,CAAS,CACT,CAAO,EACD,QAAS,UAAmB,CAC1BqV,EAAwB,EACxBF,EAAc,EACtB,CACK,EAED,GAAI,CAACX,GAAiBlW,EAAWD,CAAM,EACrC,OAAOwJ,EAGTA,EAAS,WAAWxD,CAAO,EAAE,KAAK,SAAUrE,EAAO,CAC7C,CAACmV,GAAe9Q,EAAQ,eAC1BA,EAAQ,cAAcrE,CAAK,CAEnC,CAAK,EAMD,SAASuV,GAAqB,CAC5BvV,EAAM,iBAAiB,QAAQ,SAAUD,EAAM,CAC7C,IAAIE,EAAOF,EAAK,KACZ6V,EAAe7V,EAAK,QACpBsE,EAAUuR,IAAiB,OAAS,CAAA,EAAKA,EACzCvV,EAASN,EAAK,OAElB,GAAI,OAAOM,GAAW,WAAY,CAChC,IAAIwV,EAAYxV,EAAO,CACrB,MAAOL,EACP,KAAMC,EACN,SAAU4H,EACV,QAASxD,CACrB,CAAW,EAEGyR,EAAS,UAAkB,CAAE,EAEjCZ,EAAiB,KAAKW,GAAaC,CAAM,CACnD,CACA,CAAO,CACP,CAEI,SAAST,GAAyB,CAChCH,EAAiB,QAAQ,SAAUlB,EAAI,CACrC,OAAOA,EAAI,CACnB,CAAO,EACDkB,EAAmB,CAAE,CAC3B,CAEI,OAAOrN,CACR,CACH,CACO,IAAIkO,GAA4BpB,GAAe,EC/LlDI,GAAmB,CAAC3M,GAAgB7D,GAAe+C,GAAexH,EAAW,EAC7EiW,GAA4BpB,GAAgB,CAC9C,iBAAkBI,EACpB,CAAC,ECEGA,GAAmB,CAAC3M,GAAgB7D,GAAe+C,GAAexH,GAAauF,GAAQ4H,GAAMkD,GAAiBhM,GAAO6K,EAAI,EACzH+G,GAA4BpB,GAAgB,CAC9C,iBAAkBI,EACpB,CAAC", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]}