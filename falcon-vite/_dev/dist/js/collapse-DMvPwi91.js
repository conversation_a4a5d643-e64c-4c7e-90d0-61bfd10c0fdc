var I=Object.defineProperty;var y=Object.getOwnPropertySymbols;var $=Object.prototype.hasOwnProperty,L=Object.prototype.propertyIsEnumerable;var S=(r,t,e)=>t in r?I(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,m=(r,t)=>{for(var e in t||(t={}))$.call(t,e)&&S(r,e,t[e]);if(y)for(var e of y(t))L.call(t,e)&&S(r,e,t[e]);return r};import s from"jquery";import{U as l}from"./util-B8s7WWwa.js";const u="collapse",O="4.6.2",_="bs.collapse",E=`.${_}`,v=".data-api",w=s.fn[u],g="show",p="collapse",A="collapsing",C="collapsed",D="width",q="height",F=`show${E}`,P=`shown${E}`,V=`hide${E}`,H=`hidden${E}`,R=`click${E}${v}`,j=".show, .collapsing",N='[data-toggle="collapse"]',T={toggle:!0,parent:""},M={toggle:"boolean",parent:"(string|element)"};class h{constructor(t,e){this._isTransitioning=!1,this._element=t,this._config=this._getConfig(e),this._triggerArray=[].slice.call(document.querySelectorAll(`[data-toggle="collapse"][href="#${t.id}"],[data-toggle="collapse"][data-target="#${t.id}"]`));const n=[].slice.call(document.querySelectorAll(N));for(let i=0,a=n.length;i<a;i++){const o=n[i],c=l.getSelectorFromElement(o),d=[].slice.call(document.querySelectorAll(c)).filter(f=>f===t);c!==null&&d.length>0&&(this._selector=c,this._triggerArray.push(o))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}static get VERSION(){return O}static get Default(){return T}toggle(){s(this._element).hasClass(g)?this.hide():this.show()}show(){if(this._isTransitioning||s(this._element).hasClass(g))return;let t,e;if(this._parent&&(t=[].slice.call(this._parent.querySelectorAll(j)).filter(f=>typeof this._config.parent=="string"?f.getAttribute("data-parent")===this._config.parent:f.classList.contains(p)),t.length===0&&(t=null)),t&&(e=s(t).not(this._selector).data(_),e&&e._isTransitioning))return;const n=s.Event(F);if(s(this._element).trigger(n),n.isDefaultPrevented())return;t&&(h._jQueryInterface.call(s(t).not(this._selector),"hide"),e||s(t).data(_,null));const i=this._getDimension();s(this._element).removeClass(p).addClass(A),this._element.style[i]=0,this._triggerArray.length&&s(this._triggerArray).removeClass(C).attr("aria-expanded",!0),this.setTransitioning(!0);const a=()=>{s(this._element).removeClass(A).addClass(`${p} ${g}`),this._element.style[i]="",this.setTransitioning(!1),s(this._element).trigger(P)},c=`scroll${i[0].toUpperCase()+i.slice(1)}`,d=l.getTransitionDurationFromElement(this._element);s(this._element).one(l.TRANSITION_END,a).emulateTransitionEnd(d),this._element.style[i]=`${this._element[c]}px`}hide(){if(this._isTransitioning||!s(this._element).hasClass(g))return;const t=s.Event(V);if(s(this._element).trigger(t),t.isDefaultPrevented())return;const e=this._getDimension();this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,l.reflow(this._element),s(this._element).addClass(A).removeClass(`${p} ${g}`);const n=this._triggerArray.length;if(n>0)for(let o=0;o<n;o++){const c=this._triggerArray[o],d=l.getSelectorFromElement(c);d!==null&&(s([].slice.call(document.querySelectorAll(d))).hasClass(g)||s(c).addClass(C).attr("aria-expanded",!1))}this.setTransitioning(!0);const i=()=>{this.setTransitioning(!1),s(this._element).removeClass(A).addClass(p).trigger(H)};this._element.style[e]="";const a=l.getTransitionDurationFromElement(this._element);s(this._element).one(l.TRANSITION_END,i).emulateTransitionEnd(a)}setTransitioning(t){this._isTransitioning=t}dispose(){s.removeData(this._element,_),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null}_getConfig(t){return t=m(m({},T),t),t.toggle=!!t.toggle,l.typeCheckConfig(u,t,M),t}_getDimension(){return s(this._element).hasClass(D)?D:q}_getParent(){let t;l.isElement(this._config.parent)?(t=this._config.parent,typeof this._config.parent.jquery!="undefined"&&(t=this._config.parent[0])):t=document.querySelector(this._config.parent);const e=`[data-toggle="collapse"][data-parent="${this._config.parent}"]`,n=[].slice.call(t.querySelectorAll(e));return s(n).each((i,a)=>{this._addAriaAndCollapsedClass(h._getTargetFromElement(a),[a])}),t}_addAriaAndCollapsedClass(t,e){const n=s(t).hasClass(g);e.length&&s(e).toggleClass(C,!n).attr("aria-expanded",n)}static _getTargetFromElement(t){const e=l.getSelectorFromElement(t);return e?document.querySelector(e):null}static _jQueryInterface(t){return this.each(function(){const e=s(this);let n=e.data(_);const i=m(m(m({},T),e.data()),typeof t=="object"&&t?t:{});if(!n&&i.toggle&&typeof t=="string"&&/show|hide/.test(t)&&(i.toggle=!1),n||(n=new h(this,i),e.data(_,n)),typeof t=="string"){if(typeof n[t]=="undefined")throw new TypeError(`No method named "${t}"`);n[t]()}})}}s(document).on(R,N,function(r){r.currentTarget.tagName==="A"&&r.preventDefault();const t=s(this),e=l.getSelectorFromElement(this),n=[].slice.call(document.querySelectorAll(e));s(n).each(function(){const i=s(this),o=i.data(_)?"toggle":t.data();h._jQueryInterface.call(i,o)})});s.fn[u]=h._jQueryInterface;s.fn[u].Constructor=h;s.fn[u].noConflict=()=>(s.fn[u]=w,h._jQueryInterface);export{h as default};
//# sourceMappingURL=collapse-DMvPwi91.js.map
