import{E as c,f as r,S as l}from"../selector-engine/selector-engine.DUbyXJpR.js";const d=(e,t="hide")=>{const i=`click.dismiss${e.EVENT_KEY}`,s=e.NAME;c.on(document,i,`[data-bs-dismiss="${s}"]`,function(n){if(["A","AREA"].includes(this.tagName)&&n.preventDefault(),r(this))return;const a=l.getElementFromSelector(this)||this.closest(`.${s}`);e.getOrCreateInstance(a)[t]()})};export{d as e};
//# sourceMappingURL=component-functions.MEtW7SSs.js.map
