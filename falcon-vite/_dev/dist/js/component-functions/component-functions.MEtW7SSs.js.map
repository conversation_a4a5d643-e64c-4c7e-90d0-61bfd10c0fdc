{"version": 3, "file": "component-functions.MEtW7SSs.js", "sources": ["../../../node_modules/.pnpm/bootstrap@5.3.6_@popperjs+core@2.11.8/node_modules/bootstrap/js/src/util/component-functions.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport { isDisabled } from './index.js'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = SelectorEngine.getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n"], "names": ["enableDismissTrigger", "component", "method", "clickEvent", "name", "EventHandler", "event", "isDisabled", "target", "SelectorEngine"], "mappings": "iFAWK,MAACA,EAAuB,CAACC,EAAWC,EAAS,SAAW,CAC3D,MAAMC,EAAa,gBAAgBF,EAAU,SAAS,GAChDG,EAAOH,EAAU,KAEvBI,EAAa,GAAG,SAAUF,EAAY,qBAAqBC,CAAI,KAAM,SAAUE,EAAO,CAKpF,GAJI,CAAC,IAAK,MAAM,EAAE,SAAS,KAAK,OAAO,GACrCA,EAAM,eAAc,EAGlBC,EAAW,IAAI,EACjB,OAGF,MAAMC,EAASC,EAAe,uBAAuB,IAAI,GAAK,KAAK,QAAQ,IAAIL,CAAI,EAAE,EACpEH,EAAU,oBAAoBO,CAAM,EAG5CN,CAAM,EAAC,CACjB,CAAA,CACH", "x_google_ignoreList": [0]}