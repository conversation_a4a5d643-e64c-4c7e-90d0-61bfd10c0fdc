{"version": 3, "file": "util-B8s7WWwa.js", "sources": ["../../node_modules/.pnpm/bootstrap@4.6.2_j<PERSON>y@3.7.1_popper.js@1.16.1/node_modules/bootstrap/js/src/util.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): util.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\n\n/**\n * Private TransitionEnd Helpers\n */\n\nconst TRANSITION_END = 'transitionend'\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nfunction toType(obj) {\n  if (obj === null || typeof obj === 'undefined') {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\nfunction getSpecialTransitionEndEvent() {\n  return {\n    bindType: TRANSITION_END,\n    delegateType: TRANSITION_END,\n    handle(event) {\n      if ($(event.target).is(this)) {\n        return event.handleObj.handler.apply(this, arguments) // eslint-disable-line prefer-rest-params\n      }\n\n      return undefined\n    }\n  }\n}\n\nfunction transitionEndEmulator(duration) {\n  let called = false\n\n  $(this).one(Util.TRANSITION_END, () => {\n    called = true\n  })\n\n  setTimeout(() => {\n    if (!called) {\n      Util.triggerTransitionEnd(this)\n    }\n  }, duration)\n\n  return this\n}\n\nfunction setTransitionEndSupport() {\n  $.fn.emulateTransitionEnd = transitionEndEmulator\n  $.event.special[Util.TRANSITION_END] = getSpecialTransitionEndEvent()\n}\n\n/**\n * Public Util API\n */\n\nconst Util = {\n  TRANSITION_END: 'bsTransitionEnd',\n\n  getUID(prefix) {\n    do {\n      // eslint-disable-next-line no-bitwise\n      prefix += ~~(Math.random() * MAX_UID) // \"~~\" acts like a faster Math.floor() here\n    } while (document.getElementById(prefix))\n\n    return prefix\n  },\n\n  getSelectorFromElement(element) {\n    let selector = element.getAttribute('data-target')\n\n    if (!selector || selector === '#') {\n      const hrefAttr = element.getAttribute('href')\n      selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : ''\n    }\n\n    try {\n      return document.querySelector(selector) ? selector : null\n    } catch (_) {\n      return null\n    }\n  },\n\n  getTransitionDurationFromElement(element) {\n    if (!element) {\n      return 0\n    }\n\n    // Get transition-duration of the element\n    let transitionDuration = $(element).css('transition-duration')\n    let transitionDelay = $(element).css('transition-delay')\n\n    const floatTransitionDuration = parseFloat(transitionDuration)\n    const floatTransitionDelay = parseFloat(transitionDelay)\n\n    // Return 0 if element or transition duration is not found\n    if (!floatTransitionDuration && !floatTransitionDelay) {\n      return 0\n    }\n\n    // If multiple durations are defined, take the first\n    transitionDuration = transitionDuration.split(',')[0]\n    transitionDelay = transitionDelay.split(',')[0]\n\n    return (parseFloat(transitionDuration) + parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n  },\n\n  reflow(element) {\n    return element.offsetHeight\n  },\n\n  triggerTransitionEnd(element) {\n    $(element).trigger(TRANSITION_END)\n  },\n\n  supportsTransitionEnd() {\n    return Boolean(TRANSITION_END)\n  },\n\n  isElement(obj) {\n    return (obj[0] || obj).nodeType\n  },\n\n  typeCheckConfig(componentName, config, configTypes) {\n    for (const property in configTypes) {\n      if (Object.prototype.hasOwnProperty.call(configTypes, property)) {\n        const expectedTypes = configTypes[property]\n        const value = config[property]\n        const valueType = value && Util.isElement(value) ?\n          'element' : toType(value)\n\n        if (!new RegExp(expectedTypes).test(valueType)) {\n          throw new Error(\n            `${componentName.toUpperCase()}: ` +\n            `Option \"${property}\" provided type \"${valueType}\" ` +\n            `but expected type \"${expectedTypes}\".`)\n        }\n      }\n    }\n  },\n\n  findShadowRoot(element) {\n    if (!document.documentElement.attachShadow) {\n      return null\n    }\n\n    // Can find the shadow root otherwise it'll return the document\n    if (typeof element.getRootNode === 'function') {\n      const root = element.getRootNode()\n      return root instanceof ShadowRoot ? root : null\n    }\n\n    if (element instanceof ShadowRoot) {\n      return element\n    }\n\n    // when we don't find a shadow root\n    if (!element.parentNode) {\n      return null\n    }\n\n    return Util.findShadowRoot(element.parentNode)\n  },\n\n  jQueryDetection() {\n    if (typeof $ === 'undefined') {\n      throw new TypeError('Bootstrap\\'s JavaScript requires jQuery. jQuery must be included before Bootstrap\\'s JavaScript.')\n    }\n\n    const version = $.fn.jquery.split(' ')[0].split('.')\n    const minMajor = 1\n    const ltMajor = 2\n    const minMinor = 9\n    const minPatch = 1\n    const maxMajor = 4\n\n    if (version[0] < ltMajor && version[1] < minMinor || version[0] === minMajor && version[1] === minMinor && version[2] < minPatch || version[0] >= maxMajor) {\n      throw new Error('Bootstrap\\'s JavaScript requires at least jQuery v1.9.1 but less than v4.0.0')\n    }\n  }\n}\n\nUtil.jQueryDetection()\nsetTransitionEndSupport()\n\nexport default Util\n"], "names": ["TRANSITION_END", "MAX_UID", "MILLISECONDS_MULTIPLIER", "toType", "obj", "getSpecialTransitionEndEvent", "event", "$", "transitionEndEmulator", "duration", "called", "<PERSON><PERSON>", "setTransitionEndSupport", "prefix", "element", "selector", "hrefAttr", "_", "transitionDuration", "transitionDelay", "floatTransitionDuration", "floatTransitionDelay", "componentName", "config", "configTypes", "property", "expectedTypes", "value", "valueType", "root", "version", "min<PERSON><PERSON><PERSON>", "ltMajor", "minMinor"], "mappings": "sBAaA,MAAMA,EAAiB,gBACjBC,EAAU,IACVC,EAA0B,IAGhC,SAASC,EAAOC,EAAK,CACnB,OAAIA,IAAQ,MAAQ,OAAOA,GAAQ,YAC1B,GAAGA,CAAG,GAGR,CAAE,EAAC,SAAS,KAAKA,CAAG,EAAE,MAAM,aAAa,EAAE,CAAC,EAAE,YAAW,CAClE,CAEA,SAASC,GAA+B,CACtC,MAAO,CACL,SAAUL,EACV,aAAcA,EACd,OAAOM,EAAO,CACZ,GAAIC,EAAED,EAAM,MAAM,EAAE,GAAG,IAAI,EACzB,OAAOA,EAAM,UAAU,QAAQ,MAAM,KAAM,SAAS,CAI5D,CACA,CACA,CAEA,SAASE,EAAsBC,EAAU,CACvC,IAAIC,EAAS,GAEb,OAAAH,EAAE,IAAI,EAAE,IAAII,EAAK,eAAgB,IAAM,CACrCD,EAAS,EACV,CAAA,EAED,WAAW,IAAM,CACVA,GACHC,EAAK,qBAAqB,IAAI,CAEpC,EAAKF,CAAQ,EAEJ,IACT,CAEA,SAASG,GAA0B,CACjCL,EAAE,GAAG,qBAAuBC,EAC5BD,EAAE,MAAM,QAAQI,EAAK,cAAc,EAAIN,EAA4B,CACrE,CAMK,MAACM,EAAO,CACX,eAAgB,kBAEhB,OAAOE,EAAQ,CACb,GAEEA,GAAU,CAAC,EAAE,KAAK,OAAM,EAAKZ,SACtB,SAAS,eAAeY,CAAM,GAEvC,OAAOA,CACR,EAED,uBAAuBC,EAAS,CAC9B,IAAIC,EAAWD,EAAQ,aAAa,aAAa,EAEjD,GAAI,CAACC,GAAYA,IAAa,IAAK,CACjC,MAAMC,EAAWF,EAAQ,aAAa,MAAM,EAC5CC,EAAWC,GAAYA,IAAa,IAAMA,EAAS,KAAI,EAAK,EAClE,CAEI,GAAI,CACF,OAAO,SAAS,cAAcD,CAAQ,EAAIA,EAAW,IACtD,OAAQE,EAAG,CACV,OAAO,IACb,CACG,EAED,iCAAiCH,EAAS,CACxC,GAAI,CAACA,EACH,MAAO,GAIT,IAAII,EAAqBX,EAAEO,CAAO,EAAE,IAAI,qBAAqB,EACzDK,EAAkBZ,EAAEO,CAAO,EAAE,IAAI,kBAAkB,EAEvD,MAAMM,EAA0B,WAAWF,CAAkB,EACvDG,EAAuB,WAAWF,CAAe,EAGvD,MAAI,CAACC,GAA2B,CAACC,EACxB,GAITH,EAAqBA,EAAmB,MAAM,GAAG,EAAE,CAAC,EACpDC,EAAkBA,EAAgB,MAAM,GAAG,EAAE,CAAC,GAEtC,WAAWD,CAAkB,EAAI,WAAWC,CAAe,GAAKjB,EACzE,EAED,OAAOY,EAAS,CACd,OAAOA,EAAQ,YAChB,EAED,qBAAqBA,EAAS,CAC5BP,EAAEO,CAAO,EAAE,QAAQd,CAAc,CAClC,EAED,uBAAwB,CACtB,MAAO,EAAQA,CAChB,EAED,UAAUI,EAAK,CACb,OAAQA,EAAI,CAAC,GAAKA,GAAK,QACxB,EAED,gBAAgBkB,EAAeC,EAAQC,EAAa,CAClD,UAAWC,KAAYD,EACrB,GAAI,OAAO,UAAU,eAAe,KAAKA,EAAaC,CAAQ,EAAG,CAC/D,MAAMC,EAAgBF,EAAYC,CAAQ,EACpCE,EAAQJ,EAAOE,CAAQ,EACvBG,EAAYD,GAAShB,EAAK,UAAUgB,CAAK,EAC7C,UAAYxB,EAAOwB,CAAK,EAE1B,GAAI,CAAC,IAAI,OAAOD,CAAa,EAAE,KAAKE,CAAS,EAC3C,MAAM,IAAI,MACR,GAAGN,EAAc,YAAa,CAAA,aACnBG,CAAQ,oBAAoBG,CAAS,wBAC1BF,CAAa,IAAI,CAEnD,CAEG,EAED,eAAeZ,EAAS,CACtB,GAAI,CAAC,SAAS,gBAAgB,aAC5B,OAAO,KAIT,GAAI,OAAOA,EAAQ,aAAgB,WAAY,CAC7C,MAAMe,EAAOf,EAAQ,YAAW,EAChC,OAAOe,aAAgB,WAAaA,EAAO,IACjD,CAEI,OAAIf,aAAmB,WACdA,EAIJA,EAAQ,WAINH,EAAK,eAAeG,EAAQ,UAAU,EAHpC,IAIV,EAED,iBAAkB,CAChB,GAAI,OAAOP,GAAM,YACf,MAAM,IAAI,UAAU,gGAAkG,EAGxH,MAAMuB,EAAUvB,EAAE,GAAG,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAC7CwB,EAAW,EACXC,EAAU,EACVC,EAAW,EAIjB,GAAIH,EAAQ,CAAC,EAAIE,GAAWF,EAAQ,CAAC,EAAIG,GAAYH,EAAQ,CAAC,IAAMC,GAAYD,EAAQ,CAAC,IAAMG,GAAYH,EAAQ,CAAC,EAHnG,GAGmHA,EAAQ,CAAC,GAF5H,EAGf,MAAM,IAAI,MAAM,6EAA8E,CAEpG,CACA,EAEAnB,EAAK,gBAAe,EACpBC,EAAuB", "x_google_ignoreList": [0]}