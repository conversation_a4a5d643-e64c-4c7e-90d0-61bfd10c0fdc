{"version": 3, "file": "scrollspy--h1QgKEA.js", "sources": ["../../node_modules/.pnpm/bootstrap@4.6.2_jquery@3.7.1_popper.js@1.16.1/node_modules/bootstrap/js/src/scrollspy.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\nconst SELECTOR_DATA_SPY = '[data-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_ITEMS = '.dropdown-item'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS},` +\n                          `${this._config.target} ${SELECTOR_LIST_ITEMS},` +\n                          `${this._config.target} ${SELECTOR_DROPDOWN_ITEMS}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    $(this._scrollElement).on(EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET : METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod : this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() : 0\n\n    this._offsets = []\n    this._targets = []\n\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = [].slice.call(document.querySelectorAll(this._selector))\n\n    targets\n      .map(element => {\n        let target\n        const targetSelector = Util.getSelectorFromElement(element)\n\n        if (targetSelector) {\n          target = document.querySelector(targetSelector)\n        }\n\n        if (target) {\n          const targetBCR = target.getBoundingClientRect()\n          if (targetBCR.width || targetBCR.height) {\n            // TODO (fat): remove sketch reliance on jQuery position/offset\n            return [\n              $(target)[offsetMethod]().top + offsetBase,\n              targetSelector\n            ]\n          }\n        }\n\n        return null\n      })\n      .filter(Boolean)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    $(this._scrollElement).off(EVENT_KEY)\n\n    this._element = null\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && Util.isElement(config.target)) {\n      let id = $(config.target).attr('id')\n      if (!id) {\n        id = Util.getUID(NAME)\n        $(config.target).attr('id', id)\n      }\n\n      config.target = `#${id}`\n    }\n\n    Util.typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset : this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight : this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector\n      .split(',')\n      .map(selector => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const $link = $([].slice.call(document.querySelectorAll(queries.join(','))))\n\n    if ($link.hasClass(CLASS_NAME_DROPDOWN_ITEM)) {\n      $link.closest(SELECTOR_DROPDOWN)\n        .find(SELECTOR_DROPDOWN_TOGGLE)\n        .addClass(CLASS_NAME_ACTIVE)\n      $link.addClass(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      $link.addClass(CLASS_NAME_ACTIVE)\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      $link.parents(SELECTOR_NAV_LIST_GROUP)\n        .prev(`${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n        .addClass(CLASS_NAME_ACTIVE)\n      // Handle special case when .nav-link is inside .nav-item\n      $link.parents(SELECTOR_NAV_LIST_GROUP)\n        .prev(SELECTOR_NAV_ITEMS)\n        .children(SELECTOR_NAV_LINKS)\n        .addClass(CLASS_NAME_ACTIVE)\n    }\n\n    $(this._scrollElement).trigger(EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    [].slice.call(document.querySelectorAll(this._selector))\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(window).on(EVENT_LOAD_DATA_API, () => {\n  const scrollSpys = [].slice.call(document.querySelectorAll(SELECTOR_DATA_SPY))\n  const scrollSpysLength = scrollSpys.length\n\n  for (let i = scrollSpysLength; i--;) {\n    const $spy = $(scrollSpys[i])\n    ScrollSpy._jQueryInterface.call($spy, $spy.data())\n  }\n})\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = ScrollSpy._jQueryInterface\n$.fn[NAME].Constructor = ScrollSpy\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return ScrollSpy._jQueryInterface\n}\n\nexport default ScrollSpy\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "CLASS_NAME_DROPDOWN_ITEM", "CLASS_NAME_ACTIVE", "EVENT_ACTIVATE", "EVENT_SCROLL", "EVENT_LOAD_DATA_API", "METHOD_OFFSET", "METHOD_POSITION", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_ITEMS", "SELECTOR_DROPDOWN_TOGGLE", "<PERSON><PERSON><PERSON>", "DefaultType", "ScrollSpy", "element", "config", "event", "autoMethod", "offsetMethod", "offsetBase", "target", "targetSelector", "<PERSON><PERSON>", "targetBCR", "a", "b", "item", "__spreadValues", "id", "scrollTop", "scrollHeight", "maxScroll", "i", "queries", "selector", "$link", "node", "data", "_config", "scrollSpys", "scrollSpysLength", "$spy"], "mappings": "sZAcA,MAAMA,EAAO,YACPC,EAAU,QACVC,EAAW,eACXC,EAAY,IAAID,CAAQ,GACxBE,EAAe,YACfC,EAAqBC,EAAE,GAAGN,CAAI,EAE9BO,EAA2B,gBAC3BC,EAAoB,SAEpBC,EAAiB,WAAWN,CAAS,GACrCO,EAAe,SAASP,CAAS,GACjCQ,EAAsB,OAAOR,CAAS,GAAGC,CAAY,GAErDQ,EAAgB,SAChBC,EAAkB,WAElBC,EAAoB,sBACpBC,EAA0B,oBAC1BC,EAAqB,YACrBC,EAAqB,YACrBC,EAAsB,mBACtBC,EAAoB,YACpBC,EAA0B,iBAC1BC,EAA2B,mBAE3BC,EAAU,CACd,OAAQ,GACR,OAAQ,OACR,OAAQ,EACV,EAEMC,EAAc,CAClB,OAAQ,SACR,OAAQ,SACR,OAAQ,kBACV,EAMA,MAAMC,CAAU,CACd,YAAYC,EAASC,EAAQ,CAC3B,KAAK,SAAWD,EAChB,KAAK,eAAiBA,EAAQ,UAAY,OAAS,OAASA,EAC5D,KAAK,QAAU,KAAK,WAAWC,CAAM,EACrC,KAAK,UAAY,GAAG,KAAK,QAAQ,MAAM,IAAIV,CAAkB,IACpC,KAAK,QAAQ,MAAM,IAAIE,CAAmB,IAC1C,KAAK,QAAQ,MAAM,IAAIE,CAAuB,GACvE,KAAK,SAAW,CAAA,EAChB,KAAK,SAAW,CAAA,EAChB,KAAK,cAAgB,KACrB,KAAK,cAAgB,EAErBd,EAAE,KAAK,cAAc,EAAE,GAAGI,EAAciB,GAAS,KAAK,SAASA,CAAK,CAAC,EAErE,KAAK,QAAO,EACZ,KAAK,SAAQ,CACjB,CAGE,WAAW,SAAU,CACnB,OAAO1B,CACX,CAEE,WAAW,SAAU,CACnB,OAAOqB,CACX,CAGE,SAAU,CACR,MAAMM,EAAa,KAAK,iBAAmB,KAAK,eAAe,OAC7DhB,EAAgBC,EAEZgB,EAAe,KAAK,QAAQ,SAAW,OAC3CD,EAAa,KAAK,QAAQ,OAEtBE,EAAaD,IAAiBhB,EAClC,KAAK,cAAa,EAAK,EAEzB,KAAK,SAAW,CAAA,EAChB,KAAK,SAAW,CAAA,EAEhB,KAAK,cAAgB,KAAK,iBAAgB,EAE1B,CAAE,EAAC,MAAM,KAAK,SAAS,iBAAiB,KAAK,SAAS,CAAC,EAGpE,IAAIY,GAAW,CACd,IAAIM,EACJ,MAAMC,EAAiBC,EAAK,uBAAuBR,CAAO,EAM1D,GAJIO,IACFD,EAAS,SAAS,cAAcC,CAAc,GAG5CD,EAAQ,CACV,MAAMG,EAAYH,EAAO,sBAAqB,EAC9C,GAAIG,EAAU,OAASA,EAAU,OAE/B,MAAO,CACL5B,EAAEyB,CAAM,EAAEF,CAAY,EAAG,EAAC,IAAMC,EAChCE,CACd,CAEA,CAEQ,OAAO,IACR,CAAA,EACA,OAAO,OAAO,EACd,KAAK,CAACG,EAAGC,IAAMD,EAAE,CAAC,EAAIC,EAAE,CAAC,CAAC,EAC1B,QAAQC,GAAQ,CACf,KAAK,SAAS,KAAKA,EAAK,CAAC,CAAC,EAC1B,KAAK,SAAS,KAAKA,EAAK,CAAC,CAAC,CAC3B,CAAA,CACP,CAEE,SAAU,CACR/B,EAAE,WAAW,KAAK,SAAUJ,CAAQ,EACpCI,EAAE,KAAK,cAAc,EAAE,IAAIH,CAAS,EAEpC,KAAK,SAAW,KAChB,KAAK,eAAiB,KACtB,KAAK,QAAU,KACf,KAAK,UAAY,KACjB,KAAK,SAAW,KAChB,KAAK,SAAW,KAChB,KAAK,cAAgB,KACrB,KAAK,cAAgB,IACzB,CAGE,WAAWuB,EAAQ,CAMjB,GALAA,EAASY,IAAA,GACJhB,GACC,OAAOI,GAAW,UAAYA,EAASA,EAAS,CAAE,GAGpD,OAAOA,EAAO,QAAW,UAAYO,EAAK,UAAUP,EAAO,MAAM,EAAG,CACtE,IAAIa,EAAKjC,EAAEoB,EAAO,MAAM,EAAE,KAAK,IAAI,EAC9Ba,IACHA,EAAKN,EAAK,OAAOjC,CAAI,EACrBM,EAAEoB,EAAO,MAAM,EAAE,KAAK,KAAMa,CAAE,GAGhCb,EAAO,OAAS,IAAIa,CAAE,EAC5B,CAEI,OAAAN,EAAK,gBAAgBjC,EAAM0B,EAAQH,CAAW,EAEvCG,CACX,CAEE,eAAgB,CACd,OAAO,KAAK,iBAAmB,OAC7B,KAAK,eAAe,YAAc,KAAK,eAAe,SAC5D,CAEE,kBAAmB,CACjB,OAAO,KAAK,eAAe,cAAgB,KAAK,IAC9C,SAAS,KAAK,aACd,SAAS,gBAAgB,YAC/B,CACA,CAEE,kBAAmB,CACjB,OAAO,KAAK,iBAAmB,OAC7B,OAAO,YAAc,KAAK,eAAe,sBAAqB,EAAG,MACvE,CAEE,UAAW,CACT,MAAMc,EAAY,KAAK,cAAa,EAAK,KAAK,QAAQ,OAChDC,EAAe,KAAK,iBAAgB,EACpCC,EAAY,KAAK,QAAQ,OAASD,EAAe,KAAK,iBAAgB,EAM5E,GAJI,KAAK,gBAAkBA,GACzB,KAAK,QAAO,EAGVD,GAAaE,EAAW,CAC1B,MAAMX,EAAS,KAAK,SAAS,KAAK,SAAS,OAAS,CAAC,EAEjD,KAAK,gBAAkBA,GACzB,KAAK,UAAUA,CAAM,EAGvB,MACN,CAEI,GAAI,KAAK,eAAiBS,EAAY,KAAK,SAAS,CAAC,GAAK,KAAK,SAAS,CAAC,EAAI,EAAG,CAC9E,KAAK,cAAgB,KACrB,KAAK,OAAM,EACX,MACN,CAEI,QAASG,EAAI,KAAK,SAAS,OAAQA,KACV,KAAK,gBAAkB,KAAK,SAASA,CAAC,GACzDH,GAAa,KAAK,SAASG,CAAC,IAC3B,OAAO,KAAK,SAASA,EAAI,CAAC,GAAM,aAC7BH,EAAY,KAAK,SAASG,EAAI,CAAC,IAGrC,KAAK,UAAU,KAAK,SAASA,CAAC,CAAC,CAGvC,CAEE,UAAUZ,EAAQ,CAChB,KAAK,cAAgBA,EAErB,KAAK,OAAM,EAEX,MAAMa,EAAU,KAAK,UAClB,MAAM,GAAG,EACT,IAAIC,GAAY,GAAGA,CAAQ,iBAAiBd,CAAM,MAAMc,CAAQ,UAAUd,CAAM,IAAI,EAEjFe,EAAQxC,EAAE,CAAE,EAAC,MAAM,KAAK,SAAS,iBAAiBsC,EAAQ,KAAK,GAAG,CAAC,CAAC,CAAC,EAEvEE,EAAM,SAASvC,CAAwB,GACzCuC,EAAM,QAAQ3B,CAAiB,EAC5B,KAAKE,CAAwB,EAC7B,SAASb,CAAiB,EAC7BsC,EAAM,SAAStC,CAAiB,IAGhCsC,EAAM,SAAStC,CAAiB,EAGhCsC,EAAM,QAAQ/B,CAAuB,EAClC,KAAK,GAAGC,CAAkB,KAAKE,CAAmB,EAAE,EACpD,SAASV,CAAiB,EAE7BsC,EAAM,QAAQ/B,CAAuB,EAClC,KAAKE,CAAkB,EACvB,SAASD,CAAkB,EAC3B,SAASR,CAAiB,GAG/BF,EAAE,KAAK,cAAc,EAAE,QAAQG,EAAgB,CAC7C,cAAesB,CAChB,CAAA,CACL,CAEE,QAAS,CACP,CAAE,EAAC,MAAM,KAAK,SAAS,iBAAiB,KAAK,SAAS,CAAC,EACpD,OAAOgB,GAAQA,EAAK,UAAU,SAASvC,CAAiB,CAAC,EACzD,QAAQuC,GAAQA,EAAK,UAAU,OAAOvC,CAAiB,CAAC,CAC/D,CAGE,OAAO,iBAAiBkB,EAAQ,CAC9B,OAAO,KAAK,KAAK,UAAY,CAC3B,IAAIsB,EAAO1C,EAAE,IAAI,EAAE,KAAKJ,CAAQ,EAChC,MAAM+C,EAAU,OAAOvB,GAAW,UAAYA,EAO9C,GALKsB,IACHA,EAAO,IAAIxB,EAAU,KAAMyB,CAAO,EAClC3C,EAAE,IAAI,EAAE,KAAKJ,EAAU8C,CAAI,GAGzB,OAAOtB,GAAW,SAAU,CAC9B,GAAI,OAAOsB,EAAKtB,CAAM,GAAM,YAC1B,MAAM,IAAI,UAAU,oBAAoBA,CAAM,GAAG,EAGnDsB,EAAKtB,CAAM,EAAC,CACpB,CACK,CAAA,CACL,CACA,CAMApB,EAAE,MAAM,EAAE,GAAGK,EAAqB,IAAM,CACtC,MAAMuC,EAAa,CAAA,EAAG,MAAM,KAAK,SAAS,iBAAiBpC,CAAiB,CAAC,EACvEqC,EAAmBD,EAAW,OAEpC,QAASP,EAAIQ,EAAkBR,KAAM,CACnC,MAAMS,EAAO9C,EAAE4C,EAAWP,CAAC,CAAC,EAC5BnB,EAAU,iBAAiB,KAAK4B,EAAMA,EAAK,KAAM,CAAA,CACrD,CACA,CAAC,EAMD9C,EAAE,GAAGN,CAAI,EAAIwB,EAAU,iBACvBlB,EAAE,GAAGN,CAAI,EAAE,YAAcwB,EACzBlB,EAAE,GAAGN,CAAI,EAAE,WAAa,KACtBM,EAAE,GAAGN,CAAI,EAAIK,EACNmB,EAAU", "x_google_ignoreList": [0]}