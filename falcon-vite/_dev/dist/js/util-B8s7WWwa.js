import o from"jquery";const u="transitionend",c=1e6,d=1e3;function p(t){return t===null||typeof t=="undefined"?`${t}`:{}.toString.call(t).match(/\s([a-z]+)/i)[1].toLowerCase()}function f(){return{bindType:u,delegateType:u,handle(t){if(o(t.target).is(this))return t.handleObj.handler.apply(this,arguments)}}}function h(t){let e=!1;return o(this).one(i.TRANSITION_END,()=>{e=!0}),setTimeout(()=>{e||i.triggerTransitionEnd(this)},t),this}function y(){o.fn.emulateTransitionEnd=h,o.event.special[i.TRANSITION_END]=f()}const i={TRANSITION_END:"bsTransitionEnd",getUID(t){do t+=~~(Math.random()*c);while(document.getElementById(t));return t},getSelectorFromElement(t){let e=t.getAttribute("data-target");if(!e||e==="#"){const n=t.getAttribute("href");e=n&&n!=="#"?n.trim():""}try{return document.querySelector(e)?e:null}catch(n){return null}},getTransitionDurationFromElement(t){if(!t)return 0;let e=o(t).css("transition-duration"),n=o(t).css("transition-delay");const r=parseFloat(e),a=parseFloat(n);return!r&&!a?0:(e=e.split(",")[0],n=n.split(",")[0],(parseFloat(e)+parseFloat(n))*d)},reflow(t){return t.offsetHeight},triggerTransitionEnd(t){o(t).trigger(u)},supportsTransitionEnd(){return!!u},isElement(t){return(t[0]||t).nodeType},typeCheckConfig(t,e,n){for(const r in n)if(Object.prototype.hasOwnProperty.call(n,r)){const a=n[r],s=e[r],l=s&&i.isElement(s)?"element":p(s);if(!new RegExp(a).test(l))throw new Error(`${t.toUpperCase()}: Option "${r}" provided type "${l}" but expected type "${a}".`)}},findShadowRoot(t){if(!document.documentElement.attachShadow)return null;if(typeof t.getRootNode=="function"){const e=t.getRootNode();return e instanceof ShadowRoot?e:null}return t instanceof ShadowRoot?t:t.parentNode?i.findShadowRoot(t.parentNode):null},jQueryDetection(){if(typeof o=="undefined")throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");const t=o.fn.jquery.split(" ")[0].split("."),e=1,n=2,r=9;if(t[0]<n&&t[1]<r||t[0]===e&&t[1]===r&&t[2]<1||t[0]>=4)throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};i.jQueryDetection();y();export{i as U};
//# sourceMappingURL=util-B8s7WWwa.js.map
