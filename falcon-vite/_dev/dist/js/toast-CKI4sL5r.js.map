{"version": 3, "file": "toast-CKI4sL5r.js", "sources": ["../../node_modules/.pnpm/bootstrap@4.6.2_j<PERSON>y@3.7.1_popper.js@1.16.1/node_modules/bootstrap/js/src/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"toast\"]'\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 500\n}\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\n/**\n * Class definition\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n  show() {\n    const showEvent = $.Event(EVENT_SHOW)\n\n    $(this._element).trigger(showEvent)\n    if (showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      $(this._element).trigger(EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    Util.reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = $.Event(EVENT_HIDE)\n\n    $(this._element).trigger(hideEvent)\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._close()\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    $(this._element).off(EVENT_CLICK_DISMISS)\n\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n    this._config = null\n  }\n\n  // Private\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...$(this._element).data(),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _setListeners() {\n    $(this._element).on(EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _close() {\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      $(this._element).trigger(EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data = $element.data(DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Toast._jQueryInterface\n$.fn[NAME].Constructor = Toast\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Toast._jQueryInterface\n}\n\nexport default Toast\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "CLASS_NAME_FADE", "CLASS_NAME_HIDE", "CLASS_NAME_SHOW", "CLASS_NAME_SHOWING", "EVENT_CLICK_DISMISS", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "SELECTOR_DATA_DISMISS", "<PERSON><PERSON><PERSON>", "DefaultType", "Toast", "element", "config", "showEvent", "complete", "<PERSON><PERSON>", "transitionDuration", "hideEvent", "__spreadValues", "$element", "data", "_config"], "mappings": "sZAcA,MAAMA,EAAO,QACPC,EAAU,QACVC,EAAW,WACXC,EAAY,IAAID,CAAQ,GACxBE,EAAqBC,EAAE,GAAGL,CAAI,EAE9BM,EAAkB,OAClBC,EAAkB,OAClBC,EAAkB,OAClBC,EAAqB,UAErBC,EAAsB,gBAAgBP,CAAS,GAC/CQ,EAAa,OAAOR,CAAS,GAC7BS,EAAe,SAAST,CAAS,GACjCU,EAAa,OAAOV,CAAS,GAC7BW,EAAc,QAAQX,CAAS,GAE/BY,EAAwB,yBAExBC,EAAU,CACd,UAAW,GACX,SAAU,GACV,MAAO,GACT,EAEMC,EAAc,CAClB,UAAW,UACX,SAAU,UACV,MAAO,QACT,EAMA,MAAMC,CAAM,CACV,YAAYC,EAASC,EAAQ,CAC3B,KAAK,SAAWD,EAChB,KAAK,QAAU,KAAK,WAAWC,CAAM,EACrC,KAAK,SAAW,KAChB,KAAK,cAAa,CACtB,CAGE,WAAW,SAAU,CACnB,OAAOnB,CACX,CAEE,WAAW,aAAc,CACvB,OAAOgB,CACX,CAEE,WAAW,SAAU,CACnB,OAAOD,CACX,CAGE,MAAO,CACL,MAAMK,EAAYhB,EAAE,MAAMQ,CAAU,EAGpC,GADAR,EAAE,KAAK,QAAQ,EAAE,QAAQgB,CAAS,EAC9BA,EAAU,qBACZ,OAGF,KAAK,cAAa,EAEd,KAAK,QAAQ,WACf,KAAK,SAAS,UAAU,IAAIf,CAAe,EAG7C,MAAMgB,EAAW,IAAM,CACrB,KAAK,SAAS,UAAU,OAAOb,CAAkB,EACjD,KAAK,SAAS,UAAU,IAAID,CAAe,EAE3CH,EAAE,KAAK,QAAQ,EAAE,QAAQS,CAAW,EAEhC,KAAK,QAAQ,WACf,KAAK,SAAW,WAAW,IAAM,CAC/B,KAAK,KAAI,CACnB,EAAW,KAAK,QAAQ,KAAK,EAE7B,EAKI,GAHA,KAAK,SAAS,UAAU,OAAOP,CAAe,EAC9CgB,EAAK,OAAO,KAAK,QAAQ,EACzB,KAAK,SAAS,UAAU,IAAId,CAAkB,EAC1C,KAAK,QAAQ,UAAW,CAC1B,MAAMe,EAAqBD,EAAK,iCAAiC,KAAK,QAAQ,EAE9ElB,EAAE,KAAK,QAAQ,EACZ,IAAIkB,EAAK,eAAgBD,CAAQ,EACjC,qBAAqBE,CAAkB,CAChD,MACMF,EAAQ,CAEd,CAEE,MAAO,CACL,GAAI,CAAC,KAAK,SAAS,UAAU,SAASd,CAAe,EACnD,OAGF,MAAMiB,EAAYpB,EAAE,MAAMM,CAAU,EAEpCN,EAAE,KAAK,QAAQ,EAAE,QAAQoB,CAAS,EAC9B,CAAAA,EAAU,sBAId,KAAK,OAAM,CACf,CAEE,SAAU,CACR,KAAK,cAAa,EAEd,KAAK,SAAS,UAAU,SAASjB,CAAe,GAClD,KAAK,SAAS,UAAU,OAAOA,CAAe,EAGhDH,EAAE,KAAK,QAAQ,EAAE,IAAIK,CAAmB,EAExCL,EAAE,WAAW,KAAK,SAAUH,CAAQ,EACpC,KAAK,SAAW,KAChB,KAAK,QAAU,IACnB,CAGE,WAAWkB,EAAQ,CACjB,OAAAA,EAASM,MAAA,GACJV,GACAX,EAAE,KAAK,QAAQ,EAAE,KAAM,GACtB,OAAOe,GAAW,UAAYA,EAASA,EAAS,CAAE,GAGxDG,EAAK,gBACHvB,EACAoB,EACA,KAAK,YAAY,WACvB,EAEWA,CACX,CAEE,eAAgB,CACdf,EAAE,KAAK,QAAQ,EAAE,GAAGK,EAAqBK,EAAuB,IAAM,KAAK,KAAM,CAAA,CACrF,CAEE,QAAS,CACP,MAAMO,EAAW,IAAM,CACrB,KAAK,SAAS,UAAU,IAAIf,CAAe,EAC3CF,EAAE,KAAK,QAAQ,EAAE,QAAQO,CAAY,CAC3C,EAGI,GADA,KAAK,SAAS,UAAU,OAAOJ,CAAe,EAC1C,KAAK,QAAQ,UAAW,CAC1B,MAAMgB,EAAqBD,EAAK,iCAAiC,KAAK,QAAQ,EAE9ElB,EAAE,KAAK,QAAQ,EACZ,IAAIkB,EAAK,eAAgBD,CAAQ,EACjC,qBAAqBE,CAAkB,CAChD,MACMF,EAAQ,CAEd,CAEE,eAAgB,CACd,aAAa,KAAK,QAAQ,EAC1B,KAAK,SAAW,IACpB,CAGE,OAAO,iBAAiBF,EAAQ,CAC9B,OAAO,KAAK,KAAK,UAAY,CAC3B,MAAMO,EAAWtB,EAAE,IAAI,EACvB,IAAIuB,EAAOD,EAAS,KAAKzB,CAAQ,EACjC,MAAM2B,EAAU,OAAOT,GAAW,UAAYA,EAO9C,GALKQ,IACHA,EAAO,IAAIV,EAAM,KAAMW,CAAO,EAC9BF,EAAS,KAAKzB,EAAU0B,CAAI,GAG1B,OAAOR,GAAW,SAAU,CAC9B,GAAI,OAAOQ,EAAKR,CAAM,GAAM,YAC1B,MAAM,IAAI,UAAU,oBAAoBA,CAAM,GAAG,EAGnDQ,EAAKR,CAAM,EAAE,IAAI,CACzB,CACK,CAAA,CACL,CACA,CAMAf,EAAE,GAAGL,CAAI,EAAIkB,EAAM,iBACnBb,EAAE,GAAGL,CAAI,EAAE,YAAckB,EACzBb,EAAE,GAAGL,CAAI,EAAE,WAAa,KACtBK,EAAE,GAAGL,CAAI,EAAII,EACNc,EAAM", "x_google_ignoreList": [0]}