{"version": 3, "file": "dropdown-CU6KAV4x.js", "sources": ["../../node_modules/.pnpm/bootstrap@4.6.2_j<PERSON>y@3.7.1_popper.js@1.16.1/node_modules/bootstrap/js/src/dropdown.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst ESCAPE_KEYCODE = 27 // KeyboardEvent.which value for Escape (Esc) key\nconst SPACE_KEYCODE = 32 // KeyboardEvent.which value for space key\nconst TAB_KEYCODE = 9 // KeyboardEvent.which value for tab key\nconst ARROW_UP_KEYCODE = 38 // KeyboardEvent.which value for up arrow key\nconst ARROW_DOWN_KEYCODE = 40 // KeyboardEvent.which value for down arrow key\nconst RIGHT_MOUSE_BUTTON_WHICH = 3 // MouseEvent.which value for the right button (assuming a right-handed mouse)\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEYCODE}|${ARROW_DOWN_KEYCODE}|${ESCAPE_KEYCODE}`)\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPRIGHT = 'dropright'\nconst CLASS_NAME_DROPLEFT = 'dropleft'\nconst CLASS_NAME_MENURIGHT = 'dropdown-menu-right'\nconst CLASS_NAME_POSITION_STATIC = 'position-static'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = 'top-start'\nconst PLACEMENT_TOPEND = 'top-end'\nconst PLACEMENT_BOTTOM = 'bottom-start'\nconst PLACEMENT_BOTTOMEND = 'bottom-end'\nconst PLACEMENT_RIGHT = 'right-start'\nconst PLACEMENT_LEFT = 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'scrollParent',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element = element\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n  toggle() {\n    if (this._element.disabled || $(this._element).hasClass(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = $(this._menu).hasClass(CLASS_NAME_SHOW)\n\n    Dropdown._clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show(true)\n  }\n\n  show(usePopper = false) {\n    if (this._element.disabled || $(this._element).hasClass(CLASS_NAME_DISABLED) || $(this._menu).hasClass(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n    const showEvent = $.Event(EVENT_SHOW, relatedTarget)\n    const parent = Dropdown._getParentFromElement(this._element)\n\n    $(parent).trigger(showEvent)\n\n    if (showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (!this._inNavbar && usePopper) {\n      // Check for Popper dependency\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (Util.isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        $(parent).addClass(CLASS_NAME_POSITION_STATIC)\n      }\n\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n        $(parent).closest(SELECTOR_NAVBAR_NAV).length === 0) {\n      $(document.body).children().on('mouseover', null, $.noop)\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    $(this._menu).toggleClass(CLASS_NAME_SHOW)\n    $(parent)\n      .toggleClass(CLASS_NAME_SHOW)\n      .trigger($.Event(EVENT_SHOWN, relatedTarget))\n  }\n\n  hide() {\n    if (this._element.disabled || $(this._element).hasClass(CLASS_NAME_DISABLED) || !$(this._menu).hasClass(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n    const hideEvent = $.Event(EVENT_HIDE, relatedTarget)\n    const parent = Dropdown._getParentFromElement(this._element)\n\n    $(parent).trigger(hideEvent)\n\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    $(this._menu).toggleClass(CLASS_NAME_SHOW)\n    $(parent)\n      .toggleClass(CLASS_NAME_SHOW)\n      .trigger($.Event(EVENT_HIDDEN, relatedTarget))\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    $(this._element).off(EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper !== null) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n  _addEventListeners() {\n    $(this._element).on(EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...$(this._element).data(),\n      ...config\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _getMenuElement() {\n    if (!this._menu) {\n      const parent = Dropdown._getParentFromElement(this._element)\n\n      if (parent) {\n        this._menu = parent.querySelector(SELECTOR_MENU)\n      }\n    }\n\n    return this._menu\n  }\n\n  _getPlacement() {\n    const $parentDropdown = $(this._element.parentNode)\n    let placement = PLACEMENT_BOTTOM\n\n    // Handle dropup\n    if ($parentDropdown.hasClass(CLASS_NAME_DROPUP)) {\n      placement = $(this._menu).hasClass(CLASS_NAME_MENURIGHT) ?\n        PLACEMENT_TOPEND :\n        PLACEMENT_TOP\n    } else if ($parentDropdown.hasClass(CLASS_NAME_DROPRIGHT)) {\n      placement = PLACEMENT_RIGHT\n    } else if ($parentDropdown.hasClass(CLASS_NAME_DROPLEFT)) {\n      placement = PLACEMENT_LEFT\n    } else if ($(this._menu).hasClass(CLASS_NAME_MENURIGHT)) {\n      placement = PLACEMENT_BOTTOMEND\n    }\n\n    return placement\n  }\n\n  _detectNavbar() {\n    return $(this._element).closest('.navbar').length > 0\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...this._config.offset(data.offsets, this._element)\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data) {\n        data = new Dropdown(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static _clearMenus(event) {\n    if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH ||\n      event.type === 'keyup' && event.which !== TAB_KEYCODE)) {\n      return\n    }\n\n    const toggles = [].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLE))\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown._getParentFromElement(toggles[i])\n      const context = $(toggles[i]).data(DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!$(parent).hasClass(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && (event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName) || event.type === 'keyup' && event.which === TAB_KEYCODE) &&\n          $.contains(parent, event.target)) {\n        continue\n      }\n\n      const hideEvent = $.Event(EVENT_HIDE, relatedTarget)\n      $(parent).trigger(hideEvent)\n      if (hideEvent.isDefaultPrevented()) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        $(document.body).children().off('mouseover', null, $.noop)\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      $(dropdownMenu).removeClass(CLASS_NAME_SHOW)\n      $(parent)\n        .removeClass(CLASS_NAME_SHOW)\n        .trigger($.Event(EVENT_HIDDEN, relatedTarget))\n    }\n  }\n\n  static _getParentFromElement(element) {\n    let parent\n    const selector = Util.getSelectorFromElement(element)\n\n    if (selector) {\n      parent = document.querySelector(selector)\n    }\n\n    return parent || element.parentNode\n  }\n\n  // eslint-disable-next-line complexity\n  static _dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE &&\n      (event.which !== ARROW_DOWN_KEYCODE && event.which !== ARROW_UP_KEYCODE ||\n        $(event.target).closest(SELECTOR_MENU).length) : !REGEXP_KEYDOWN.test(event.which)) {\n      return\n    }\n\n    if (this.disabled || $(this).hasClass(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown._getParentFromElement(this)\n    const isActive = $(parent).hasClass(CLASS_NAME_SHOW)\n\n    if (!isActive && event.which === ESCAPE_KEYCODE) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (!isActive || (event.which === ESCAPE_KEYCODE || event.which === SPACE_KEYCODE)) {\n      if (event.which === ESCAPE_KEYCODE) {\n        $(parent.querySelector(SELECTOR_DATA_TOGGLE)).trigger('focus')\n      }\n\n      $(this).trigger('click')\n      return\n    }\n\n    const items = [].slice.call(parent.querySelectorAll(SELECTOR_VISIBLE_ITEMS))\n      .filter(item => $(item).is(':visible'))\n\n    if (items.length === 0) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.which === ARROW_UP_KEYCODE && index > 0) { // Up\n      index--\n    }\n\n    if (event.which === ARROW_DOWN_KEYCODE && index < items.length - 1) { // Down\n      index++\n    }\n\n    if (index < 0) {\n      index = 0\n    }\n\n    items[index].focus()\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(document)\n  .on(EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown._dataApiKeydownHandler)\n  .on(EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown._dataApiKeydownHandler)\n  .on(`${EVENT_CLICK_DATA_API} ${EVENT_KEYUP_DATA_API}`, Dropdown._clearMenus)\n  .on(EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n    event.preventDefault()\n    event.stopPropagation()\n    Dropdown._jQueryInterface.call($(this), 'toggle')\n  })\n  .on(EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => {\n    e.stopPropagation()\n  })\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Dropdown._jQueryInterface\n$.fn[NAME].Constructor = Dropdown\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Dropdown._jQueryInterface\n}\n\nexport default Dropdown\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "ESCAPE_KEYCODE", "SPACE_KEYCODE", "TAB_KEYCODE", "ARROW_UP_KEYCODE", "ARROW_DOWN_KEYCODE", "RIGHT_MOUSE_BUTTON_WHICH", "REGEXP_KEYDOWN", "CLASS_NAME_DISABLED", "CLASS_NAME_SHOW", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPRIGHT", "CLASS_NAME_DROPLEFT", "CLASS_NAME_MENURIGHT", "CLASS_NAME_POSITION_STATIC", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_CLICK", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "SELECTOR_DATA_TOGGLE", "SELECTOR_FORM_CHILD", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "<PERSON><PERSON><PERSON>", "DefaultType", "Dropdown", "element", "config", "isActive", "usePopper", "relatedTarget", "showEvent", "parent", "<PERSON><PERSON>", "referenceElement", "<PERSON><PERSON>", "hideEvent", "event", "__spreadValues", "$parentDropdown", "placement", "offset", "data", "popperConfig", "_config", "toggles", "i", "len", "context", "dropdownMenu", "selector", "items", "item", "index", "e"], "mappings": "+bAeA,MAAMA,EAAO,WACPC,EAAU,QACVC,EAAW,cACXC,EAAY,IAAID,CAAQ,GACxBE,EAAe,YACfC,EAAqBC,EAAE,GAAGN,CAAI,EAC9BO,EAAiB,GACjBC,EAAgB,GAChBC,EAAc,EACdC,EAAmB,GACnBC,EAAqB,GACrBC,EAA2B,EAC3BC,EAAiB,IAAI,OAAO,GAAGH,CAAgB,IAAIC,CAAkB,IAAIJ,CAAc,EAAE,EAEzFO,EAAsB,WACtBC,EAAkB,OAClBC,EAAoB,SACpBC,EAAuB,YACvBC,EAAsB,WACtBC,EAAuB,sBACvBC,EAA6B,kBAE7BC,EAAa,OAAOlB,CAAS,GAC7BmB,EAAe,SAASnB,CAAS,GACjCoB,EAAa,OAAOpB,CAAS,GAC7BqB,EAAc,QAAQrB,CAAS,GAC/BsB,EAAc,QAAQtB,CAAS,GAC/BuB,EAAuB,QAAQvB,CAAS,GAAGC,CAAY,GACvDuB,EAAyB,UAAUxB,CAAS,GAAGC,CAAY,GAC3DwB,EAAuB,QAAQzB,CAAS,GAAGC,CAAY,GAEvDyB,EAAuB,2BACvBC,EAAsB,iBACtBC,EAAgB,iBAChBC,EAAsB,cACtBC,EAAyB,8DAEzBC,EAAgB,YAChBC,GAAmB,UACnBC,GAAmB,eACnBC,GAAsB,aACtBC,GAAkB,cAClBC,GAAiB,aAEjBC,GAAU,CACd,OAAQ,EACR,KAAM,GACN,SAAU,eACV,UAAW,SACX,QAAS,UACT,aAAc,IAChB,EAEMC,GAAc,CAClB,OAAQ,2BACR,KAAM,UACN,SAAU,mBACV,UAAW,mBACX,QAAS,SACT,aAAc,eAChB,EAMA,MAAMC,CAAS,CACb,YAAYC,EAASC,EAAQ,CAC3B,KAAK,SAAWD,EAChB,KAAK,QAAU,KACf,KAAK,QAAU,KAAK,WAAWC,CAAM,EACrC,KAAK,MAAQ,KAAK,gBAAe,EACjC,KAAK,UAAY,KAAK,cAAa,EAEnC,KAAK,mBAAkB,CAC3B,CAGE,WAAW,SAAU,CACnB,OAAO3C,CACX,CAEE,WAAW,SAAU,CACnB,OAAOuC,EACX,CAEE,WAAW,aAAc,CACvB,OAAOC,EACX,CAGE,QAAS,CACP,GAAI,KAAK,SAAS,UAAYnC,EAAE,KAAK,QAAQ,EAAE,SAASQ,CAAmB,EACzE,OAGF,MAAM+B,EAAWvC,EAAE,KAAK,KAAK,EAAE,SAASS,CAAe,EAEvD2B,EAAS,YAAW,EAEhB,CAAAG,GAIJ,KAAK,KAAK,EAAI,CAClB,CAEE,KAAKC,EAAY,GAAO,CACtB,GAAI,KAAK,SAAS,UAAYxC,EAAE,KAAK,QAAQ,EAAE,SAASQ,CAAmB,GAAKR,EAAE,KAAK,KAAK,EAAE,SAASS,CAAe,EACpH,OAGF,MAAMgC,EAAgB,CACpB,cAAe,KAAK,QAC1B,EACUC,EAAY1C,EAAE,MAAMiB,EAAYwB,CAAa,EAC7CE,EAASP,EAAS,sBAAsB,KAAK,QAAQ,EAI3D,GAFApC,EAAE2C,CAAM,EAAE,QAAQD,CAAS,EAEvB,CAAAA,EAAU,qBAKd,IAAI,CAAC,KAAK,WAAaF,EAAW,CAEhC,GAAI,OAAOI,GAAW,YACpB,MAAM,IAAI,UAAU,8DAA+D,EAGrF,IAAIC,EAAmB,KAAK,SAExB,KAAK,QAAQ,YAAc,SAC7BA,EAAmBF,EACVG,EAAK,UAAU,KAAK,QAAQ,SAAS,IAC9CD,EAAmB,KAAK,QAAQ,UAG5B,OAAO,KAAK,QAAQ,UAAU,QAAW,cAC3CA,EAAmB,KAAK,QAAQ,UAAU,CAAC,IAO3C,KAAK,QAAQ,WAAa,gBAC5B7C,EAAE2C,CAAM,EAAE,SAAS7B,CAA0B,EAG/C,KAAK,QAAU,IAAI8B,EAAOC,EAAkB,KAAK,MAAO,KAAK,iBAAkB,CAAA,CACrF,CAMQ,iBAAkB,SAAS,iBAC3B7C,EAAE2C,CAAM,EAAE,QAAQjB,CAAmB,EAAE,SAAW,GACpD1B,EAAE,SAAS,IAAI,EAAE,SAAQ,EAAG,GAAG,YAAa,KAAMA,EAAE,IAAI,EAG1D,KAAK,SAAS,MAAK,EACnB,KAAK,SAAS,aAAa,gBAAiB,EAAI,EAEhDA,EAAE,KAAK,KAAK,EAAE,YAAYS,CAAe,EACzCT,EAAE2C,CAAM,EACL,YAAYlC,CAAe,EAC3B,QAAQT,EAAE,MAAMkB,EAAauB,CAAa,CAAC,EAClD,CAEE,MAAO,CACL,GAAI,KAAK,SAAS,UAAYzC,EAAE,KAAK,QAAQ,EAAE,SAASQ,CAAmB,GAAK,CAACR,EAAE,KAAK,KAAK,EAAE,SAASS,CAAe,EACrH,OAGF,MAAMgC,EAAgB,CACpB,cAAe,KAAK,QAC1B,EACUM,EAAY/C,EAAE,MAAMe,EAAY0B,CAAa,EAC7CE,EAASP,EAAS,sBAAsB,KAAK,QAAQ,EAE3DpC,EAAE2C,CAAM,EAAE,QAAQI,CAAS,EAEvB,CAAAA,EAAU,uBAIV,KAAK,SACP,KAAK,QAAQ,QAAO,EAGtB/C,EAAE,KAAK,KAAK,EAAE,YAAYS,CAAe,EACzCT,EAAE2C,CAAM,EACL,YAAYlC,CAAe,EAC3B,QAAQT,EAAE,MAAMgB,EAAcyB,CAAa,CAAC,EACnD,CAEE,SAAU,CACRzC,EAAE,WAAW,KAAK,SAAUJ,CAAQ,EACpCI,EAAE,KAAK,QAAQ,EAAE,IAAIH,CAAS,EAC9B,KAAK,SAAW,KAChB,KAAK,MAAQ,KACT,KAAK,UAAY,OACnB,KAAK,QAAQ,QAAO,EACpB,KAAK,QAAU,KAErB,CAEE,QAAS,CACP,KAAK,UAAY,KAAK,cAAa,EAC/B,KAAK,UAAY,MACnB,KAAK,QAAQ,eAAc,CAEjC,CAGE,oBAAqB,CACnBG,EAAE,KAAK,QAAQ,EAAE,GAAGmB,EAAa6B,GAAS,CACxCA,EAAM,eAAc,EACpBA,EAAM,gBAAe,EACrB,KAAK,OAAM,CACZ,CAAA,CACL,CAEE,WAAWV,EAAQ,CACjB,OAAAA,EAASW,MAAA,GACJ,KAAK,YAAY,SACjBjD,EAAE,KAAK,QAAQ,EAAE,KAAM,GACvBsC,GAGLQ,EAAK,gBACHpD,EACA4C,EACA,KAAK,YAAY,WACvB,EAEWA,CACX,CAEE,iBAAkB,CAChB,GAAI,CAAC,KAAK,MAAO,CACf,MAAMK,EAASP,EAAS,sBAAsB,KAAK,QAAQ,EAEvDO,IACF,KAAK,MAAQA,EAAO,cAAclB,CAAa,EAEvD,CAEI,OAAO,KAAK,KAChB,CAEE,eAAgB,CACd,MAAMyB,EAAkBlD,EAAE,KAAK,SAAS,UAAU,EAClD,IAAImD,EAAYrB,GAGhB,OAAIoB,EAAgB,SAASxC,CAAiB,EAC5CyC,EAAYnD,EAAE,KAAK,KAAK,EAAE,SAASa,CAAoB,EACrDgB,GACAD,EACOsB,EAAgB,SAASvC,CAAoB,EACtDwC,EAAYnB,GACHkB,EAAgB,SAAStC,CAAmB,EACrDuC,EAAYlB,GACHjC,EAAE,KAAK,KAAK,EAAE,SAASa,CAAoB,IACpDsC,EAAYpB,IAGPoB,CACX,CAEE,eAAgB,CACd,OAAOnD,EAAE,KAAK,QAAQ,EAAE,QAAQ,SAAS,EAAE,OAAS,CACxD,CAEE,YAAa,CACX,MAAMoD,EAAS,CAAA,EAEf,OAAI,OAAO,KAAK,QAAQ,QAAW,WACjCA,EAAO,GAAKC,IACVA,EAAK,QAAUJ,IAAA,GACVI,EAAK,SACL,KAAK,QAAQ,OAAOA,EAAK,QAAS,KAAK,QAAQ,GAG7CA,GAGTD,EAAO,OAAS,KAAK,QAAQ,OAGxBA,CACX,CAEE,kBAAmB,CACjB,MAAME,EAAe,CACnB,UAAW,KAAK,cAAe,EAC/B,UAAW,CACT,OAAQ,KAAK,WAAY,EACzB,KAAM,CACJ,QAAS,KAAK,QAAQ,IACvB,EACD,gBAAiB,CACf,kBAAmB,KAAK,QAAQ,QAC1C,CACA,CACA,EAGI,OAAI,KAAK,QAAQ,UAAY,WAC3BA,EAAa,UAAU,WAAa,CAClC,QAAS,EACjB,GAGWL,IAAA,GACFK,GACA,KAAK,QAAQ,aAEtB,CAGE,OAAO,iBAAiBhB,EAAQ,CAC9B,OAAO,KAAK,KAAK,UAAY,CAC3B,IAAIe,EAAOrD,EAAE,IAAI,EAAE,KAAKJ,CAAQ,EAChC,MAAM2D,EAAU,OAAOjB,GAAW,SAAWA,EAAS,KAOtD,GALKe,IACHA,EAAO,IAAIjB,EAAS,KAAMmB,CAAO,EACjCvD,EAAE,IAAI,EAAE,KAAKJ,EAAUyD,CAAI,GAGzB,OAAOf,GAAW,SAAU,CAC9B,GAAI,OAAOe,EAAKf,CAAM,GAAM,YAC1B,MAAM,IAAI,UAAU,oBAAoBA,CAAM,GAAG,EAGnDe,EAAKf,CAAM,EAAC,CACpB,CACK,CAAA,CACL,CAEE,OAAO,YAAYU,EAAO,CACxB,GAAIA,IAAUA,EAAM,QAAU1C,GAC5B0C,EAAM,OAAS,SAAWA,EAAM,QAAU7C,GAC1C,OAGF,MAAMqD,EAAU,CAAA,EAAG,MAAM,KAAK,SAAS,iBAAiBjC,CAAoB,CAAC,EAE7E,QAASkC,EAAI,EAAGC,EAAMF,EAAQ,OAAQC,EAAIC,EAAKD,IAAK,CAClD,MAAMd,EAASP,EAAS,sBAAsBoB,EAAQC,CAAC,CAAC,EAClDE,EAAU3D,EAAEwD,EAAQC,CAAC,CAAC,EAAE,KAAK7D,CAAQ,EACrC6C,EAAgB,CACpB,cAAee,EAAQC,CAAC,CAChC,EAMM,GAJIT,GAASA,EAAM,OAAS,UAC1BP,EAAc,WAAaO,GAGzB,CAACW,EACH,SAGF,MAAMC,EAAeD,EAAQ,MAK7B,GAJI,CAAC3D,EAAE2C,CAAM,EAAE,SAASlC,CAAe,GAInCuC,IAAUA,EAAM,OAAS,SACzB,kBAAkB,KAAKA,EAAM,OAAO,OAAO,GAAKA,EAAM,OAAS,SAAWA,EAAM,QAAU7C,IAC1FH,EAAE,SAAS2C,EAAQK,EAAM,MAAM,EACjC,SAGF,MAAMD,EAAY/C,EAAE,MAAMe,EAAY0B,CAAa,EACnDzC,EAAE2C,CAAM,EAAE,QAAQI,CAAS,EACvB,CAAAA,EAAU,uBAMV,iBAAkB,SAAS,iBAC7B/C,EAAE,SAAS,IAAI,EAAE,SAAQ,EAAG,IAAI,YAAa,KAAMA,EAAE,IAAI,EAG3DwD,EAAQC,CAAC,EAAE,aAAa,gBAAiB,OAAO,EAE5CE,EAAQ,SACVA,EAAQ,QAAQ,QAAO,EAGzB3D,EAAE4D,CAAY,EAAE,YAAYnD,CAAe,EAC3CT,EAAE2C,CAAM,EACL,YAAYlC,CAAe,EAC3B,QAAQT,EAAE,MAAMgB,EAAcyB,CAAa,CAAC,EACrD,CACA,CAEE,OAAO,sBAAsBJ,EAAS,CACpC,IAAIM,EACJ,MAAMkB,EAAWf,EAAK,uBAAuBT,CAAO,EAEpD,OAAIwB,IACFlB,EAAS,SAAS,cAAckB,CAAQ,GAGnClB,GAAUN,EAAQ,UAC7B,CAGE,OAAO,uBAAuBW,EAAO,CAenC,IAPI,kBAAkB,KAAKA,EAAM,OAAO,OAAO,EAC7CA,EAAM,QAAU9C,GAAiB8C,EAAM,QAAU/C,IAChD+C,EAAM,QAAU3C,GAAsB2C,EAAM,QAAU5C,GACrDJ,EAAEgD,EAAM,MAAM,EAAE,QAAQvB,CAAa,EAAE,QAAU,CAAClB,EAAe,KAAKyC,EAAM,KAAK,IAIjF,KAAK,UAAYhD,EAAE,IAAI,EAAE,SAASQ,CAAmB,EACvD,OAGF,MAAMmC,EAASP,EAAS,sBAAsB,IAAI,EAC5CG,EAAWvC,EAAE2C,CAAM,EAAE,SAASlC,CAAe,EAEnD,GAAI,CAAC8B,GAAYS,EAAM,QAAU/C,EAC/B,OAMF,GAHA+C,EAAM,eAAc,EACpBA,EAAM,gBAAe,EAEjB,CAACT,GAAaS,EAAM,QAAU/C,GAAkB+C,EAAM,QAAU9C,EAAgB,CAC9E8C,EAAM,QAAU/C,GAClBD,EAAE2C,EAAO,cAAcpB,CAAoB,CAAC,EAAE,QAAQ,OAAO,EAG/DvB,EAAE,IAAI,EAAE,QAAQ,OAAO,EACvB,MACN,CAEI,MAAM8D,EAAQ,CAAA,EAAG,MAAM,KAAKnB,EAAO,iBAAiBhB,CAAsB,CAAC,EACxE,OAAOoC,GAAQ/D,EAAE+D,CAAI,EAAE,GAAG,UAAU,CAAC,EAExC,GAAID,EAAM,SAAW,EACnB,OAGF,IAAIE,EAAQF,EAAM,QAAQd,EAAM,MAAM,EAElCA,EAAM,QAAU5C,GAAoB4D,EAAQ,GAC9CA,IAGEhB,EAAM,QAAU3C,GAAsB2D,EAAQF,EAAM,OAAS,GAC/DE,IAGEA,EAAQ,IACVA,EAAQ,GAGVF,EAAME,CAAK,EAAE,MAAK,CACtB,CACA,CAMAhE,EAAE,QAAQ,EACP,GAAGqB,EAAwBE,EAAsBa,EAAS,sBAAsB,EAChF,GAAGf,EAAwBI,EAAeW,EAAS,sBAAsB,EACzE,GAAG,GAAGhB,CAAoB,IAAIE,CAAoB,GAAIc,EAAS,WAAW,EAC1E,GAAGhB,EAAsBG,EAAsB,SAAUyB,EAAO,CAC/DA,EAAM,eAAc,EACpBA,EAAM,gBAAe,EACrBZ,EAAS,iBAAiB,KAAKpC,EAAE,IAAI,EAAG,QAAQ,CACjD,CAAA,EACA,GAAGoB,EAAsBI,EAAqByC,GAAK,CAClDA,EAAE,gBAAe,CAClB,CAAA,EAMHjE,EAAE,GAAGN,CAAI,EAAI0C,EAAS,iBACtBpC,EAAE,GAAGN,CAAI,EAAE,YAAc0C,EACzBpC,EAAE,GAAGN,CAAI,EAAE,WAAa,KACtBM,EAAE,GAAGN,CAAI,EAAIK,EACNqC,EAAS", "x_google_ignoreList": [0]}