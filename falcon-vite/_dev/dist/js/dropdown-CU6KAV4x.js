var K=Object.defineProperty;var O=Object.getOwnPropertySymbols;var H=Object.prototype.hasOwnProperty,V=Object.prototype.propertyIsEnumerable;var S=(r,e,s)=>e in r?K(r,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):r[e]=s,f=(r,e)=>{for(var s in e||(e={}))H.call(e,s)&&S(r,s,e[s]);if(O)for(var s of O(e))V.call(e,s)&&S(r,s,e[s]);return r};import t from"jquery";import{P as w}from"./popper-BdHdNNH2.js";import{U as m}from"./util-B8s7WWwa.js";const _="dropdown",Y="4.6.2",p="bs.dropdown",c=`.${p}`,y=".data-api",k=t.fn[_],u=27,b=32,D=9,T=38,A=40,F=3,U=new RegExp(`${T}|${A}|${u}`),d="disabled",l="show",x="dropup",W="dropright",j="dropleft",L="dropdown-menu-right",v="position-static",I=`hide${c}`,M=`hidden${c}`,B=`show${c}`,q=`shown${c}`,G=`click${c}`,C=`click${c}${y}`,R=`keydown${c}${y}`,Q=`keyup${c}${y}`,E='[data-toggle="dropdown"]',J=".dropdown form",N=".dropdown-menu",X=".navbar-nav",z=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",Z="top-start",ee="top-end",te="bottom-start",se="bottom-end",ne="right-start",oe="left-start",ie={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},re={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"};class i{constructor(e,s){this._element=e,this._popper=null,this._config=this._getConfig(s),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}static get VERSION(){return Y}static get Default(){return ie}static get DefaultType(){return re}toggle(){if(this._element.disabled||t(this._element).hasClass(d))return;const e=t(this._menu).hasClass(l);i._clearMenus(),!e&&this.show(!0)}show(e=!1){if(this._element.disabled||t(this._element).hasClass(d)||t(this._menu).hasClass(l))return;const s={relatedTarget:this._element},n=t.Event(B,s),a=i._getParentFromElement(this._element);if(t(a).trigger(n),!n.isDefaultPrevented()){if(!this._inNavbar&&e){if(typeof w=="undefined")throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let o=this._element;this._config.reference==="parent"?o=a:m.isElement(this._config.reference)&&(o=this._config.reference,typeof this._config.reference.jquery!="undefined"&&(o=this._config.reference[0])),this._config.boundary!=="scrollParent"&&t(a).addClass(v),this._popper=new w(o,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&t(a).closest(X).length===0&&t(document.body).children().on("mouseover",null,t.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),t(this._menu).toggleClass(l),t(a).toggleClass(l).trigger(t.Event(q,s))}}hide(){if(this._element.disabled||t(this._element).hasClass(d)||!t(this._menu).hasClass(l))return;const e={relatedTarget:this._element},s=t.Event(I,e),n=i._getParentFromElement(this._element);t(n).trigger(s),!s.isDefaultPrevented()&&(this._popper&&this._popper.destroy(),t(this._menu).toggleClass(l),t(n).toggleClass(l).trigger(t.Event(M,e)))}dispose(){t.removeData(this._element,p),t(this._element).off(c),this._element=null,this._menu=null,this._popper!==null&&(this._popper.destroy(),this._popper=null)}update(){this._inNavbar=this._detectNavbar(),this._popper!==null&&this._popper.scheduleUpdate()}_addEventListeners(){t(this._element).on(G,e=>{e.preventDefault(),e.stopPropagation(),this.toggle()})}_getConfig(e){return e=f(f(f({},this.constructor.Default),t(this._element).data()),e),m.typeCheckConfig(_,e,this.constructor.DefaultType),e}_getMenuElement(){if(!this._menu){const e=i._getParentFromElement(this._element);e&&(this._menu=e.querySelector(N))}return this._menu}_getPlacement(){const e=t(this._element.parentNode);let s=te;return e.hasClass(x)?s=t(this._menu).hasClass(L)?ee:Z:e.hasClass(W)?s=ne:e.hasClass(j)?s=oe:t(this._menu).hasClass(L)&&(s=se),s}_detectNavbar(){return t(this._element).closest(".navbar").length>0}_getOffset(){const e={};return typeof this._config.offset=="function"?e.fn=s=>(s.offsets=f(f({},s.offsets),this._config.offset(s.offsets,this._element)),s):e.offset=this._config.offset,e}_getPopperConfig(){const e={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return this._config.display==="static"&&(e.modifiers.applyStyle={enabled:!1}),f(f({},e),this._config.popperConfig)}static _jQueryInterface(e){return this.each(function(){let s=t(this).data(p);const n=typeof e=="object"?e:null;if(s||(s=new i(this,n),t(this).data(p,s)),typeof e=="string"){if(typeof s[e]=="undefined")throw new TypeError(`No method named "${e}"`);s[e]()}})}static _clearMenus(e){if(e&&(e.which===F||e.type==="keyup"&&e.which!==D))return;const s=[].slice.call(document.querySelectorAll(E));for(let n=0,a=s.length;n<a;n++){const o=i._getParentFromElement(s[n]),h=t(s[n]).data(p),g={relatedTarget:s[n]};if(e&&e.type==="click"&&(g.clickEvent=e),!h)continue;const $=h._menu;if(!t(o).hasClass(l)||e&&(e.type==="click"&&/input|textarea/i.test(e.target.tagName)||e.type==="keyup"&&e.which===D)&&t.contains(o,e.target))continue;const P=t.Event(I,g);t(o).trigger(P),!P.isDefaultPrevented()&&("ontouchstart"in document.documentElement&&t(document.body).children().off("mouseover",null,t.noop),s[n].setAttribute("aria-expanded","false"),h._popper&&h._popper.destroy(),t($).removeClass(l),t(o).removeClass(l).trigger(t.Event(M,g)))}}static _getParentFromElement(e){let s;const n=m.getSelectorFromElement(e);return n&&(s=document.querySelector(n)),s||e.parentNode}static _dataApiKeydownHandler(e){if((/input|textarea/i.test(e.target.tagName)?e.which===b||e.which!==u&&(e.which!==A&&e.which!==T||t(e.target).closest(N).length):!U.test(e.which))||this.disabled||t(this).hasClass(d))return;const s=i._getParentFromElement(this),n=t(s).hasClass(l);if(!n&&e.which===u)return;if(e.preventDefault(),e.stopPropagation(),!n||e.which===u||e.which===b){e.which===u&&t(s.querySelector(E)).trigger("focus"),t(this).trigger("click");return}const a=[].slice.call(s.querySelectorAll(z)).filter(h=>t(h).is(":visible"));if(a.length===0)return;let o=a.indexOf(e.target);e.which===T&&o>0&&o--,e.which===A&&o<a.length-1&&o++,o<0&&(o=0),a[o].focus()}}t(document).on(R,E,i._dataApiKeydownHandler).on(R,N,i._dataApiKeydownHandler).on(`${C} ${Q}`,i._clearMenus).on(C,E,function(r){r.preventDefault(),r.stopPropagation(),i._jQueryInterface.call(t(this),"toggle")}).on(C,J,r=>{r.stopPropagation()});t.fn[_]=i._jQueryInterface;t.fn[_].Constructor=i;t.fn[_].noConflict=()=>(t.fn[_]=k,i._jQueryInterface);export{i as default};
//# sourceMappingURL=dropdown-CU6KAV4x.js.map
