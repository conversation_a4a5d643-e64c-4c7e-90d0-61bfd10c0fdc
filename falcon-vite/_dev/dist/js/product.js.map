{"version": 3, "file": "product.js", "sources": ["../../js/product/components/ProductGallery.js", "../../js/product/index.js"], "sourcesContent": ["class ProductGallery {\n  constructor({\n    thumbsSliderSelector = '.js-product-thumbs',\n    mainSliderSelector = '.js-product-main-images',\n    modalSliderSelector = '.js-modal-gallery',\n    galleryModalSelector = '.js-product-images-modal',\n  } = {}) {\n    this.thumbsSliderSelector = thumbsSliderSelector;\n    this.mainSliderSelector = mainSliderSelector;\n    this.modalSliderSelector = modalSliderSelector;\n    this.galleryModalSelector = galleryModalSelector;\n    this.mainSliderSwiperInstance = null;\n    this.modalSliderSwiperInstance = null;\n  }\n\n  init() {\n    this.mainSliderSwiperInstance = null;\n    this.modalSliderSwiperInstance = null;\n    this.initProductImageSlider();\n    this.initModalGallerySlider();\n  }\n\n  async initProductImageSlider() {\n    const thumbsElem = document.querySelector(this.thumbsSliderSelector);\n    const galleryTopElem = document.querySelector(this.mainSliderSelector);\n\n    if (!thumbsElem && !galleryTopElem) {\n      return;\n    }\n\n    const galleryThumbs = new prestashop.SwiperSlider(thumbsElem, {\n      breakpoints: {\n        320: {\n          slidesPerView: 3,\n        },\n        576: {\n          slidesPerView: 4,\n        },\n      },\n      watchSlidesVisibility: true,\n      watchSlidesProgress: true,\n    });\n\n    const galleryThumbsInstance = await galleryThumbs.initSlider();\n\n    const mainSlider = new prestashop.SwiperSlider(galleryTopElem, {\n      spaceBetween: 10,\n      navigation: {\n        nextEl: galleryTopElem.querySelector('.swiper-button-next'),\n        prevEl: galleryTopElem.querySelector('.swiper-button-prev'),\n      },\n      thumbs: {\n        swiper: galleryThumbsInstance,\n      },\n    });\n\n    const mainSliderInstance = await mainSlider.initSlider();\n\n    this.mainSliderSwiperInstance = mainSliderInstance;\n  }\n\n  initModalGallerySlider() {\n    const gallerySliderElem = document.querySelector(this.modalSliderSelector);\n\n    if (!gallerySliderElem) {\n      return;\n    }\n\n    const handleModalOpen = async () => {\n      if (this.modalSliderSwiperInstance) {\n        gallerySliderElem.style.opacity = 0;\n\n        // DIRTY HACK\n        setTimeout(() => {\n          this.modalSliderSwiperInstance.update();\n          this.modalSliderSwiperInstance.slideTo(this.mainSliderSwiperInstance ? this.mainSliderSwiperInstance.activeIndex : 0, 0);\n          gallerySliderElem.style.opacity = 1;\n        }, 200);\n      } else {\n        const modalSlider = new prestashop.SwiperSlider(gallerySliderElem, {\n          slidesPerView: 1,\n          spaceBetween: 10,\n          initialSlide: this.mainSliderSwiperInstance ? this.mainSliderSwiperInstance.activeIndex : 0,\n          navigation: {\n            nextEl: gallerySliderElem.querySelector('.swiper-button-next'),\n            prevEl: gallerySliderElem.querySelector('.swiper-button-prev'),\n          },\n        });\n\n        const modalSliderInstance = await modalSlider.initSlider();\n\n        this.modalSliderSwiperInstance = modalSliderInstance;\n      }\n    };\n\n    // TO REFACTO LATER WITH BS5 REMOVE JQUERY!\n    $(this.galleryModalSelector).on('show.bs.modal', handleModalOpen);\n  }\n}\n\nexport default ProductGallery;\n", "import $ from 'jquery';\nimport ProductGallery from '@/js/product/components/ProductGallery';\n\nfunction activateFirstProductTab() {\n  $('.product-tabs .nav .nav-item:first-child a').tab('show');\n}\n\nfunction handleProductDetailsToggle() {\n  const $link = $('[href=\"#product-details\"]');\n  const $tab = $($link.attr('href'));\n\n  if ($tab.length && $link.length && $link.hasClass('active')) {\n    $tab.addClass('show active');\n  }\n}\n\n$(() => {\n  activateFirstProductTab();\n  const gallery = new ProductGallery();\n\n  gallery.init();\n\n  prestashop.on('updatedProductCombination', (event) => {\n    gallery.init();\n\n    const { product_add_to_cart: productAddToCart } = event;\n\n    if (productAddToCart) {\n      const node = document.createElement('div');\n      node.innerHTML = productAddToCart;\n\n      const html = node.querySelector('.js-product-actions-buttons');\n\n      if (html) {\n        const productActionsElement = document.querySelector('.js-product-actions-buttons');\n\n        productActionsElement.replaceWith(html);\n      }\n    }\n  });\n\n  prestashop.on('updatedProduct', () => {\n    handleProductDetailsToggle();\n  });\n});\n"], "names": ["ProductGallery", "thumbsSliderSelector", "mainSliderSelector", "modalSliderSelector", "galleryModalSelector", "__async", "thumbsElem", "galleryTopElem", "galleryThumbsInstance", "mainSliderInstance", "gallerySliderElem", "handleModalOpen", "modalSliderInstance", "activateFirstProductTab", "$", "handleProductDetailsToggle", "$link", "$tab", "gallery", "event", "productAddToCart", "node", "html"], "mappings": "mOAAA,MAAMA,CAAe,CACnB,YAAY,CACV,qBAAAC,EAAuB,qBACvB,mBAAAC,EAAqB,0BACrB,oBAAAC,EAAsB,oBACtB,qBAAAC,EAAuB,0BACxB,EAAG,GAAI,CACN,KAAK,qBAAuBH,EAC5B,KAAK,mBAAqBC,EAC1B,KAAK,oBAAsBC,EAC3B,KAAK,qBAAuBC,EAC5B,KAAK,yBAA2B,KAChC,KAAK,0BAA4B,IACrC,CAEE,MAAO,CACL,KAAK,yBAA2B,KAChC,KAAK,0BAA4B,KACjC,KAAK,uBAAwB,EAC7B,KAAK,uBAAwB,CACjC,CAEQ,wBAAyB,QAAAC,EAAA,sBAC7B,MAAMC,EAAa,SAAS,cAAc,KAAK,oBAAoB,EAC7DC,EAAiB,SAAS,cAAc,KAAK,kBAAkB,EAErE,GAAI,CAACD,GAAc,CAACC,EAClB,OAgBF,MAAMC,EAAwB,MAbR,IAAI,WAAW,aAAaF,EAAY,CAC5D,YAAa,CACX,IAAK,CACH,cAAe,CAChB,EACD,IAAK,CACH,cAAe,CAChB,CACF,EACD,sBAAuB,GACvB,oBAAqB,EAC3B,CAAK,EAEiD,WAAY,EAaxDG,EAAqB,MAXR,IAAI,WAAW,aAAaF,EAAgB,CAC7D,aAAc,GACd,WAAY,CACV,OAAQA,EAAe,cAAc,qBAAqB,EAC1D,OAAQA,EAAe,cAAc,qBAAqB,CAC3D,EACD,OAAQ,CACN,OAAQC,CACT,CACP,CAAK,EAE2C,WAAY,EAExD,KAAK,yBAA2BC,CACpC,GAEE,wBAAyB,CACvB,MAAMC,EAAoB,SAAS,cAAc,KAAK,mBAAmB,EAEzE,GAAI,CAACA,EACH,OAGF,MAAMC,EAAkB,IAAYN,EAAA,sBAClC,GAAI,KAAK,0BACPK,EAAkB,MAAM,QAAU,EAGlC,WAAW,IAAM,CACf,KAAK,0BAA0B,OAAQ,EACvC,KAAK,0BAA0B,QAAQ,KAAK,yBAA2B,KAAK,yBAAyB,YAAc,EAAG,CAAC,EACvHA,EAAkB,MAAM,QAAU,CACnC,EAAE,GAAG,MACD,CAWL,MAAME,EAAsB,MAVR,IAAI,WAAW,aAAaF,EAAmB,CACjE,cAAe,EACf,aAAc,GACd,aAAc,KAAK,yBAA2B,KAAK,yBAAyB,YAAc,EAC1F,WAAY,CACV,OAAQA,EAAkB,cAAc,qBAAqB,EAC7D,OAAQA,EAAkB,cAAc,qBAAqB,CAC9D,CACX,CAAS,EAE6C,WAAY,EAE1D,KAAK,0BAA4BE,CACzC,CACK,GAGD,EAAE,KAAK,oBAAoB,EAAE,GAAG,gBAAiBD,CAAe,CACpE,CACA,CC/FA,SAASE,GAA0B,CACjCC,EAAE,4CAA4C,EAAE,IAAI,MAAM,CAC5D,CAEA,SAASC,GAA6B,CACpC,MAAMC,EAAQF,EAAE,2BAA2B,EACrCG,EAAOH,EAAEE,EAAM,KAAK,MAAM,CAAC,EAE7BC,EAAK,QAAUD,EAAM,QAAUA,EAAM,SAAS,QAAQ,GACxDC,EAAK,SAAS,aAAa,CAE/B,CAEAH,EAAE,IAAM,CACND,EAAyB,EACzB,MAAMK,EAAU,IAAIlB,EAEpBkB,EAAQ,KAAM,EAEd,WAAW,GAAG,4BAA8BC,GAAU,CACpDD,EAAQ,KAAM,EAEd,KAAM,CAAE,oBAAqBE,CAAgB,EAAKD,EAElD,GAAIC,EAAkB,CACpB,MAAMC,EAAO,SAAS,cAAc,KAAK,EACzCA,EAAK,UAAYD,EAEjB,MAAME,EAAOD,EAAK,cAAc,6BAA6B,EAEzDC,GAC4B,SAAS,cAAc,6BAA6B,EAE5D,YAAYA,CAAI,CAE9C,CACA,CAAG,EAED,WAAW,GAAG,iBAAkB,IAAM,CACpCP,EAA4B,CAChC,CAAG,CACH,CAAC"}