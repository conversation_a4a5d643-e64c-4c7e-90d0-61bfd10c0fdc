var D=Object.defineProperty;var m=Object.getOwnPropertySymbols;var S=Object.prototype.hasOwnProperty,I=Object.prototype.propertyIsEnumerable;var u=(i,t,e)=>t in i?D(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e,h=(i,t)=>{for(var e in t||(t={}))S.call(t,e)&&u(i,e,t[e]);if(m)for(var e of m(t))I.call(t,e)&&u(i,e,t[e]);return i};import s from"jquery";import{U as o}from"./util-B8s7WWwa.js";const a="toast",L="4.6.2",c="bs.toast",r=`.${c}`,g=s.fn[a],A="fade",f="hide",l="show",d="showing",E=`click.dismiss${r}`,p=`hide${r}`,y=`hidden${r}`,C=`show${r}`,v=`shown${r}`,O='[data-dismiss="toast"]',T={animation:!0,autohide:!0,delay:500},w={animation:"boolean",autohide:"boolean",delay:"number"};class _{constructor(t,e){this._element=t,this._config=this._getConfig(e),this._timeout=null,this._setListeners()}static get VERSION(){return L}static get DefaultType(){return w}static get Default(){return T}show(){const t=s.Event(C);if(s(this._element).trigger(t),t.isDefaultPrevented())return;this._clearTimeout(),this._config.animation&&this._element.classList.add(A);const e=()=>{this._element.classList.remove(d),this._element.classList.add(l),s(this._element).trigger(v),this._config.autohide&&(this._timeout=setTimeout(()=>{this.hide()},this._config.delay))};if(this._element.classList.remove(f),o.reflow(this._element),this._element.classList.add(d),this._config.animation){const n=o.getTransitionDurationFromElement(this._element);s(this._element).one(o.TRANSITION_END,e).emulateTransitionEnd(n)}else e()}hide(){if(!this._element.classList.contains(l))return;const t=s.Event(p);s(this._element).trigger(t),!t.isDefaultPrevented()&&this._close()}dispose(){this._clearTimeout(),this._element.classList.contains(l)&&this._element.classList.remove(l),s(this._element).off(E),s.removeData(this._element,c),this._element=null,this._config=null}_getConfig(t){return t=h(h(h({},T),s(this._element).data()),typeof t=="object"&&t?t:{}),o.typeCheckConfig(a,t,this.constructor.DefaultType),t}_setListeners(){s(this._element).on(E,O,()=>this.hide())}_close(){const t=()=>{this._element.classList.add(f),s(this._element).trigger(y)};if(this._element.classList.remove(l),this._config.animation){const e=o.getTransitionDurationFromElement(this._element);s(this._element).one(o.TRANSITION_END,t).emulateTransitionEnd(e)}else t()}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static _jQueryInterface(t){return this.each(function(){const e=s(this);let n=e.data(c);const N=typeof t=="object"&&t;if(n||(n=new _(this,N),e.data(c,n)),typeof t=="string"){if(typeof n[t]=="undefined")throw new TypeError(`No method named "${t}"`);n[t](this)}})}}s.fn[a]=_._jQueryInterface;s.fn[a].Constructor=_;s.fn[a].noConflict=()=>(s.fn[a]=g,_._jQueryInterface);export{_ as default};
//# sourceMappingURL=toast-CKI4sL5r.js.map
