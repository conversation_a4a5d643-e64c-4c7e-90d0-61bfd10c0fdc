var d=Object.defineProperty,C=Object.defineProperties;var m=Object.getOwnPropertyDescriptors;var E=Object.getOwnPropertySymbols;var T=Object.prototype.hasOwnProperty,g=Object.prototype.propertyIsEnumerable;var h=(o,t,e)=>t in o?d(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,c=(o,t)=>{for(var e in t||(t={}))T.call(t,e)&&h(o,e,t[e]);if(E)for(var e of E(t))g.call(t,e)&&h(o,e,t[e]);return o},l=(o,t)=>C(o,m(t));import s from"jquery";import p from"./tooltip-B4WYtgWX.js";import"./popper-BdHdNNH2.js";import"./util-B8s7WWwa.js";const i="popover",_="4.6.2",a="bs.popover",n=`.${a}`,S=s.fn[i],u="bs-popover",$=new RegExp(`(^|\\s)${u}\\S+`,"g"),v="fade",N="show",A=".popover-header",O=".popover-body",I=l(c({},p.Default),{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),y=l(c({},p.DefaultType),{content:"(string|element|function)"}),D={HIDE:`hide${n}`,HIDDEN:`hidden${n}`,SHOW:`show${n}`,SHOWN:`shown${n}`,INSERTED:`inserted${n}`,CLICK:`click${n}`,FOCUSIN:`focusin${n}`,FOCUSOUT:`focusout${n}`,MOUSEENTER:`mouseenter${n}`,MOUSELEAVE:`mouseleave${n}`};class r extends p{static get VERSION(){return _}static get Default(){return I}static get NAME(){return i}static get DATA_KEY(){return a}static get Event(){return D}static get EVENT_KEY(){return n}static get DefaultType(){return y}isWithContent(){return this.getTitle()||this._getContent()}addAttachmentClass(t){s(this.getTipElement()).addClass(`${u}-${t}`)}getTipElement(){return this.tip=this.tip||s(this.config.template)[0],this.tip}setContent(){const t=s(this.getTipElement());this.setElementContent(t.find(A),this.getTitle());let e=this._getContent();typeof e=="function"&&(e=e.call(this.element)),this.setElementContent(t.find(O),e),t.removeClass(`${v} ${N}`)}_getContent(){return this.element.getAttribute("data-content")||this.config.content}_cleanTipClass(){const t=s(this.getTipElement()),e=t.attr("class").match($);e!==null&&e.length>0&&t.removeClass(e.join(""))}static _jQueryInterface(t){return this.each(function(){let e=s(this).data(a);const f=typeof t=="object"?t:null;if(!(!e&&/dispose|hide/.test(t))&&(e||(e=new r(this,f),s(this).data(a,e)),typeof t=="string")){if(typeof e[t]=="undefined")throw new TypeError(`No method named "${t}"`);e[t]()}})}}s.fn[i]=r._jQueryInterface;s.fn[i].Constructor=r;s.fn[i].noConflict=()=>(s.fn[i]=S,r._jQueryInterface);export{r as default};
//# sourceMappingURL=popover-pgn5fEK0.js.map
