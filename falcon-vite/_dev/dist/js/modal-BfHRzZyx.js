var L=Object.defineProperty;var b=Object.getOwnPropertySymbols;var B=Object.prototype.hasOwnProperty,M=Object.prototype.propertyIsEnumerable;var N=(d,t,i)=>t in d?L(d,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):d[t]=i,h=(d,t)=>{for(var i in t||(t={}))B.call(t,i)&&N(d,i,t[i]);if(b)for(var i of b(t))M.call(t,i)&&N(d,i,t[i]);return d};import e from"jquery";import{U as o}from"./util-B8s7WWwa.js";const _="modal",$="4.6.2",g="bs.modal",l=`.${g}`,W=".data-api",P=e.fn[_],y=27,V="modal-dialog-scrollable",x="modal-scrollbar-measure",F="modal-backdrop",C="modal-open",c="fade",u="show",D="modal-static",j=`hide${l}`,H=`hidePrevented${l}`,I=`hidden${l}`,R=`show${l}`,U=`shown${l}`,f=`focusin${l}`,A=`resize${l}`,S=`click.dismiss${l}`,w=`keydown.dismiss${l}`,Y=`mouseup.dismiss${l}`,k=`mousedown.dismiss${l}`,K=`click${l}${W}`,q=".modal-dialog",Q=".modal-body",z='[data-toggle="modal"]',G='[data-dismiss="modal"]',v=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",O=".sticky-top",T={backdrop:!0,keyboard:!0,focus:!0,show:!0},J={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"};class m{constructor(t,i){this._config=this._getConfig(i),this._element=t,this._dialog=t.querySelector(q),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}static get VERSION(){return $}static get Default(){return T}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown||this._isTransitioning)return;const i=e.Event(R,{relatedTarget:t});e(this._element).trigger(i),!i.isDefaultPrevented()&&(this._isShown=!0,e(this._element).hasClass(c)&&(this._isTransitioning=!0),this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),e(this._element).on(S,G,s=>this.hide(s)),e(this._dialog).on(k,()=>{e(this._element).one(Y,s=>{e(s.target).is(this._element)&&(this._ignoreBackdropClick=!0)})}),this._showBackdrop(()=>this._showElement(t)))}hide(t){if(t&&t.preventDefault(),!this._isShown||this._isTransitioning)return;const i=e.Event(j);if(e(this._element).trigger(i),!this._isShown||i.isDefaultPrevented())return;this._isShown=!1;const s=e(this._element).hasClass(c);if(s&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),e(document).off(f),e(this._element).removeClass(u),e(this._element).off(S),e(this._dialog).off(k),s){const n=o.getTransitionDurationFromElement(this._element);e(this._element).one(o.TRANSITION_END,a=>this._hideModal(a)).emulateTransitionEnd(n)}else this._hideModal()}dispose(){[window,this._element,this._dialog].forEach(t=>e(t).off(l)),e(document).off(f),e.removeData(this._element,g),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null}handleUpdate(){this._adjustDialog()}_getConfig(t){return t=h(h({},T),t),o.typeCheckConfig(_,t,J),t}_triggerBackdropTransition(){const t=e.Event(H);if(e(this._element).trigger(t),t.isDefaultPrevented())return;const i=this._element.scrollHeight>document.documentElement.clientHeight;i||(this._element.style.overflowY="hidden"),this._element.classList.add(D);const s=o.getTransitionDurationFromElement(this._dialog);e(this._element).off(o.TRANSITION_END),e(this._element).one(o.TRANSITION_END,()=>{this._element.classList.remove(D),i||e(this._element).one(o.TRANSITION_END,()=>{this._element.style.overflowY=""}).emulateTransitionEnd(this._element,s)}).emulateTransitionEnd(s),this._element.focus()}_showElement(t){const i=e(this._element).hasClass(c),s=this._dialog?this._dialog.querySelector(Q):null;(!this._element.parentNode||this._element.parentNode.nodeType!==Node.ELEMENT_NODE)&&document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),e(this._dialog).hasClass(V)&&s?s.scrollTop=0:this._element.scrollTop=0,i&&o.reflow(this._element),e(this._element).addClass(u),this._config.focus&&this._enforceFocus();const n=e.Event(U,{relatedTarget:t}),a=()=>{this._config.focus&&this._element.focus(),this._isTransitioning=!1,e(this._element).trigger(n)};if(i){const r=o.getTransitionDurationFromElement(this._dialog);e(this._dialog).one(o.TRANSITION_END,a).emulateTransitionEnd(r)}else a()}_enforceFocus(){e(document).off(f).on(f,t=>{document!==t.target&&this._element!==t.target&&e(this._element).has(t.target).length===0&&this._element.focus()})}_setEscapeEvent(){this._isShown?e(this._element).on(w,t=>{this._config.keyboard&&t.which===y?(t.preventDefault(),this.hide()):!this._config.keyboard&&t.which===y&&this._triggerBackdropTransition()}):this._isShown||e(this._element).off(w)}_setResizeEvent(){this._isShown?e(window).on(A,t=>this.handleUpdate(t)):e(window).off(A)}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._showBackdrop(()=>{e(document.body).removeClass(C),this._resetAdjustments(),this._resetScrollbar(),e(this._element).trigger(I)})}_removeBackdrop(){this._backdrop&&(e(this._backdrop).remove(),this._backdrop=null)}_showBackdrop(t){const i=e(this._element).hasClass(c)?c:"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className=F,i&&this._backdrop.classList.add(i),e(this._backdrop).appendTo(document.body),e(this._element).on(S,n=>{if(this._ignoreBackdropClick){this._ignoreBackdropClick=!1;return}n.target===n.currentTarget&&(this._config.backdrop==="static"?this._triggerBackdropTransition():this.hide())}),i&&o.reflow(this._backdrop),e(this._backdrop).addClass(u),!t)return;if(!i){t();return}const s=o.getTransitionDurationFromElement(this._backdrop);e(this._backdrop).one(o.TRANSITION_END,t).emulateTransitionEnd(s)}else if(!this._isShown&&this._backdrop){e(this._backdrop).removeClass(u);const s=()=>{this._removeBackdrop(),t&&t()};if(e(this._element).hasClass(c)){const n=o.getTransitionDurationFromElement(this._backdrop);e(this._backdrop).one(o.TRANSITION_END,s).emulateTransitionEnd(n)}else s()}else t&&t()}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&t&&(this._element.style.paddingLeft=`${this._scrollbarWidth}px`),this._isBodyOverflowing&&!t&&(this._element.style.paddingRight=`${this._scrollbarWidth}px`)}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}_checkScrollbar(){const t=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(t.left+t.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()}_setScrollbar(){if(this._isBodyOverflowing){const t=[].slice.call(document.querySelectorAll(v)),i=[].slice.call(document.querySelectorAll(O));e(t).each((a,r)=>{const E=r.style.paddingRight,p=e(r).css("padding-right");e(r).data("padding-right",E).css("padding-right",`${parseFloat(p)+this._scrollbarWidth}px`)}),e(i).each((a,r)=>{const E=r.style.marginRight,p=e(r).css("margin-right");e(r).data("margin-right",E).css("margin-right",`${parseFloat(p)-this._scrollbarWidth}px`)});const s=document.body.style.paddingRight,n=e(document.body).css("padding-right");e(document.body).data("padding-right",s).css("padding-right",`${parseFloat(n)+this._scrollbarWidth}px`)}e(document.body).addClass(C)}_resetScrollbar(){const t=[].slice.call(document.querySelectorAll(v));e(t).each((n,a)=>{const r=e(a).data("padding-right");e(a).removeData("padding-right"),a.style.paddingRight=r||""});const i=[].slice.call(document.querySelectorAll(`${O}`));e(i).each((n,a)=>{const r=e(a).data("margin-right");typeof r!="undefined"&&e(a).css("margin-right",r).removeData("margin-right")});const s=e(document.body).data("padding-right");e(document.body).removeData("padding-right"),document.body.style.paddingRight=s||""}_getScrollbarWidth(){const t=document.createElement("div");t.className=x,document.body.appendChild(t);const i=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),i}static _jQueryInterface(t,i){return this.each(function(){let s=e(this).data(g);const n=h(h(h({},T),e(this).data()),typeof t=="object"&&t?t:{});if(s||(s=new m(this,n),e(this).data(g,s)),typeof t=="string"){if(typeof s[t]=="undefined")throw new TypeError(`No method named "${t}"`);s[t](i)}else n.show&&s.show(i)})}}e(document).on(K,z,function(d){let t;const i=o.getSelectorFromElement(this);i&&(t=document.querySelector(i));const s=e(t).data(g)?"toggle":h(h({},e(t).data()),e(this).data());(this.tagName==="A"||this.tagName==="AREA")&&d.preventDefault();const n=e(t).one(R,a=>{a.isDefaultPrevented()||n.one(I,()=>{e(this).is(":visible")&&this.focus()})});m._jQueryInterface.call(e(t),s,this)});e.fn[_]=m._jQueryInterface;e.fn[_].Constructor=m;e.fn[_].noConflict=()=>(e.fn[_]=P,m._jQueryInterface);export{m as default};
//# sourceMappingURL=modal-BfHRzZyx.js.map
