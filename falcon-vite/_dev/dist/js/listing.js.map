{"version": 3, "file": "listing.js", "sources": ["../../node_modules/.pnpm/nouislider@15.8.1/node_modules/nouislider/dist/nouislider.mjs", "../../node_modules/.pnpm/wnumb@1.2.0/node_modules/wnumb/wNumb.js", "../../js/listing/components/filters/FiltersUrlHandler.js", "../../js/listing/components/filters/RangeSlider.js", "../../js/listing/components/filters/FiltersRangeSliders.js", "../../js/listing/components/filters/Filters.js", "../../js/listing/index.js"], "sourcesContent": ["\"use strict\";\nexport var PipsMode;\n(function (PipsMode) {\n    PipsMode[\"Range\"] = \"range\";\n    PipsMode[\"Steps\"] = \"steps\";\n    PipsMode[\"Positions\"] = \"positions\";\n    PipsMode[\"Count\"] = \"count\";\n    PipsMode[\"Values\"] = \"values\";\n})(PipsMode || (PipsMode = {}));\nexport var PipsType;\n(function (PipsType) {\n    PipsType[PipsType[\"None\"] = -1] = \"None\";\n    PipsType[PipsType[\"NoValue\"] = 0] = \"NoValue\";\n    PipsType[PipsType[\"LargeValue\"] = 1] = \"LargeValue\";\n    PipsType[PipsType[\"SmallValue\"] = 2] = \"SmallValue\";\n})(PipsType || (PipsType = {}));\n//region Helper Methods\nfunction isValidFormatter(entry) {\n    return isValidPartialFormatter(entry) && typeof entry.from === \"function\";\n}\nfunction isValidPartialFormatter(entry) {\n    // partial formatters only need a to function and not a from function\n    return typeof entry === \"object\" && typeof entry.to === \"function\";\n}\nfunction removeElement(el) {\n    el.parentElement.removeChild(el);\n}\nfunction isSet(value) {\n    return value !== null && value !== undefined;\n}\n// Bindable version\nfunction preventDefault(e) {\n    e.preventDefault();\n}\n// Removes duplicates from an array.\nfunction unique(array) {\n    return array.filter(function (a) {\n        return !this[a] ? (this[a] = true) : false;\n    }, {});\n}\n// Round a value to the closest 'to'.\nfunction closest(value, to) {\n    return Math.round(value / to) * to;\n}\n// Current position of an element relative to the document.\nfunction offset(elem, orientation) {\n    var rect = elem.getBoundingClientRect();\n    var doc = elem.ownerDocument;\n    var docElem = doc.documentElement;\n    var pageOffset = getPageOffset(doc);\n    // getBoundingClientRect contains left scroll in Chrome on Android.\n    // I haven't found a feature detection that proves this. Worst case\n    // scenario on mis-match: the 'tap' feature on horizontal sliders breaks.\n    if (/webkit.*Chrome.*Mobile/i.test(navigator.userAgent)) {\n        pageOffset.x = 0;\n    }\n    return orientation ? rect.top + pageOffset.y - docElem.clientTop : rect.left + pageOffset.x - docElem.clientLeft;\n}\n// Checks whether a value is numerical.\nfunction isNumeric(a) {\n    return typeof a === \"number\" && !isNaN(a) && isFinite(a);\n}\n// Sets a class and removes it after [duration] ms.\nfunction addClassFor(element, className, duration) {\n    if (duration > 0) {\n        addClass(element, className);\n        setTimeout(function () {\n            removeClass(element, className);\n        }, duration);\n    }\n}\n// Limits a value to 0 - 100\nfunction limit(a) {\n    return Math.max(Math.min(a, 100), 0);\n}\n// Wraps a variable as an array, if it isn't one yet.\n// Note that an input array is returned by reference!\nfunction asArray(a) {\n    return Array.isArray(a) ? a : [a];\n}\n// Counts decimals\nfunction countDecimals(numStr) {\n    numStr = String(numStr);\n    var pieces = numStr.split(\".\");\n    return pieces.length > 1 ? pieces[1].length : 0;\n}\n// http://youmightnotneedjquery.com/#add_class\nfunction addClass(el, className) {\n    if (el.classList && !/\\s/.test(className)) {\n        el.classList.add(className);\n    }\n    else {\n        el.className += \" \" + className;\n    }\n}\n// http://youmightnotneedjquery.com/#remove_class\nfunction removeClass(el, className) {\n    if (el.classList && !/\\s/.test(className)) {\n        el.classList.remove(className);\n    }\n    else {\n        el.className = el.className.replace(new RegExp(\"(^|\\\\b)\" + className.split(\" \").join(\"|\") + \"(\\\\b|$)\", \"gi\"), \" \");\n    }\n}\n// https://plainjs.com/javascript/attributes/adding-removing-and-testing-for-classes-9/\nfunction hasClass(el, className) {\n    return el.classList ? el.classList.contains(className) : new RegExp(\"\\\\b\" + className + \"\\\\b\").test(el.className);\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/Window/scrollY#Notes\nfunction getPageOffset(doc) {\n    var supportPageOffset = window.pageXOffset !== undefined;\n    var isCSS1Compat = (doc.compatMode || \"\") === \"CSS1Compat\";\n    var x = supportPageOffset\n        ? window.pageXOffset\n        : isCSS1Compat\n            ? doc.documentElement.scrollLeft\n            : doc.body.scrollLeft;\n    var y = supportPageOffset\n        ? window.pageYOffset\n        : isCSS1Compat\n            ? doc.documentElement.scrollTop\n            : doc.body.scrollTop;\n    return {\n        x: x,\n        y: y,\n    };\n}\n// we provide a function to compute constants instead\n// of accessing window.* as soon as the module needs it\n// so that we do not compute anything if not needed\nfunction getActions() {\n    // Determine the events to bind. IE11 implements pointerEvents without\n    // a prefix, which breaks compatibility with the IE10 implementation.\n    return window.navigator.pointerEnabled\n        ? {\n            start: \"pointerdown\",\n            move: \"pointermove\",\n            end: \"pointerup\",\n        }\n        : window.navigator.msPointerEnabled\n            ? {\n                start: \"MSPointerDown\",\n                move: \"MSPointerMove\",\n                end: \"MSPointerUp\",\n            }\n            : {\n                start: \"mousedown touchstart\",\n                move: \"mousemove touchmove\",\n                end: \"mouseup touchend\",\n            };\n}\n// https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n// Issue #785\nfunction getSupportsPassive() {\n    var supportsPassive = false;\n    /* eslint-disable */\n    try {\n        var opts = Object.defineProperty({}, \"passive\", {\n            get: function () {\n                supportsPassive = true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener(\"test\", null, opts);\n    }\n    catch (e) { }\n    /* eslint-enable */\n    return supportsPassive;\n}\nfunction getSupportsTouchActionNone() {\n    return window.CSS && CSS.supports && CSS.supports(\"touch-action\", \"none\");\n}\n//endregion\n//region Range Calculation\n// Determine the size of a sub-range in relation to a full range.\nfunction subRangeRatio(pa, pb) {\n    return 100 / (pb - pa);\n}\n// (percentage) How many percent is this value of this range?\nfunction fromPercentage(range, value, startRange) {\n    return (value * 100) / (range[startRange + 1] - range[startRange]);\n}\n// (percentage) Where is this value on this range?\nfunction toPercentage(range, value) {\n    return fromPercentage(range, range[0] < 0 ? value + Math.abs(range[0]) : value - range[0], 0);\n}\n// (value) How much is this percentage on this range?\nfunction isPercentage(range, value) {\n    return (value * (range[1] - range[0])) / 100 + range[0];\n}\nfunction getJ(value, arr) {\n    var j = 1;\n    while (value >= arr[j]) {\n        j += 1;\n    }\n    return j;\n}\n// (percentage) Input a value, find where, on a scale of 0-100, it applies.\nfunction toStepping(xVal, xPct, value) {\n    if (value >= xVal.slice(-1)[0]) {\n        return 100;\n    }\n    var j = getJ(value, xVal);\n    var va = xVal[j - 1];\n    var vb = xVal[j];\n    var pa = xPct[j - 1];\n    var pb = xPct[j];\n    return pa + toPercentage([va, vb], value) / subRangeRatio(pa, pb);\n}\n// (value) Input a percentage, find where it is on the specified range.\nfunction fromStepping(xVal, xPct, value) {\n    // There is no range group that fits 100\n    if (value >= 100) {\n        return xVal.slice(-1)[0];\n    }\n    var j = getJ(value, xPct);\n    var va = xVal[j - 1];\n    var vb = xVal[j];\n    var pa = xPct[j - 1];\n    var pb = xPct[j];\n    return isPercentage([va, vb], (value - pa) * subRangeRatio(pa, pb));\n}\n// (percentage) Get the step that applies at a certain value.\nfunction getStep(xPct, xSteps, snap, value) {\n    if (value === 100) {\n        return value;\n    }\n    var j = getJ(value, xPct);\n    var a = xPct[j - 1];\n    var b = xPct[j];\n    // If 'snap' is set, steps are used as fixed points on the slider.\n    if (snap) {\n        // Find the closest position, a or b.\n        if (value - a > (b - a) / 2) {\n            return b;\n        }\n        return a;\n    }\n    if (!xSteps[j - 1]) {\n        return value;\n    }\n    return xPct[j - 1] + closest(value - xPct[j - 1], xSteps[j - 1]);\n}\n//endregion\n//region Spectrum\nvar Spectrum = /** @class */ (function () {\n    function Spectrum(entry, snap, singleStep) {\n        this.xPct = [];\n        this.xVal = [];\n        this.xSteps = [];\n        this.xNumSteps = [];\n        this.xHighestCompleteStep = [];\n        this.xSteps = [singleStep || false];\n        this.xNumSteps = [false];\n        this.snap = snap;\n        var index;\n        var ordered = [];\n        // Map the object keys to an array.\n        Object.keys(entry).forEach(function (index) {\n            ordered.push([asArray(entry[index]), index]);\n        });\n        // Sort all entries by value (numeric sort).\n        ordered.sort(function (a, b) {\n            return a[0][0] - b[0][0];\n        });\n        // Convert all entries to subranges.\n        for (index = 0; index < ordered.length; index++) {\n            this.handleEntryPoint(ordered[index][1], ordered[index][0]);\n        }\n        // Store the actual step values.\n        // xSteps is sorted in the same order as xPct and xVal.\n        this.xNumSteps = this.xSteps.slice(0);\n        // Convert all numeric steps to the percentage of the subrange they represent.\n        for (index = 0; index < this.xNumSteps.length; index++) {\n            this.handleStepPoint(index, this.xNumSteps[index]);\n        }\n    }\n    Spectrum.prototype.getDistance = function (value) {\n        var distances = [];\n        for (var index = 0; index < this.xNumSteps.length - 1; index++) {\n            distances[index] = fromPercentage(this.xVal, value, index);\n        }\n        return distances;\n    };\n    // Calculate the percentual distance over the whole scale of ranges.\n    // direction: 0 = backwards / 1 = forwards\n    Spectrum.prototype.getAbsoluteDistance = function (value, distances, direction) {\n        var xPct_index = 0;\n        // Calculate range where to start calculation\n        if (value < this.xPct[this.xPct.length - 1]) {\n            while (value > this.xPct[xPct_index + 1]) {\n                xPct_index++;\n            }\n        }\n        else if (value === this.xPct[this.xPct.length - 1]) {\n            xPct_index = this.xPct.length - 2;\n        }\n        // If looking backwards and the value is exactly at a range separator then look one range further\n        if (!direction && value === this.xPct[xPct_index + 1]) {\n            xPct_index++;\n        }\n        if (distances === null) {\n            distances = [];\n        }\n        var start_factor;\n        var rest_factor = 1;\n        var rest_rel_distance = distances[xPct_index];\n        var range_pct = 0;\n        var rel_range_distance = 0;\n        var abs_distance_counter = 0;\n        var range_counter = 0;\n        // Calculate what part of the start range the value is\n        if (direction) {\n            start_factor = (value - this.xPct[xPct_index]) / (this.xPct[xPct_index + 1] - this.xPct[xPct_index]);\n        }\n        else {\n            start_factor = (this.xPct[xPct_index + 1] - value) / (this.xPct[xPct_index + 1] - this.xPct[xPct_index]);\n        }\n        // Do until the complete distance across ranges is calculated\n        while (rest_rel_distance > 0) {\n            // Calculate the percentage of total range\n            range_pct = this.xPct[xPct_index + 1 + range_counter] - this.xPct[xPct_index + range_counter];\n            // Detect if the margin, padding or limit is larger then the current range and calculate\n            if (distances[xPct_index + range_counter] * rest_factor + 100 - start_factor * 100 > 100) {\n                // If larger then take the percentual distance of the whole range\n                rel_range_distance = range_pct * start_factor;\n                // Rest factor of relative percentual distance still to be calculated\n                rest_factor = (rest_rel_distance - 100 * start_factor) / distances[xPct_index + range_counter];\n                // Set start factor to 1 as for next range it does not apply.\n                start_factor = 1;\n            }\n            else {\n                // If smaller or equal then take the percentual distance of the calculate percentual part of that range\n                rel_range_distance = ((distances[xPct_index + range_counter] * range_pct) / 100) * rest_factor;\n                // No rest left as the rest fits in current range\n                rest_factor = 0;\n            }\n            if (direction) {\n                abs_distance_counter = abs_distance_counter - rel_range_distance;\n                // Limit range to first range when distance becomes outside of minimum range\n                if (this.xPct.length + range_counter >= 1) {\n                    range_counter--;\n                }\n            }\n            else {\n                abs_distance_counter = abs_distance_counter + rel_range_distance;\n                // Limit range to last range when distance becomes outside of maximum range\n                if (this.xPct.length - range_counter >= 1) {\n                    range_counter++;\n                }\n            }\n            // Rest of relative percentual distance still to be calculated\n            rest_rel_distance = distances[xPct_index + range_counter] * rest_factor;\n        }\n        return value + abs_distance_counter;\n    };\n    Spectrum.prototype.toStepping = function (value) {\n        value = toStepping(this.xVal, this.xPct, value);\n        return value;\n    };\n    Spectrum.prototype.fromStepping = function (value) {\n        return fromStepping(this.xVal, this.xPct, value);\n    };\n    Spectrum.prototype.getStep = function (value) {\n        value = getStep(this.xPct, this.xSteps, this.snap, value);\n        return value;\n    };\n    Spectrum.prototype.getDefaultStep = function (value, isDown, size) {\n        var j = getJ(value, this.xPct);\n        // When at the top or stepping down, look at the previous sub-range\n        if (value === 100 || (isDown && value === this.xPct[j - 1])) {\n            j = Math.max(j - 1, 1);\n        }\n        return (this.xVal[j] - this.xVal[j - 1]) / size;\n    };\n    Spectrum.prototype.getNearbySteps = function (value) {\n        var j = getJ(value, this.xPct);\n        return {\n            stepBefore: {\n                startValue: this.xVal[j - 2],\n                step: this.xNumSteps[j - 2],\n                highestStep: this.xHighestCompleteStep[j - 2],\n            },\n            thisStep: {\n                startValue: this.xVal[j - 1],\n                step: this.xNumSteps[j - 1],\n                highestStep: this.xHighestCompleteStep[j - 1],\n            },\n            stepAfter: {\n                startValue: this.xVal[j],\n                step: this.xNumSteps[j],\n                highestStep: this.xHighestCompleteStep[j],\n            },\n        };\n    };\n    Spectrum.prototype.countStepDecimals = function () {\n        var stepDecimals = this.xNumSteps.map(countDecimals);\n        return Math.max.apply(null, stepDecimals);\n    };\n    Spectrum.prototype.hasNoSize = function () {\n        return this.xVal[0] === this.xVal[this.xVal.length - 1];\n    };\n    // Outside testing\n    Spectrum.prototype.convert = function (value) {\n        return this.getStep(this.toStepping(value));\n    };\n    Spectrum.prototype.handleEntryPoint = function (index, value) {\n        var percentage;\n        // Covert min/max syntax to 0 and 100.\n        if (index === \"min\") {\n            percentage = 0;\n        }\n        else if (index === \"max\") {\n            percentage = 100;\n        }\n        else {\n            percentage = parseFloat(index);\n        }\n        // Check for correct input.\n        if (!isNumeric(percentage) || !isNumeric(value[0])) {\n            throw new Error(\"noUiSlider: 'range' value isn't numeric.\");\n        }\n        // Store values.\n        this.xPct.push(percentage);\n        this.xVal.push(value[0]);\n        var value1 = Number(value[1]);\n        // NaN will evaluate to false too, but to keep\n        // logging clear, set step explicitly. Make sure\n        // not to override the 'step' setting with false.\n        if (!percentage) {\n            if (!isNaN(value1)) {\n                this.xSteps[0] = value1;\n            }\n        }\n        else {\n            this.xSteps.push(isNaN(value1) ? false : value1);\n        }\n        this.xHighestCompleteStep.push(0);\n    };\n    Spectrum.prototype.handleStepPoint = function (i, n) {\n        // Ignore 'false' stepping.\n        if (!n) {\n            return;\n        }\n        // Step over zero-length ranges (#948);\n        if (this.xVal[i] === this.xVal[i + 1]) {\n            this.xSteps[i] = this.xHighestCompleteStep[i] = this.xVal[i];\n            return;\n        }\n        // Factor to range ratio\n        this.xSteps[i] =\n            fromPercentage([this.xVal[i], this.xVal[i + 1]], n, 0) / subRangeRatio(this.xPct[i], this.xPct[i + 1]);\n        var totalSteps = (this.xVal[i + 1] - this.xVal[i]) / this.xNumSteps[i];\n        var highestStep = Math.ceil(Number(totalSteps.toFixed(3)) - 1);\n        var step = this.xVal[i] + this.xNumSteps[i] * highestStep;\n        this.xHighestCompleteStep[i] = step;\n    };\n    return Spectrum;\n}());\n//endregion\n//region Options\n/*\tEvery input option is tested and parsed. This will prevent\n    endless validation in internal methods. These tests are\n    structured with an item for every option available. An\n    option can be marked as required by setting the 'r' flag.\n    The testing function is provided with three arguments:\n        - The provided value for the option;\n        - A reference to the options object;\n        - The name for the option;\n\n    The testing function returns false when an error is detected,\n    or true when everything is OK. It can also modify the option\n    object, to make sure all values can be correctly looped elsewhere. */\n//region Defaults\nvar defaultFormatter = {\n    to: function (value) {\n        return value === undefined ? \"\" : value.toFixed(2);\n    },\n    from: Number,\n};\nvar cssClasses = {\n    target: \"target\",\n    base: \"base\",\n    origin: \"origin\",\n    handle: \"handle\",\n    handleLower: \"handle-lower\",\n    handleUpper: \"handle-upper\",\n    touchArea: \"touch-area\",\n    horizontal: \"horizontal\",\n    vertical: \"vertical\",\n    background: \"background\",\n    connect: \"connect\",\n    connects: \"connects\",\n    ltr: \"ltr\",\n    rtl: \"rtl\",\n    textDirectionLtr: \"txt-dir-ltr\",\n    textDirectionRtl: \"txt-dir-rtl\",\n    draggable: \"draggable\",\n    drag: \"state-drag\",\n    tap: \"state-tap\",\n    active: \"active\",\n    tooltip: \"tooltip\",\n    pips: \"pips\",\n    pipsHorizontal: \"pips-horizontal\",\n    pipsVertical: \"pips-vertical\",\n    marker: \"marker\",\n    markerHorizontal: \"marker-horizontal\",\n    markerVertical: \"marker-vertical\",\n    markerNormal: \"marker-normal\",\n    markerLarge: \"marker-large\",\n    markerSub: \"marker-sub\",\n    value: \"value\",\n    valueHorizontal: \"value-horizontal\",\n    valueVertical: \"value-vertical\",\n    valueNormal: \"value-normal\",\n    valueLarge: \"value-large\",\n    valueSub: \"value-sub\",\n};\n// Namespaces of internal event listeners\nvar INTERNAL_EVENT_NS = {\n    tooltips: \".__tooltips\",\n    aria: \".__aria\",\n};\n//endregion\nfunction testStep(parsed, entry) {\n    if (!isNumeric(entry)) {\n        throw new Error(\"noUiSlider: 'step' is not numeric.\");\n    }\n    // The step option can still be used to set stepping\n    // for linear sliders. Overwritten if set in 'range'.\n    parsed.singleStep = entry;\n}\nfunction testKeyboardPageMultiplier(parsed, entry) {\n    if (!isNumeric(entry)) {\n        throw new Error(\"noUiSlider: 'keyboardPageMultiplier' is not numeric.\");\n    }\n    parsed.keyboardPageMultiplier = entry;\n}\nfunction testKeyboardMultiplier(parsed, entry) {\n    if (!isNumeric(entry)) {\n        throw new Error(\"noUiSlider: 'keyboardMultiplier' is not numeric.\");\n    }\n    parsed.keyboardMultiplier = entry;\n}\nfunction testKeyboardDefaultStep(parsed, entry) {\n    if (!isNumeric(entry)) {\n        throw new Error(\"noUiSlider: 'keyboardDefaultStep' is not numeric.\");\n    }\n    parsed.keyboardDefaultStep = entry;\n}\nfunction testRange(parsed, entry) {\n    // Filter incorrect input.\n    if (typeof entry !== \"object\" || Array.isArray(entry)) {\n        throw new Error(\"noUiSlider: 'range' is not an object.\");\n    }\n    // Catch missing start or end.\n    if (entry.min === undefined || entry.max === undefined) {\n        throw new Error(\"noUiSlider: Missing 'min' or 'max' in 'range'.\");\n    }\n    parsed.spectrum = new Spectrum(entry, parsed.snap || false, parsed.singleStep);\n}\nfunction testStart(parsed, entry) {\n    entry = asArray(entry);\n    // Validate input. Values aren't tested, as the public .val method\n    // will always provide a valid location.\n    if (!Array.isArray(entry) || !entry.length) {\n        throw new Error(\"noUiSlider: 'start' option is incorrect.\");\n    }\n    // Store the number of handles.\n    parsed.handles = entry.length;\n    // When the slider is initialized, the .val method will\n    // be called with the start options.\n    parsed.start = entry;\n}\nfunction testSnap(parsed, entry) {\n    if (typeof entry !== \"boolean\") {\n        throw new Error(\"noUiSlider: 'snap' option must be a boolean.\");\n    }\n    // Enforce 100% stepping within subranges.\n    parsed.snap = entry;\n}\nfunction testAnimate(parsed, entry) {\n    if (typeof entry !== \"boolean\") {\n        throw new Error(\"noUiSlider: 'animate' option must be a boolean.\");\n    }\n    // Enforce 100% stepping within subranges.\n    parsed.animate = entry;\n}\nfunction testAnimationDuration(parsed, entry) {\n    if (typeof entry !== \"number\") {\n        throw new Error(\"noUiSlider: 'animationDuration' option must be a number.\");\n    }\n    parsed.animationDuration = entry;\n}\nfunction testConnect(parsed, entry) {\n    var connect = [false];\n    var i;\n    // Map legacy options\n    if (entry === \"lower\") {\n        entry = [true, false];\n    }\n    else if (entry === \"upper\") {\n        entry = [false, true];\n    }\n    // Handle boolean options\n    if (entry === true || entry === false) {\n        for (i = 1; i < parsed.handles; i++) {\n            connect.push(entry);\n        }\n        connect.push(false);\n    }\n    // Reject invalid input\n    else if (!Array.isArray(entry) || !entry.length || entry.length !== parsed.handles + 1) {\n        throw new Error(\"noUiSlider: 'connect' option doesn't match handle count.\");\n    }\n    else {\n        connect = entry;\n    }\n    parsed.connect = connect;\n}\nfunction testOrientation(parsed, entry) {\n    // Set orientation to an a numerical value for easy\n    // array selection.\n    switch (entry) {\n        case \"horizontal\":\n            parsed.ort = 0;\n            break;\n        case \"vertical\":\n            parsed.ort = 1;\n            break;\n        default:\n            throw new Error(\"noUiSlider: 'orientation' option is invalid.\");\n    }\n}\nfunction testMargin(parsed, entry) {\n    if (!isNumeric(entry)) {\n        throw new Error(\"noUiSlider: 'margin' option must be numeric.\");\n    }\n    // Issue #582\n    if (entry === 0) {\n        return;\n    }\n    parsed.margin = parsed.spectrum.getDistance(entry);\n}\nfunction testLimit(parsed, entry) {\n    if (!isNumeric(entry)) {\n        throw new Error(\"noUiSlider: 'limit' option must be numeric.\");\n    }\n    parsed.limit = parsed.spectrum.getDistance(entry);\n    if (!parsed.limit || parsed.handles < 2) {\n        throw new Error(\"noUiSlider: 'limit' option is only supported on linear sliders with 2 or more handles.\");\n    }\n}\nfunction testPadding(parsed, entry) {\n    var index;\n    if (!isNumeric(entry) && !Array.isArray(entry)) {\n        throw new Error(\"noUiSlider: 'padding' option must be numeric or array of exactly 2 numbers.\");\n    }\n    if (Array.isArray(entry) && !(entry.length === 2 || isNumeric(entry[0]) || isNumeric(entry[1]))) {\n        throw new Error(\"noUiSlider: 'padding' option must be numeric or array of exactly 2 numbers.\");\n    }\n    if (entry === 0) {\n        return;\n    }\n    if (!Array.isArray(entry)) {\n        entry = [entry, entry];\n    }\n    // 'getDistance' returns false for invalid values.\n    parsed.padding = [parsed.spectrum.getDistance(entry[0]), parsed.spectrum.getDistance(entry[1])];\n    for (index = 0; index < parsed.spectrum.xNumSteps.length - 1; index++) {\n        // last \"range\" can't contain step size as it is purely an endpoint.\n        if (parsed.padding[0][index] < 0 || parsed.padding[1][index] < 0) {\n            throw new Error(\"noUiSlider: 'padding' option must be a positive number(s).\");\n        }\n    }\n    var totalPadding = entry[0] + entry[1];\n    var firstValue = parsed.spectrum.xVal[0];\n    var lastValue = parsed.spectrum.xVal[parsed.spectrum.xVal.length - 1];\n    if (totalPadding / (lastValue - firstValue) > 1) {\n        throw new Error(\"noUiSlider: 'padding' option must not exceed 100% of the range.\");\n    }\n}\nfunction testDirection(parsed, entry) {\n    // Set direction as a numerical value for easy parsing.\n    // Invert connection for RTL sliders, so that the proper\n    // handles get the connect/background classes.\n    switch (entry) {\n        case \"ltr\":\n            parsed.dir = 0;\n            break;\n        case \"rtl\":\n            parsed.dir = 1;\n            break;\n        default:\n            throw new Error(\"noUiSlider: 'direction' option was not recognized.\");\n    }\n}\nfunction testBehaviour(parsed, entry) {\n    // Make sure the input is a string.\n    if (typeof entry !== \"string\") {\n        throw new Error(\"noUiSlider: 'behaviour' must be a string containing options.\");\n    }\n    // Check if the string contains any keywords.\n    // None are required.\n    var tap = entry.indexOf(\"tap\") >= 0;\n    var drag = entry.indexOf(\"drag\") >= 0;\n    var fixed = entry.indexOf(\"fixed\") >= 0;\n    var snap = entry.indexOf(\"snap\") >= 0;\n    var hover = entry.indexOf(\"hover\") >= 0;\n    var unconstrained = entry.indexOf(\"unconstrained\") >= 0;\n    var invertConnects = entry.indexOf(\"invert-connects\") >= 0;\n    var dragAll = entry.indexOf(\"drag-all\") >= 0;\n    var smoothSteps = entry.indexOf(\"smooth-steps\") >= 0;\n    if (fixed) {\n        if (parsed.handles !== 2) {\n            throw new Error(\"noUiSlider: 'fixed' behaviour must be used with 2 handles\");\n        }\n        // Use margin to enforce fixed state\n        testMargin(parsed, parsed.start[1] - parsed.start[0]);\n    }\n    if (invertConnects && parsed.handles !== 2) {\n        throw new Error(\"noUiSlider: 'invert-connects' behaviour must be used with 2 handles\");\n    }\n    if (unconstrained && (parsed.margin || parsed.limit)) {\n        throw new Error(\"noUiSlider: 'unconstrained' behaviour cannot be used with margin or limit\");\n    }\n    parsed.events = {\n        tap: tap || snap,\n        drag: drag,\n        dragAll: dragAll,\n        smoothSteps: smoothSteps,\n        fixed: fixed,\n        snap: snap,\n        hover: hover,\n        unconstrained: unconstrained,\n        invertConnects: invertConnects,\n    };\n}\nfunction testTooltips(parsed, entry) {\n    if (entry === false) {\n        return;\n    }\n    if (entry === true || isValidPartialFormatter(entry)) {\n        parsed.tooltips = [];\n        for (var i = 0; i < parsed.handles; i++) {\n            parsed.tooltips.push(entry);\n        }\n    }\n    else {\n        entry = asArray(entry);\n        if (entry.length !== parsed.handles) {\n            throw new Error(\"noUiSlider: must pass a formatter for all handles.\");\n        }\n        entry.forEach(function (formatter) {\n            if (typeof formatter !== \"boolean\" && !isValidPartialFormatter(formatter)) {\n                throw new Error(\"noUiSlider: 'tooltips' must be passed a formatter or 'false'.\");\n            }\n        });\n        parsed.tooltips = entry;\n    }\n}\nfunction testHandleAttributes(parsed, entry) {\n    if (entry.length !== parsed.handles) {\n        throw new Error(\"noUiSlider: must pass a attributes for all handles.\");\n    }\n    parsed.handleAttributes = entry;\n}\nfunction testAriaFormat(parsed, entry) {\n    if (!isValidPartialFormatter(entry)) {\n        throw new Error(\"noUiSlider: 'ariaFormat' requires 'to' method.\");\n    }\n    parsed.ariaFormat = entry;\n}\nfunction testFormat(parsed, entry) {\n    if (!isValidFormatter(entry)) {\n        throw new Error(\"noUiSlider: 'format' requires 'to' and 'from' methods.\");\n    }\n    parsed.format = entry;\n}\nfunction testKeyboardSupport(parsed, entry) {\n    if (typeof entry !== \"boolean\") {\n        throw new Error(\"noUiSlider: 'keyboardSupport' option must be a boolean.\");\n    }\n    parsed.keyboardSupport = entry;\n}\nfunction testDocumentElement(parsed, entry) {\n    // This is an advanced option. Passed values are used without validation.\n    parsed.documentElement = entry;\n}\nfunction testCssPrefix(parsed, entry) {\n    if (typeof entry !== \"string\" && entry !== false) {\n        throw new Error(\"noUiSlider: 'cssPrefix' must be a string or `false`.\");\n    }\n    parsed.cssPrefix = entry;\n}\nfunction testCssClasses(parsed, entry) {\n    if (typeof entry !== \"object\") {\n        throw new Error(\"noUiSlider: 'cssClasses' must be an object.\");\n    }\n    if (typeof parsed.cssPrefix === \"string\") {\n        parsed.cssClasses = {};\n        Object.keys(entry).forEach(function (key) {\n            parsed.cssClasses[key] = parsed.cssPrefix + entry[key];\n        });\n    }\n    else {\n        parsed.cssClasses = entry;\n    }\n}\n// Test all developer settings and parse to assumption-safe values.\nfunction testOptions(options) {\n    // To prove a fix for #537, freeze options here.\n    // If the object is modified, an error will be thrown.\n    // Object.freeze(options);\n    var parsed = {\n        margin: null,\n        limit: null,\n        padding: null,\n        animate: true,\n        animationDuration: 300,\n        ariaFormat: defaultFormatter,\n        format: defaultFormatter,\n    };\n    // Tests are executed in the order they are presented here.\n    var tests = {\n        step: { r: false, t: testStep },\n        keyboardPageMultiplier: { r: false, t: testKeyboardPageMultiplier },\n        keyboardMultiplier: { r: false, t: testKeyboardMultiplier },\n        keyboardDefaultStep: { r: false, t: testKeyboardDefaultStep },\n        start: { r: true, t: testStart },\n        connect: { r: true, t: testConnect },\n        direction: { r: true, t: testDirection },\n        snap: { r: false, t: testSnap },\n        animate: { r: false, t: testAnimate },\n        animationDuration: { r: false, t: testAnimationDuration },\n        range: { r: true, t: testRange },\n        orientation: { r: false, t: testOrientation },\n        margin: { r: false, t: testMargin },\n        limit: { r: false, t: testLimit },\n        padding: { r: false, t: testPadding },\n        behaviour: { r: true, t: testBehaviour },\n        ariaFormat: { r: false, t: testAriaFormat },\n        format: { r: false, t: testFormat },\n        tooltips: { r: false, t: testTooltips },\n        keyboardSupport: { r: true, t: testKeyboardSupport },\n        documentElement: { r: false, t: testDocumentElement },\n        cssPrefix: { r: true, t: testCssPrefix },\n        cssClasses: { r: true, t: testCssClasses },\n        handleAttributes: { r: false, t: testHandleAttributes },\n    };\n    var defaults = {\n        connect: false,\n        direction: \"ltr\",\n        behaviour: \"tap\",\n        orientation: \"horizontal\",\n        keyboardSupport: true,\n        cssPrefix: \"noUi-\",\n        cssClasses: cssClasses,\n        keyboardPageMultiplier: 5,\n        keyboardMultiplier: 1,\n        keyboardDefaultStep: 10,\n    };\n    // AriaFormat defaults to regular format, if any.\n    if (options.format && !options.ariaFormat) {\n        options.ariaFormat = options.format;\n    }\n    // Run all options through a testing mechanism to ensure correct\n    // input. It should be noted that options might get modified to\n    // be handled properly. E.g. wrapping integers in arrays.\n    Object.keys(tests).forEach(function (name) {\n        // If the option isn't set, but it is required, throw an error.\n        if (!isSet(options[name]) && defaults[name] === undefined) {\n            if (tests[name].r) {\n                throw new Error(\"noUiSlider: '\" + name + \"' is required.\");\n            }\n            return;\n        }\n        tests[name].t(parsed, !isSet(options[name]) ? defaults[name] : options[name]);\n    });\n    // Forward pips options\n    parsed.pips = options.pips;\n    // All recent browsers accept unprefixed transform.\n    // We need -ms- for IE9 and -webkit- for older Android;\n    // Assume use of -webkit- if unprefixed and -ms- are not supported.\n    // https://caniuse.com/#feat=transforms2d\n    var d = document.createElement(\"div\");\n    var msPrefix = d.style.msTransform !== undefined;\n    var noPrefix = d.style.transform !== undefined;\n    parsed.transformRule = noPrefix ? \"transform\" : msPrefix ? \"msTransform\" : \"webkitTransform\";\n    // Pips don't move, so we can place them using left/top.\n    var styles = [\n        [\"left\", \"top\"],\n        [\"right\", \"bottom\"],\n    ];\n    parsed.style = styles[parsed.dir][parsed.ort];\n    return parsed;\n}\n//endregion\nfunction scope(target, options, originalOptions) {\n    var actions = getActions();\n    var supportsTouchActionNone = getSupportsTouchActionNone();\n    var supportsPassive = supportsTouchActionNone && getSupportsPassive();\n    // All variables local to 'scope' are prefixed with 'scope_'\n    // Slider DOM Nodes\n    var scope_Target = target;\n    var scope_Base;\n    var scope_ConnectBase;\n    var scope_Handles;\n    var scope_Connects;\n    var scope_Pips;\n    var scope_Tooltips;\n    // Slider state values\n    var scope_Spectrum = options.spectrum;\n    var scope_Values = [];\n    var scope_Locations = [];\n    var scope_HandleNumbers = [];\n    var scope_ActiveHandlesCount = 0;\n    var scope_Events = {};\n    var scope_ConnectsInverted = false;\n    // Document Nodes\n    var scope_Document = target.ownerDocument;\n    var scope_DocumentElement = options.documentElement || scope_Document.documentElement;\n    var scope_Body = scope_Document.body;\n    // For horizontal sliders in standard ltr documents,\n    // make .noUi-origin overflow to the left so the document doesn't scroll.\n    var scope_DirOffset = scope_Document.dir === \"rtl\" || options.ort === 1 ? 0 : 100;\n    // Creates a node, adds it to target, returns the new node.\n    function addNodeTo(addTarget, className) {\n        var div = scope_Document.createElement(\"div\");\n        if (className) {\n            addClass(div, className);\n        }\n        addTarget.appendChild(div);\n        return div;\n    }\n    // Append a origin to the base\n    function addOrigin(base, handleNumber) {\n        var origin = addNodeTo(base, options.cssClasses.origin);\n        var handle = addNodeTo(origin, options.cssClasses.handle);\n        addNodeTo(handle, options.cssClasses.touchArea);\n        handle.setAttribute(\"data-handle\", String(handleNumber));\n        if (options.keyboardSupport) {\n            // https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/tabindex\n            // 0 = focusable and reachable\n            handle.setAttribute(\"tabindex\", \"0\");\n            handle.addEventListener(\"keydown\", function (event) {\n                return eventKeydown(event, handleNumber);\n            });\n        }\n        if (options.handleAttributes !== undefined) {\n            var attributes_1 = options.handleAttributes[handleNumber];\n            Object.keys(attributes_1).forEach(function (attribute) {\n                handle.setAttribute(attribute, attributes_1[attribute]);\n            });\n        }\n        handle.setAttribute(\"role\", \"slider\");\n        handle.setAttribute(\"aria-orientation\", options.ort ? \"vertical\" : \"horizontal\");\n        if (handleNumber === 0) {\n            addClass(handle, options.cssClasses.handleLower);\n        }\n        else if (handleNumber === options.handles - 1) {\n            addClass(handle, options.cssClasses.handleUpper);\n        }\n        origin.handle = handle;\n        return origin;\n    }\n    // Insert nodes for connect elements\n    function addConnect(base, add) {\n        if (!add) {\n            return false;\n        }\n        return addNodeTo(base, options.cssClasses.connect);\n    }\n    // Add handles to the slider base.\n    function addElements(connectOptions, base) {\n        scope_ConnectBase = addNodeTo(base, options.cssClasses.connects);\n        scope_Handles = [];\n        scope_Connects = [];\n        scope_Connects.push(addConnect(scope_ConnectBase, connectOptions[0]));\n        // [::::O====O====O====]\n        // connectOptions = [0, 1, 1, 1]\n        for (var i = 0; i < options.handles; i++) {\n            // Keep a list of all added handles.\n            scope_Handles.push(addOrigin(base, i));\n            scope_HandleNumbers[i] = i;\n            scope_Connects.push(addConnect(scope_ConnectBase, connectOptions[i + 1]));\n        }\n    }\n    // Initialize a single slider.\n    function addSlider(addTarget) {\n        // Apply classes and data to the target.\n        addClass(addTarget, options.cssClasses.target);\n        if (options.dir === 0) {\n            addClass(addTarget, options.cssClasses.ltr);\n        }\n        else {\n            addClass(addTarget, options.cssClasses.rtl);\n        }\n        if (options.ort === 0) {\n            addClass(addTarget, options.cssClasses.horizontal);\n        }\n        else {\n            addClass(addTarget, options.cssClasses.vertical);\n        }\n        var textDirection = getComputedStyle(addTarget).direction;\n        if (textDirection === \"rtl\") {\n            addClass(addTarget, options.cssClasses.textDirectionRtl);\n        }\n        else {\n            addClass(addTarget, options.cssClasses.textDirectionLtr);\n        }\n        return addNodeTo(addTarget, options.cssClasses.base);\n    }\n    function addTooltip(handle, handleNumber) {\n        if (!options.tooltips || !options.tooltips[handleNumber]) {\n            return false;\n        }\n        return addNodeTo(handle.firstChild, options.cssClasses.tooltip);\n    }\n    function isSliderDisabled() {\n        return scope_Target.hasAttribute(\"disabled\");\n    }\n    // Disable the slider dragging if any handle is disabled\n    function isHandleDisabled(handleNumber) {\n        var handleOrigin = scope_Handles[handleNumber];\n        return handleOrigin.hasAttribute(\"disabled\");\n    }\n    function disable(handleNumber) {\n        if (handleNumber !== null && handleNumber !== undefined) {\n            scope_Handles[handleNumber].setAttribute(\"disabled\", \"\");\n            scope_Handles[handleNumber].handle.removeAttribute(\"tabindex\");\n        }\n        else {\n            scope_Target.setAttribute(\"disabled\", \"\");\n            scope_Handles.forEach(function (handle) {\n                handle.handle.removeAttribute(\"tabindex\");\n            });\n        }\n    }\n    function enable(handleNumber) {\n        if (handleNumber !== null && handleNumber !== undefined) {\n            scope_Handles[handleNumber].removeAttribute(\"disabled\");\n            scope_Handles[handleNumber].handle.setAttribute(\"tabindex\", \"0\");\n        }\n        else {\n            scope_Target.removeAttribute(\"disabled\");\n            scope_Handles.forEach(function (handle) {\n                handle.removeAttribute(\"disabled\");\n                handle.handle.setAttribute(\"tabindex\", \"0\");\n            });\n        }\n    }\n    function removeTooltips() {\n        if (scope_Tooltips) {\n            removeEvent(\"update\" + INTERNAL_EVENT_NS.tooltips);\n            scope_Tooltips.forEach(function (tooltip) {\n                if (tooltip) {\n                    removeElement(tooltip);\n                }\n            });\n            scope_Tooltips = null;\n        }\n    }\n    // The tooltips option is a shorthand for using the 'update' event.\n    function tooltips() {\n        removeTooltips();\n        // Tooltips are added with options.tooltips in original order.\n        scope_Tooltips = scope_Handles.map(addTooltip);\n        bindEvent(\"update\" + INTERNAL_EVENT_NS.tooltips, function (values, handleNumber, unencoded) {\n            if (!scope_Tooltips || !options.tooltips) {\n                return;\n            }\n            if (scope_Tooltips[handleNumber] === false) {\n                return;\n            }\n            var formattedValue = values[handleNumber];\n            if (options.tooltips[handleNumber] !== true) {\n                formattedValue = options.tooltips[handleNumber].to(unencoded[handleNumber]);\n            }\n            scope_Tooltips[handleNumber].innerHTML = formattedValue;\n        });\n    }\n    function aria() {\n        removeEvent(\"update\" + INTERNAL_EVENT_NS.aria);\n        bindEvent(\"update\" + INTERNAL_EVENT_NS.aria, function (values, handleNumber, unencoded, tap, positions) {\n            // Update Aria Values for all handles, as a change in one changes min and max values for the next.\n            scope_HandleNumbers.forEach(function (index) {\n                var handle = scope_Handles[index];\n                var min = checkHandlePosition(scope_Locations, index, 0, true, true, true);\n                var max = checkHandlePosition(scope_Locations, index, 100, true, true, true);\n                var now = positions[index];\n                // Formatted value for display\n                var text = String(options.ariaFormat.to(unencoded[index]));\n                // Map to slider range values\n                min = scope_Spectrum.fromStepping(min).toFixed(1);\n                max = scope_Spectrum.fromStepping(max).toFixed(1);\n                now = scope_Spectrum.fromStepping(now).toFixed(1);\n                handle.children[0].setAttribute(\"aria-valuemin\", min);\n                handle.children[0].setAttribute(\"aria-valuemax\", max);\n                handle.children[0].setAttribute(\"aria-valuenow\", now);\n                handle.children[0].setAttribute(\"aria-valuetext\", text);\n            });\n        });\n    }\n    function getGroup(pips) {\n        // Use the range.\n        if (pips.mode === PipsMode.Range || pips.mode === PipsMode.Steps) {\n            return scope_Spectrum.xVal;\n        }\n        if (pips.mode === PipsMode.Count) {\n            if (pips.values < 2) {\n                throw new Error(\"noUiSlider: 'values' (>= 2) required for mode 'count'.\");\n            }\n            // Divide 0 - 100 in 'count' parts.\n            var interval = pips.values - 1;\n            var spread = 100 / interval;\n            var values = [];\n            // List these parts and have them handled as 'positions'.\n            while (interval--) {\n                values[interval] = interval * spread;\n            }\n            values.push(100);\n            return mapToRange(values, pips.stepped);\n        }\n        if (pips.mode === PipsMode.Positions) {\n            // Map all percentages to on-range values.\n            return mapToRange(pips.values, pips.stepped);\n        }\n        if (pips.mode === PipsMode.Values) {\n            // If the value must be stepped, it needs to be converted to a percentage first.\n            if (pips.stepped) {\n                return pips.values.map(function (value) {\n                    // Convert to percentage, apply step, return to value.\n                    return scope_Spectrum.fromStepping(scope_Spectrum.getStep(scope_Spectrum.toStepping(value)));\n                });\n            }\n            // Otherwise, we can simply use the values.\n            return pips.values;\n        }\n        return []; // pips.mode = never\n    }\n    function mapToRange(values, stepped) {\n        return values.map(function (value) {\n            return scope_Spectrum.fromStepping(stepped ? scope_Spectrum.getStep(value) : value);\n        });\n    }\n    function generateSpread(pips) {\n        function safeIncrement(value, increment) {\n            // Avoid floating point variance by dropping the smallest decimal places.\n            return Number((value + increment).toFixed(7));\n        }\n        var group = getGroup(pips);\n        var indexes = {};\n        var firstInRange = scope_Spectrum.xVal[0];\n        var lastInRange = scope_Spectrum.xVal[scope_Spectrum.xVal.length - 1];\n        var ignoreFirst = false;\n        var ignoreLast = false;\n        var prevPct = 0;\n        // Create a copy of the group, sort it and filter away all duplicates.\n        group = unique(group.slice().sort(function (a, b) {\n            return a - b;\n        }));\n        // Make sure the range starts with the first element.\n        if (group[0] !== firstInRange) {\n            group.unshift(firstInRange);\n            ignoreFirst = true;\n        }\n        // Likewise for the last one.\n        if (group[group.length - 1] !== lastInRange) {\n            group.push(lastInRange);\n            ignoreLast = true;\n        }\n        group.forEach(function (current, index) {\n            // Get the current step and the lower + upper positions.\n            var step;\n            var i;\n            var q;\n            var low = current;\n            var high = group[index + 1];\n            var newPct;\n            var pctDifference;\n            var pctPos;\n            var type;\n            var steps;\n            var realSteps;\n            var stepSize;\n            var isSteps = pips.mode === PipsMode.Steps;\n            // When using 'steps' mode, use the provided steps.\n            // Otherwise, we'll step on to the next subrange.\n            if (isSteps) {\n                step = scope_Spectrum.xNumSteps[index];\n            }\n            // Default to a 'full' step.\n            if (!step) {\n                step = high - low;\n            }\n            // If high is undefined we are at the last subrange. Make sure it iterates once (#1088)\n            if (high === undefined) {\n                high = low;\n            }\n            // Make sure step isn't 0, which would cause an infinite loop (#654)\n            step = Math.max(step, 0.0000001);\n            // Find all steps in the subrange.\n            for (i = low; i <= high; i = safeIncrement(i, step)) {\n                // Get the percentage value for the current step,\n                // calculate the size for the subrange.\n                newPct = scope_Spectrum.toStepping(i);\n                pctDifference = newPct - prevPct;\n                steps = pctDifference / (pips.density || 1);\n                realSteps = Math.round(steps);\n                // This ratio represents the amount of percentage-space a point indicates.\n                // For a density 1 the points/percentage = 1. For density 2, that percentage needs to be re-divided.\n                // Round the percentage offset to an even number, then divide by two\n                // to spread the offset on both sides of the range.\n                stepSize = pctDifference / realSteps;\n                // Divide all points evenly, adding the correct number to this subrange.\n                // Run up to <= so that 100% gets a point, event if ignoreLast is set.\n                for (q = 1; q <= realSteps; q += 1) {\n                    // The ratio between the rounded value and the actual size might be ~1% off.\n                    // Correct the percentage offset by the number of points\n                    // per subrange. density = 1 will result in 100 points on the\n                    // full range, 2 for 50, 4 for 25, etc.\n                    pctPos = prevPct + q * stepSize;\n                    indexes[pctPos.toFixed(5)] = [scope_Spectrum.fromStepping(pctPos), 0];\n                }\n                // Determine the point type.\n                type = group.indexOf(i) > -1 ? PipsType.LargeValue : isSteps ? PipsType.SmallValue : PipsType.NoValue;\n                // Enforce the 'ignoreFirst' option by overwriting the type for 0.\n                if (!index && ignoreFirst && i !== high) {\n                    type = 0;\n                }\n                if (!(i === high && ignoreLast)) {\n                    // Mark the 'type' of this point. 0 = plain, 1 = real value, 2 = step value.\n                    indexes[newPct.toFixed(5)] = [i, type];\n                }\n                // Update the percentage count.\n                prevPct = newPct;\n            }\n        });\n        return indexes;\n    }\n    function addMarking(spread, filterFunc, formatter) {\n        var _a, _b;\n        var element = scope_Document.createElement(\"div\");\n        var valueSizeClasses = (_a = {},\n            _a[PipsType.None] = \"\",\n            _a[PipsType.NoValue] = options.cssClasses.valueNormal,\n            _a[PipsType.LargeValue] = options.cssClasses.valueLarge,\n            _a[PipsType.SmallValue] = options.cssClasses.valueSub,\n            _a);\n        var markerSizeClasses = (_b = {},\n            _b[PipsType.None] = \"\",\n            _b[PipsType.NoValue] = options.cssClasses.markerNormal,\n            _b[PipsType.LargeValue] = options.cssClasses.markerLarge,\n            _b[PipsType.SmallValue] = options.cssClasses.markerSub,\n            _b);\n        var valueOrientationClasses = [options.cssClasses.valueHorizontal, options.cssClasses.valueVertical];\n        var markerOrientationClasses = [options.cssClasses.markerHorizontal, options.cssClasses.markerVertical];\n        addClass(element, options.cssClasses.pips);\n        addClass(element, options.ort === 0 ? options.cssClasses.pipsHorizontal : options.cssClasses.pipsVertical);\n        function getClasses(type, source) {\n            var a = source === options.cssClasses.value;\n            var orientationClasses = a ? valueOrientationClasses : markerOrientationClasses;\n            var sizeClasses = a ? valueSizeClasses : markerSizeClasses;\n            return source + \" \" + orientationClasses[options.ort] + \" \" + sizeClasses[type];\n        }\n        function addSpread(offset, value, type) {\n            // Apply the filter function, if it is set.\n            type = filterFunc ? filterFunc(value, type) : type;\n            if (type === PipsType.None) {\n                return;\n            }\n            // Add a marker for every point\n            var node = addNodeTo(element, false);\n            node.className = getClasses(type, options.cssClasses.marker);\n            node.style[options.style] = offset + \"%\";\n            // Values are only appended for points marked '1' or '2'.\n            if (type > PipsType.NoValue) {\n                node = addNodeTo(element, false);\n                node.className = getClasses(type, options.cssClasses.value);\n                node.setAttribute(\"data-value\", String(value));\n                node.style[options.style] = offset + \"%\";\n                node.innerHTML = String(formatter.to(value));\n            }\n        }\n        // Append all points.\n        Object.keys(spread).forEach(function (offset) {\n            addSpread(offset, spread[offset][0], spread[offset][1]);\n        });\n        return element;\n    }\n    function removePips() {\n        if (scope_Pips) {\n            removeElement(scope_Pips);\n            scope_Pips = null;\n        }\n    }\n    function pips(pips) {\n        // Fix #669\n        removePips();\n        var spread = generateSpread(pips);\n        var filter = pips.filter;\n        var format = pips.format || {\n            to: function (value) {\n                return String(Math.round(value));\n            },\n        };\n        scope_Pips = scope_Target.appendChild(addMarking(spread, filter, format));\n        return scope_Pips;\n    }\n    // Shorthand for base dimensions.\n    function baseSize() {\n        var rect = scope_Base.getBoundingClientRect();\n        var alt = (\"offset\" + [\"Width\", \"Height\"][options.ort]);\n        return options.ort === 0 ? rect.width || scope_Base[alt] : rect.height || scope_Base[alt];\n    }\n    // Handler for attaching events trough a proxy.\n    function attachEvent(events, element, callback, data) {\n        // This function can be used to 'filter' events to the slider.\n        // element is a node, not a nodeList\n        var method = function (event) {\n            var e = fixEvent(event, data.pageOffset, data.target || element);\n            // fixEvent returns false if this event has a different target\n            // when handling (multi-) touch events;\n            if (!e) {\n                return false;\n            }\n            // doNotReject is passed by all end events to make sure released touches\n            // are not rejected, leaving the slider \"stuck\" to the cursor;\n            if (isSliderDisabled() && !data.doNotReject) {\n                return false;\n            }\n            // Stop if an active 'tap' transition is taking place.\n            if (hasClass(scope_Target, options.cssClasses.tap) && !data.doNotReject) {\n                return false;\n            }\n            // Ignore right or middle clicks on start #454\n            if (events === actions.start && e.buttons !== undefined && e.buttons > 1) {\n                return false;\n            }\n            // Ignore right or middle clicks on start #454\n            if (data.hover && e.buttons) {\n                return false;\n            }\n            // 'supportsPassive' is only true if a browser also supports touch-action: none in CSS.\n            // iOS safari does not, so it doesn't get to benefit from passive scrolling. iOS does support\n            // touch-action: manipulation, but that allows panning, which breaks\n            // sliders after zooming/on non-responsive pages.\n            // See: https://bugs.webkit.org/show_bug.cgi?id=133112\n            if (!supportsPassive) {\n                e.preventDefault();\n            }\n            e.calcPoint = e.points[options.ort];\n            // Call the event handler with the event [ and additional data ].\n            callback(e, data);\n            return;\n        };\n        var methods = [];\n        // Bind a closure on the target for every event type.\n        events.split(\" \").forEach(function (eventName) {\n            element.addEventListener(eventName, method, supportsPassive ? { passive: true } : false);\n            methods.push([eventName, method]);\n        });\n        return methods;\n    }\n    // Provide a clean event with standardized offset values.\n    function fixEvent(e, pageOffset, eventTarget) {\n        // Filter the event to register the type, which can be\n        // touch, mouse or pointer. Offset changes need to be\n        // made on an event specific basis.\n        var touch = e.type.indexOf(\"touch\") === 0;\n        var mouse = e.type.indexOf(\"mouse\") === 0;\n        var pointer = e.type.indexOf(\"pointer\") === 0;\n        var x = 0;\n        var y = 0;\n        // IE10 implemented pointer events with a prefix;\n        if (e.type.indexOf(\"MSPointer\") === 0) {\n            pointer = true;\n        }\n        // Erroneous events seem to be passed in occasionally on iOS/iPadOS after user finishes interacting with\n        // the slider. They appear to be of type MouseEvent, yet they don't have usual properties set. Ignore\n        // events that have no touches or buttons associated with them. (#1057, #1079, #1095)\n        if (e.type === \"mousedown\" && !e.buttons && !e.touches) {\n            return false;\n        }\n        // The only thing one handle should be concerned about is the touches that originated on top of it.\n        if (touch) {\n            // Returns true if a touch originated on the target.\n            var isTouchOnTarget = function (checkTouch) {\n                var target = checkTouch.target;\n                return (target === eventTarget ||\n                    eventTarget.contains(target) ||\n                    (e.composed && e.composedPath().shift() === eventTarget));\n            };\n            // In the case of touchstart events, we need to make sure there is still no more than one\n            // touch on the target so we look amongst all touches.\n            if (e.type === \"touchstart\") {\n                var targetTouches = Array.prototype.filter.call(e.touches, isTouchOnTarget);\n                // Do not support more than one touch per handle.\n                if (targetTouches.length > 1) {\n                    return false;\n                }\n                x = targetTouches[0].pageX;\n                y = targetTouches[0].pageY;\n            }\n            else {\n                // In the other cases, find on changedTouches is enough.\n                var targetTouch = Array.prototype.find.call(e.changedTouches, isTouchOnTarget);\n                // Cancel if the target touch has not moved.\n                if (!targetTouch) {\n                    return false;\n                }\n                x = targetTouch.pageX;\n                y = targetTouch.pageY;\n            }\n        }\n        pageOffset = pageOffset || getPageOffset(scope_Document);\n        if (mouse || pointer) {\n            x = e.clientX + pageOffset.x;\n            y = e.clientY + pageOffset.y;\n        }\n        e.pageOffset = pageOffset;\n        e.points = [x, y];\n        e.cursor = mouse || pointer; // Fix #435\n        return e;\n    }\n    // Translate a coordinate in the document to a percentage on the slider\n    function calcPointToPercentage(calcPoint) {\n        var location = calcPoint - offset(scope_Base, options.ort);\n        var proposal = (location * 100) / baseSize();\n        // Clamp proposal between 0% and 100%\n        // Out-of-bound coordinates may occur when .noUi-base pseudo-elements\n        // are used (e.g. contained handles feature)\n        proposal = limit(proposal);\n        return options.dir ? 100 - proposal : proposal;\n    }\n    // Find handle closest to a certain percentage on the slider\n    function getClosestHandle(clickedPosition) {\n        var smallestDifference = 100;\n        var handleNumber = false;\n        scope_Handles.forEach(function (handle, index) {\n            // Disabled handles are ignored\n            if (isHandleDisabled(index)) {\n                return;\n            }\n            var handlePosition = scope_Locations[index];\n            var differenceWithThisHandle = Math.abs(handlePosition - clickedPosition);\n            // Initial state\n            var clickAtEdge = differenceWithThisHandle === 100 && smallestDifference === 100;\n            // Difference with this handle is smaller than the previously checked handle\n            var isCloser = differenceWithThisHandle < smallestDifference;\n            var isCloserAfter = differenceWithThisHandle <= smallestDifference && clickedPosition > handlePosition;\n            if (isCloser || isCloserAfter || clickAtEdge) {\n                handleNumber = index;\n                smallestDifference = differenceWithThisHandle;\n            }\n        });\n        return handleNumber;\n    }\n    // Fire 'end' when a mouse or pen leaves the document.\n    function documentLeave(event, data) {\n        if (event.type === \"mouseout\" &&\n            event.target.nodeName === \"HTML\" &&\n            event.relatedTarget === null) {\n            eventEnd(event, data);\n        }\n    }\n    // Handle movement on document for handle and range drag.\n    function eventMove(event, data) {\n        // Fix #498\n        // Check value of .buttons in 'start' to work around a bug in IE10 mobile (data.buttonsProperty).\n        // https://connect.microsoft.com/IE/feedback/details/927005/mobile-ie10-windows-phone-buttons-property-of-pointermove-event-always-zero\n        // IE9 has .buttons and .which zero on mousemove.\n        // Firefox breaks the spec MDN defines.\n        if (navigator.appVersion.indexOf(\"MSIE 9\") === -1 && event.buttons === 0 && data.buttonsProperty !== 0) {\n            return eventEnd(event, data);\n        }\n        // Check if we are moving up or down\n        var movement = (options.dir ? -1 : 1) * (event.calcPoint - data.startCalcPoint);\n        // Convert the movement into a percentage of the slider width/height\n        var proposal = (movement * 100) / data.baseSize;\n        moveHandles(movement > 0, proposal, data.locations, data.handleNumbers, data.connect);\n    }\n    // Unbind move events on document, call callbacks.\n    function eventEnd(event, data) {\n        // The handle is no longer active, so remove the class.\n        if (data.handle) {\n            removeClass(data.handle, options.cssClasses.active);\n            scope_ActiveHandlesCount -= 1;\n        }\n        // Unbind the move and end events, which are added on 'start'.\n        data.listeners.forEach(function (c) {\n            scope_DocumentElement.removeEventListener(c[0], c[1]);\n        });\n        if (scope_ActiveHandlesCount === 0) {\n            // Remove dragging class.\n            removeClass(scope_Target, options.cssClasses.drag);\n            setZindex();\n            // Remove cursor styles and text-selection events bound to the body.\n            if (event.cursor) {\n                scope_Body.style.cursor = \"\";\n                scope_Body.removeEventListener(\"selectstart\", preventDefault);\n            }\n        }\n        if (options.events.smoothSteps) {\n            data.handleNumbers.forEach(function (handleNumber) {\n                setHandle(handleNumber, scope_Locations[handleNumber], true, true, false, false);\n            });\n            data.handleNumbers.forEach(function (handleNumber) {\n                fireEvent(\"update\", handleNumber);\n            });\n        }\n        data.handleNumbers.forEach(function (handleNumber) {\n            fireEvent(\"change\", handleNumber);\n            fireEvent(\"set\", handleNumber);\n            fireEvent(\"end\", handleNumber);\n        });\n    }\n    // Bind move events on document.\n    function eventStart(event, data) {\n        // Ignore event if any handle is disabled\n        if (data.handleNumbers.some(isHandleDisabled)) {\n            return;\n        }\n        var handle;\n        if (data.handleNumbers.length === 1) {\n            var handleOrigin = scope_Handles[data.handleNumbers[0]];\n            handle = handleOrigin.children[0];\n            scope_ActiveHandlesCount += 1;\n            // Mark the handle as 'active' so it can be styled.\n            addClass(handle, options.cssClasses.active);\n        }\n        // A drag should never propagate up to the 'tap' event.\n        event.stopPropagation();\n        // Record the event listeners.\n        var listeners = [];\n        // Attach the move and end events.\n        var moveEvent = attachEvent(actions.move, scope_DocumentElement, eventMove, {\n            // The event target has changed so we need to propagate the original one so that we keep\n            // relying on it to extract target touches.\n            target: event.target,\n            handle: handle,\n            connect: data.connect,\n            listeners: listeners,\n            startCalcPoint: event.calcPoint,\n            baseSize: baseSize(),\n            pageOffset: event.pageOffset,\n            handleNumbers: data.handleNumbers,\n            buttonsProperty: event.buttons,\n            locations: scope_Locations.slice(),\n        });\n        var endEvent = attachEvent(actions.end, scope_DocumentElement, eventEnd, {\n            target: event.target,\n            handle: handle,\n            listeners: listeners,\n            doNotReject: true,\n            handleNumbers: data.handleNumbers,\n        });\n        var outEvent = attachEvent(\"mouseout\", scope_DocumentElement, documentLeave, {\n            target: event.target,\n            handle: handle,\n            listeners: listeners,\n            doNotReject: true,\n            handleNumbers: data.handleNumbers,\n        });\n        // We want to make sure we pushed the listeners in the listener list rather than creating\n        // a new one as it has already been passed to the event handlers.\n        listeners.push.apply(listeners, moveEvent.concat(endEvent, outEvent));\n        // Text selection isn't an issue on touch devices,\n        // so adding cursor styles can be skipped.\n        if (event.cursor) {\n            // Prevent the 'I' cursor and extend the range-drag cursor.\n            scope_Body.style.cursor = getComputedStyle(event.target).cursor;\n            // Mark the target with a dragging state.\n            if (scope_Handles.length > 1) {\n                addClass(scope_Target, options.cssClasses.drag);\n            }\n            // Prevent text selection when dragging the handles.\n            // In noUiSlider <= 9.2.0, this was handled by calling preventDefault on mouse/touch start/move,\n            // which is scroll blocking. The selectstart event is supported by FireFox starting from version 52,\n            // meaning the only holdout is iOS Safari. This doesn't matter: text selection isn't triggered there.\n            // The 'cursor' flag is false.\n            // See: http://caniuse.com/#search=selectstart\n            scope_Body.addEventListener(\"selectstart\", preventDefault, false);\n        }\n        data.handleNumbers.forEach(function (handleNumber) {\n            fireEvent(\"start\", handleNumber);\n        });\n    }\n    // Move closest handle to tapped location.\n    function eventTap(event) {\n        // The tap event shouldn't propagate up\n        event.stopPropagation();\n        var proposal = calcPointToPercentage(event.calcPoint);\n        var handleNumber = getClosestHandle(proposal);\n        // Tackle the case that all handles are 'disabled'.\n        if (handleNumber === false) {\n            return;\n        }\n        // Flag the slider as it is now in a transitional state.\n        // Transition takes a configurable amount of ms (default 300). Re-enable the slider after that.\n        if (!options.events.snap) {\n            addClassFor(scope_Target, options.cssClasses.tap, options.animationDuration);\n        }\n        setHandle(handleNumber, proposal, true, true);\n        setZindex();\n        fireEvent(\"slide\", handleNumber, true);\n        fireEvent(\"update\", handleNumber, true);\n        if (!options.events.snap) {\n            fireEvent(\"change\", handleNumber, true);\n            fireEvent(\"set\", handleNumber, true);\n        }\n        else {\n            eventStart(event, { handleNumbers: [handleNumber] });\n        }\n    }\n    // Fires a 'hover' event for a hovered mouse/pen position.\n    function eventHover(event) {\n        var proposal = calcPointToPercentage(event.calcPoint);\n        var to = scope_Spectrum.getStep(proposal);\n        var value = scope_Spectrum.fromStepping(to);\n        Object.keys(scope_Events).forEach(function (targetEvent) {\n            if (\"hover\" === targetEvent.split(\".\")[0]) {\n                scope_Events[targetEvent].forEach(function (callback) {\n                    callback.call(scope_Self, value);\n                });\n            }\n        });\n    }\n    // Handles keydown on focused handles\n    // Don't move the document when pressing arrow keys on focused handles\n    function eventKeydown(event, handleNumber) {\n        if (isSliderDisabled() || isHandleDisabled(handleNumber)) {\n            return false;\n        }\n        var horizontalKeys = [\"Left\", \"Right\"];\n        var verticalKeys = [\"Down\", \"Up\"];\n        var largeStepKeys = [\"PageDown\", \"PageUp\"];\n        var edgeKeys = [\"Home\", \"End\"];\n        if (options.dir && !options.ort) {\n            // On an right-to-left slider, the left and right keys act inverted\n            horizontalKeys.reverse();\n        }\n        else if (options.ort && !options.dir) {\n            // On a top-to-bottom slider, the up and down keys act inverted\n            verticalKeys.reverse();\n            largeStepKeys.reverse();\n        }\n        // Strip \"Arrow\" for IE compatibility. https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/key\n        var key = event.key.replace(\"Arrow\", \"\");\n        var isLargeDown = key === largeStepKeys[0];\n        var isLargeUp = key === largeStepKeys[1];\n        var isDown = key === verticalKeys[0] || key === horizontalKeys[0] || isLargeDown;\n        var isUp = key === verticalKeys[1] || key === horizontalKeys[1] || isLargeUp;\n        var isMin = key === edgeKeys[0];\n        var isMax = key === edgeKeys[1];\n        if (!isDown && !isUp && !isMin && !isMax) {\n            return true;\n        }\n        event.preventDefault();\n        var to;\n        if (isUp || isDown) {\n            var direction = isDown ? 0 : 1;\n            var steps = getNextStepsForHandle(handleNumber);\n            var step = steps[direction];\n            // At the edge of a slider, do nothing\n            if (step === null) {\n                return false;\n            }\n            // No step set, use the default of 10% of the sub-range\n            if (step === false) {\n                step = scope_Spectrum.getDefaultStep(scope_Locations[handleNumber], isDown, options.keyboardDefaultStep);\n            }\n            if (isLargeUp || isLargeDown) {\n                step *= options.keyboardPageMultiplier;\n            }\n            else {\n                step *= options.keyboardMultiplier;\n            }\n            // Step over zero-length ranges (#948);\n            step = Math.max(step, 0.0000001);\n            // Decrement for down steps\n            step = (isDown ? -1 : 1) * step;\n            to = scope_Values[handleNumber] + step;\n        }\n        else if (isMax) {\n            // End key\n            to = options.spectrum.xVal[options.spectrum.xVal.length - 1];\n        }\n        else {\n            // Home key\n            to = options.spectrum.xVal[0];\n        }\n        setHandle(handleNumber, scope_Spectrum.toStepping(to), true, true);\n        fireEvent(\"slide\", handleNumber);\n        fireEvent(\"update\", handleNumber);\n        fireEvent(\"change\", handleNumber);\n        fireEvent(\"set\", handleNumber);\n        return false;\n    }\n    // Attach events to several slider parts.\n    function bindSliderEvents(behaviour) {\n        // Attach the standard drag event to the handles.\n        if (!behaviour.fixed) {\n            scope_Handles.forEach(function (handle, index) {\n                // These events are only bound to the visual handle\n                // element, not the 'real' origin element.\n                attachEvent(actions.start, handle.children[0], eventStart, {\n                    handleNumbers: [index],\n                });\n            });\n        }\n        // Attach the tap event to the slider base.\n        if (behaviour.tap) {\n            attachEvent(actions.start, scope_Base, eventTap, {});\n        }\n        // Fire hover events\n        if (behaviour.hover) {\n            attachEvent(actions.move, scope_Base, eventHover, {\n                hover: true,\n            });\n        }\n        // Make the range draggable.\n        if (behaviour.drag) {\n            scope_Connects.forEach(function (connect, index) {\n                if (connect === false || index === 0 || index === scope_Connects.length - 1) {\n                    return;\n                }\n                var handleBefore = scope_Handles[index - 1];\n                var handleAfter = scope_Handles[index];\n                var eventHolders = [connect];\n                var handlesToDrag = [handleBefore, handleAfter];\n                var handleNumbersToDrag = [index - 1, index];\n                addClass(connect, options.cssClasses.draggable);\n                // When the range is fixed, the entire range can\n                // be dragged by the handles. The handle in the first\n                // origin will propagate the start event upward,\n                // but it needs to be bound manually on the other.\n                if (behaviour.fixed) {\n                    eventHolders.push(handleBefore.children[0]);\n                    eventHolders.push(handleAfter.children[0]);\n                }\n                if (behaviour.dragAll) {\n                    handlesToDrag = scope_Handles;\n                    handleNumbersToDrag = scope_HandleNumbers;\n                }\n                eventHolders.forEach(function (eventHolder) {\n                    attachEvent(actions.start, eventHolder, eventStart, {\n                        handles: handlesToDrag,\n                        handleNumbers: handleNumbersToDrag,\n                        connect: connect,\n                    });\n                });\n            });\n        }\n    }\n    // Attach an event to this slider, possibly including a namespace\n    function bindEvent(namespacedEvent, callback) {\n        scope_Events[namespacedEvent] = scope_Events[namespacedEvent] || [];\n        scope_Events[namespacedEvent].push(callback);\n        // If the event bound is 'update,' fire it immediately for all handles.\n        if (namespacedEvent.split(\".\")[0] === \"update\") {\n            scope_Handles.forEach(function (a, index) {\n                fireEvent(\"update\", index);\n            });\n        }\n    }\n    function isInternalNamespace(namespace) {\n        return namespace === INTERNAL_EVENT_NS.aria || namespace === INTERNAL_EVENT_NS.tooltips;\n    }\n    // Undo attachment of event\n    function removeEvent(namespacedEvent) {\n        var event = namespacedEvent && namespacedEvent.split(\".\")[0];\n        var namespace = event ? namespacedEvent.substring(event.length) : namespacedEvent;\n        Object.keys(scope_Events).forEach(function (bind) {\n            var tEvent = bind.split(\".\")[0];\n            var tNamespace = bind.substring(tEvent.length);\n            if ((!event || event === tEvent) && (!namespace || namespace === tNamespace)) {\n                // only delete protected internal event if intentional\n                if (!isInternalNamespace(tNamespace) || namespace === tNamespace) {\n                    delete scope_Events[bind];\n                }\n            }\n        });\n    }\n    // External event handling\n    function fireEvent(eventName, handleNumber, tap) {\n        Object.keys(scope_Events).forEach(function (targetEvent) {\n            var eventType = targetEvent.split(\".\")[0];\n            if (eventName === eventType) {\n                scope_Events[targetEvent].forEach(function (callback) {\n                    callback.call(\n                    // Use the slider public API as the scope ('this')\n                    scope_Self, \n                    // Return values as array, so arg_1[arg_2] is always valid.\n                    scope_Values.map(options.format.to), \n                    // Handle index, 0 or 1\n                    handleNumber, \n                    // Un-formatted slider values\n                    scope_Values.slice(), \n                    // Event is fired by tap, true or false\n                    tap || false, \n                    // Left offset of the handle, in relation to the slider\n                    scope_Locations.slice(), \n                    // add the slider public API to an accessible parameter when this is unavailable\n                    scope_Self);\n                });\n            }\n        });\n    }\n    // Split out the handle positioning logic so the Move event can use it, too\n    function checkHandlePosition(reference, handleNumber, to, lookBackward, lookForward, getValue, smoothSteps) {\n        var distance;\n        // For sliders with multiple handles, limit movement to the other handle.\n        // Apply the margin option by adding it to the handle positions.\n        if (scope_Handles.length > 1 && !options.events.unconstrained) {\n            if (lookBackward && handleNumber > 0) {\n                distance = scope_Spectrum.getAbsoluteDistance(reference[handleNumber - 1], options.margin, false);\n                to = Math.max(to, distance);\n            }\n            if (lookForward && handleNumber < scope_Handles.length - 1) {\n                distance = scope_Spectrum.getAbsoluteDistance(reference[handleNumber + 1], options.margin, true);\n                to = Math.min(to, distance);\n            }\n        }\n        // The limit option has the opposite effect, limiting handles to a\n        // maximum distance from another. Limit must be > 0, as otherwise\n        // handles would be unmovable.\n        if (scope_Handles.length > 1 && options.limit) {\n            if (lookBackward && handleNumber > 0) {\n                distance = scope_Spectrum.getAbsoluteDistance(reference[handleNumber - 1], options.limit, false);\n                to = Math.min(to, distance);\n            }\n            if (lookForward && handleNumber < scope_Handles.length - 1) {\n                distance = scope_Spectrum.getAbsoluteDistance(reference[handleNumber + 1], options.limit, true);\n                to = Math.max(to, distance);\n            }\n        }\n        // The padding option keeps the handles a certain distance from the\n        // edges of the slider. Padding must be > 0.\n        if (options.padding) {\n            if (handleNumber === 0) {\n                distance = scope_Spectrum.getAbsoluteDistance(0, options.padding[0], false);\n                to = Math.max(to, distance);\n            }\n            if (handleNumber === scope_Handles.length - 1) {\n                distance = scope_Spectrum.getAbsoluteDistance(100, options.padding[1], true);\n                to = Math.min(to, distance);\n            }\n        }\n        if (!smoothSteps) {\n            to = scope_Spectrum.getStep(to);\n        }\n        // Limit percentage to the 0 - 100 range\n        to = limit(to);\n        // Return false if handle can't move\n        if (to === reference[handleNumber] && !getValue) {\n            return false;\n        }\n        return to;\n    }\n    // Uses slider orientation to create CSS rules. a = base value;\n    function inRuleOrder(v, a) {\n        var o = options.ort;\n        return (o ? a : v) + \", \" + (o ? v : a);\n    }\n    // Moves handle(s) by a percentage\n    // (bool, % to move, [% where handle started, ...], [index in scope_Handles, ...])\n    function moveHandles(upward, proposal, locations, handleNumbers, connect) {\n        var proposals = locations.slice();\n        // Store first handle now, so we still have it in case handleNumbers is reversed\n        var firstHandle = handleNumbers[0];\n        var smoothSteps = options.events.smoothSteps;\n        var b = [!upward, upward];\n        var f = [upward, !upward];\n        // Copy handleNumbers so we don't change the dataset\n        handleNumbers = handleNumbers.slice();\n        // Check to see which handle is 'leading'.\n        // If that one can't move the second can't either.\n        if (upward) {\n            handleNumbers.reverse();\n        }\n        // Step 1: get the maximum percentage that any of the handles can move\n        if (handleNumbers.length > 1) {\n            handleNumbers.forEach(function (handleNumber, o) {\n                var to = checkHandlePosition(proposals, handleNumber, proposals[handleNumber] + proposal, b[o], f[o], false, smoothSteps);\n                // Stop if one of the handles can't move.\n                if (to === false) {\n                    proposal = 0;\n                }\n                else {\n                    proposal = to - proposals[handleNumber];\n                    proposals[handleNumber] = to;\n                }\n            });\n        }\n        // If using one handle, check backward AND forward\n        else {\n            b = f = [true];\n        }\n        var state = false;\n        // Step 2: Try to set the handles with the found percentage\n        handleNumbers.forEach(function (handleNumber, o) {\n            state =\n                setHandle(handleNumber, locations[handleNumber] + proposal, b[o], f[o], false, smoothSteps) || state;\n        });\n        // Step 3: If a handle moved, fire events\n        if (state) {\n            handleNumbers.forEach(function (handleNumber) {\n                fireEvent(\"update\", handleNumber);\n                fireEvent(\"slide\", handleNumber);\n            });\n            // If target is a connect, then fire drag event\n            if (connect != undefined) {\n                fireEvent(\"drag\", firstHandle);\n            }\n        }\n    }\n    // Takes a base value and an offset. This offset is used for the connect bar size.\n    // In the initial design for this feature, the origin element was 1% wide.\n    // Unfortunately, a rounding bug in Chrome makes it impossible to implement this feature\n    // in this manner: https://bugs.chromium.org/p/chromium/issues/detail?id=798223\n    function transformDirection(a, b) {\n        return options.dir ? 100 - a - b : a;\n    }\n    // Updates scope_Locations and scope_Values, updates visual state\n    function updateHandlePosition(handleNumber, to) {\n        // Update locations.\n        scope_Locations[handleNumber] = to;\n        // Convert the value to the slider stepping/range.\n        scope_Values[handleNumber] = scope_Spectrum.fromStepping(to);\n        var translation = transformDirection(to, 0) - scope_DirOffset;\n        var translateRule = \"translate(\" + inRuleOrder(translation + \"%\", \"0\") + \")\";\n        scope_Handles[handleNumber].style[options.transformRule] = translateRule;\n        // sanity check for at least 2 handles (e.g. during setup)\n        if (options.events.invertConnects && scope_Locations.length > 1) {\n            // check if handles passed each other, but don't match the ConnectsInverted state\n            var handlesAreInOrder = scope_Locations.every(function (position, index, locations) {\n                return index === 0 || position >= locations[index - 1];\n            });\n            if (scope_ConnectsInverted !== !handlesAreInOrder) {\n                // invert connects when handles pass each other\n                invertConnects();\n                // invertConnects already updates all connect elements\n                return;\n            }\n        }\n        updateConnect(handleNumber);\n        updateConnect(handleNumber + 1);\n        if (scope_ConnectsInverted) {\n            // When connects are inverted, we also have to update adjacent connects\n            updateConnect(handleNumber - 1);\n            updateConnect(handleNumber + 2);\n        }\n    }\n    // Handles before the slider middle are stacked later = higher,\n    // Handles after the middle later is lower\n    // [[7] [8] .......... | .......... [5] [4]\n    function setZindex() {\n        scope_HandleNumbers.forEach(function (handleNumber) {\n            var dir = scope_Locations[handleNumber] > 50 ? -1 : 1;\n            var zIndex = 3 + (scope_Handles.length + dir * handleNumber);\n            scope_Handles[handleNumber].style.zIndex = String(zIndex);\n        });\n    }\n    // Test suggested values and apply margin, step.\n    // if exactInput is true, don't run checkHandlePosition, then the handle can be placed in between steps (#436)\n    function setHandle(handleNumber, to, lookBackward, lookForward, exactInput, smoothSteps) {\n        if (!exactInput) {\n            to = checkHandlePosition(scope_Locations, handleNumber, to, lookBackward, lookForward, false, smoothSteps);\n        }\n        if (to === false) {\n            return false;\n        }\n        updateHandlePosition(handleNumber, to);\n        return true;\n    }\n    // Updates style attribute for connect nodes\n    function updateConnect(index) {\n        // Skip connects set to false\n        if (!scope_Connects[index]) {\n            return;\n        }\n        // Create a copy of locations, so we can sort them for the local scope logic\n        var locations = scope_Locations.slice();\n        if (scope_ConnectsInverted) {\n            locations.sort(function (a, b) {\n                return a - b;\n            });\n        }\n        var l = 0;\n        var h = 100;\n        if (index !== 0) {\n            l = locations[index - 1];\n        }\n        if (index !== scope_Connects.length - 1) {\n            h = locations[index];\n        }\n        // We use two rules:\n        // 'translate' to change the left/top offset;\n        // 'scale' to change the width of the element;\n        // As the element has a width of 100%, a translation of 100% is equal to 100% of the parent (.noUi-base)\n        var connectWidth = h - l;\n        var translateRule = \"translate(\" + inRuleOrder(transformDirection(l, connectWidth) + \"%\", \"0\") + \")\";\n        var scaleRule = \"scale(\" + inRuleOrder(connectWidth / 100, \"1\") + \")\";\n        scope_Connects[index].style[options.transformRule] =\n            translateRule + \" \" + scaleRule;\n    }\n    // Parses value passed to .set method. Returns current value if not parse-able.\n    function resolveToValue(to, handleNumber) {\n        // Setting with null indicates an 'ignore'.\n        // Inputting 'false' is invalid.\n        if (to === null || to === false || to === undefined) {\n            return scope_Locations[handleNumber];\n        }\n        // If a formatted number was passed, attempt to decode it.\n        if (typeof to === \"number\") {\n            to = String(to);\n        }\n        to = options.format.from(to);\n        if (to !== false) {\n            to = scope_Spectrum.toStepping(to);\n        }\n        // If parsing the number failed, use the current value.\n        if (to === false || isNaN(to)) {\n            return scope_Locations[handleNumber];\n        }\n        return to;\n    }\n    // Set the slider value.\n    function valueSet(input, fireSetEvent, exactInput) {\n        var values = asArray(input);\n        var isInit = scope_Locations[0] === undefined;\n        // Event fires by default\n        fireSetEvent = fireSetEvent === undefined ? true : fireSetEvent;\n        // Animation is optional.\n        // Make sure the initial values were set before using animated placement.\n        if (options.animate && !isInit) {\n            addClassFor(scope_Target, options.cssClasses.tap, options.animationDuration);\n        }\n        // First pass, without lookAhead but with lookBackward. Values are set from left to right.\n        scope_HandleNumbers.forEach(function (handleNumber) {\n            setHandle(handleNumber, resolveToValue(values[handleNumber], handleNumber), true, false, exactInput);\n        });\n        var i = scope_HandleNumbers.length === 1 ? 0 : 1;\n        // Spread handles evenly across the slider if the range has no size (min=max)\n        if (isInit && scope_Spectrum.hasNoSize()) {\n            exactInput = true;\n            scope_Locations[0] = 0;\n            if (scope_HandleNumbers.length > 1) {\n                var space_1 = 100 / (scope_HandleNumbers.length - 1);\n                scope_HandleNumbers.forEach(function (handleNumber) {\n                    scope_Locations[handleNumber] = handleNumber * space_1;\n                });\n            }\n        }\n        // Secondary passes. Now that all base values are set, apply constraints.\n        // Iterate all handles to ensure constraints are applied for the entire slider (Issue #1009)\n        for (; i < scope_HandleNumbers.length; ++i) {\n            scope_HandleNumbers.forEach(function (handleNumber) {\n                setHandle(handleNumber, scope_Locations[handleNumber], true, true, exactInput);\n            });\n        }\n        setZindex();\n        scope_HandleNumbers.forEach(function (handleNumber) {\n            fireEvent(\"update\", handleNumber);\n            // Fire the event only for handles that received a new value, as per #579\n            if (values[handleNumber] !== null && fireSetEvent) {\n                fireEvent(\"set\", handleNumber);\n            }\n        });\n    }\n    // Reset slider to initial values\n    function valueReset(fireSetEvent) {\n        valueSet(options.start, fireSetEvent);\n    }\n    // Set value for a single handle\n    function valueSetHandle(handleNumber, value, fireSetEvent, exactInput) {\n        // Ensure numeric input\n        handleNumber = Number(handleNumber);\n        if (!(handleNumber >= 0 && handleNumber < scope_HandleNumbers.length)) {\n            throw new Error(\"noUiSlider: invalid handle number, got: \" + handleNumber);\n        }\n        // Look both backward and forward, since we don't want this handle to \"push\" other handles (#960);\n        // The exactInput argument can be used to ignore slider stepping (#436)\n        setHandle(handleNumber, resolveToValue(value, handleNumber), true, true, exactInput);\n        fireEvent(\"update\", handleNumber);\n        if (fireSetEvent) {\n            fireEvent(\"set\", handleNumber);\n        }\n    }\n    // Get the slider value.\n    function valueGet(unencoded) {\n        if (unencoded === void 0) { unencoded = false; }\n        if (unencoded) {\n            // return a copy of the raw values\n            return scope_Values.length === 1 ? scope_Values[0] : scope_Values.slice(0);\n        }\n        var values = scope_Values.map(options.format.to);\n        // If only one handle is used, return a single value.\n        if (values.length === 1) {\n            return values[0];\n        }\n        return values;\n    }\n    // Removes classes from the root and empties it.\n    function destroy() {\n        // remove protected internal listeners\n        removeEvent(INTERNAL_EVENT_NS.aria);\n        removeEvent(INTERNAL_EVENT_NS.tooltips);\n        Object.keys(options.cssClasses).forEach(function (key) {\n            removeClass(scope_Target, options.cssClasses[key]);\n        });\n        while (scope_Target.firstChild) {\n            scope_Target.removeChild(scope_Target.firstChild);\n        }\n        delete scope_Target.noUiSlider;\n    }\n    function getNextStepsForHandle(handleNumber) {\n        var location = scope_Locations[handleNumber];\n        var nearbySteps = scope_Spectrum.getNearbySteps(location);\n        var value = scope_Values[handleNumber];\n        var increment = nearbySteps.thisStep.step;\n        var decrement = null;\n        // If snapped, directly use defined step value\n        if (options.snap) {\n            return [\n                value - nearbySteps.stepBefore.startValue || null,\n                nearbySteps.stepAfter.startValue - value || null,\n            ];\n        }\n        // If the next value in this step moves into the next step,\n        // the increment is the start of the next step - the current value\n        if (increment !== false) {\n            if (value + increment > nearbySteps.stepAfter.startValue) {\n                increment = nearbySteps.stepAfter.startValue - value;\n            }\n        }\n        // If the value is beyond the starting point\n        if (value > nearbySteps.thisStep.startValue) {\n            decrement = nearbySteps.thisStep.step;\n        }\n        else if (nearbySteps.stepBefore.step === false) {\n            decrement = false;\n        }\n        // If a handle is at the start of a step, it always steps back into the previous step first\n        else {\n            decrement = value - nearbySteps.stepBefore.highestStep;\n        }\n        // Now, if at the slider edges, there is no in/decrement\n        if (location === 100) {\n            increment = null;\n        }\n        else if (location === 0) {\n            decrement = null;\n        }\n        // As per #391, the comparison for the decrement step can have some rounding issues.\n        var stepDecimals = scope_Spectrum.countStepDecimals();\n        // Round per #391\n        if (increment !== null && increment !== false) {\n            increment = Number(increment.toFixed(stepDecimals));\n        }\n        if (decrement !== null && decrement !== false) {\n            decrement = Number(decrement.toFixed(stepDecimals));\n        }\n        return [decrement, increment];\n    }\n    // Get the current step size for the slider.\n    function getNextSteps() {\n        return scope_HandleNumbers.map(getNextStepsForHandle);\n    }\n    // Updatable: margin, limit, padding, step, range, animate, snap\n    function updateOptions(optionsToUpdate, fireSetEvent) {\n        // Spectrum is created using the range, snap, direction and step options.\n        // 'snap' and 'step' can be updated.\n        // If 'snap' and 'step' are not passed, they should remain unchanged.\n        var v = valueGet();\n        var updateAble = [\n            \"margin\",\n            \"limit\",\n            \"padding\",\n            \"range\",\n            \"animate\",\n            \"snap\",\n            \"step\",\n            \"format\",\n            \"pips\",\n            \"tooltips\",\n            \"connect\",\n        ];\n        // Only change options that we're actually passed to update.\n        updateAble.forEach(function (name) {\n            // Check for undefined. null removes the value.\n            if (optionsToUpdate[name] !== undefined) {\n                originalOptions[name] = optionsToUpdate[name];\n            }\n        });\n        var newOptions = testOptions(originalOptions);\n        // Load new options into the slider state\n        updateAble.forEach(function (name) {\n            if (optionsToUpdate[name] !== undefined) {\n                options[name] = newOptions[name];\n            }\n        });\n        scope_Spectrum = newOptions.spectrum;\n        // Limit, margin and padding depend on the spectrum but are stored outside of it. (#677)\n        options.margin = newOptions.margin;\n        options.limit = newOptions.limit;\n        options.padding = newOptions.padding;\n        // Update pips, removes existing.\n        if (options.pips) {\n            pips(options.pips);\n        }\n        else {\n            removePips();\n        }\n        // Update tooltips, removes existing.\n        if (options.tooltips) {\n            tooltips();\n        }\n        else {\n            removeTooltips();\n        }\n        // Invalidate the current positioning so valueSet forces an update.\n        scope_Locations = [];\n        valueSet(isSet(optionsToUpdate.start) ? optionsToUpdate.start : v, fireSetEvent);\n        // Update connects only if it was set\n        if (optionsToUpdate.connect) {\n            updateConnectOption();\n        }\n    }\n    function updateConnectOption() {\n        // IE supported way of removing children including event handlers\n        while (scope_ConnectBase.firstChild) {\n            scope_ConnectBase.removeChild(scope_ConnectBase.firstChild);\n        }\n        // Adding new connects according to the new connect options\n        for (var i = 0; i <= options.handles; i++) {\n            scope_Connects[i] = addConnect(scope_ConnectBase, options.connect[i]);\n            updateConnect(i);\n        }\n        // re-adding drag events for the new connect elements\n        // to ignore the other events we have to negate the 'if (!behaviour.fixed)' check\n        bindSliderEvents({ drag: options.events.drag, fixed: true });\n    }\n    // Invert options for connect handles\n    function invertConnects() {\n        scope_ConnectsInverted = !scope_ConnectsInverted;\n        testConnect(options, \n        // inverse the connect boolean array\n        options.connect.map(function (b) { return !b; }));\n        updateConnectOption();\n    }\n    // Initialization steps\n    function setupSlider() {\n        // Create the base element, initialize HTML and set classes.\n        // Add handles and connect elements.\n        scope_Base = addSlider(scope_Target);\n        addElements(options.connect, scope_Base);\n        // Attach user events.\n        bindSliderEvents(options.events);\n        // Use the public value method to set the start values.\n        valueSet(options.start);\n        if (options.pips) {\n            pips(options.pips);\n        }\n        if (options.tooltips) {\n            tooltips();\n        }\n        aria();\n    }\n    setupSlider();\n    var scope_Self = {\n        destroy: destroy,\n        steps: getNextSteps,\n        on: bindEvent,\n        off: removeEvent,\n        get: valueGet,\n        set: valueSet,\n        setHandle: valueSetHandle,\n        reset: valueReset,\n        disable: disable,\n        enable: enable,\n        // Exposed for unit testing, don't use this in your application.\n        __moveHandles: function (upward, proposal, handleNumbers) {\n            moveHandles(upward, proposal, scope_Locations, handleNumbers);\n        },\n        options: originalOptions,\n        updateOptions: updateOptions,\n        target: scope_Target,\n        removePips: removePips,\n        removeTooltips: removeTooltips,\n        getPositions: function () {\n            return scope_Locations.slice();\n        },\n        getTooltips: function () {\n            return scope_Tooltips;\n        },\n        getOrigins: function () {\n            return scope_Handles;\n        },\n        pips: pips, // Issue #594\n    };\n    return scope_Self;\n}\n// Run the standard initializer\nfunction initialize(target, originalOptions) {\n    if (!target || !target.nodeName) {\n        throw new Error(\"noUiSlider: create requires a single element, got: \" + target);\n    }\n    // Throw an error if the slider was already initialized.\n    if (target.noUiSlider) {\n        throw new Error(\"noUiSlider: Slider was already initialized.\");\n    }\n    // Test the options and create the slider environment;\n    var options = testOptions(originalOptions);\n    var api = scope(target, options, originalOptions);\n    target.noUiSlider = api;\n    return api;\n}\nexport { initialize as create };\nexport { cssClasses };\nexport default {\n    // Exposed for unit testing, don't use this in your application.\n    __spectrum: Spectrum,\n    // A reference to the default classes, allows global changes.\n    // Use the cssClasses option for changes to one slider.\n    cssClasses: cssClasses,\n    create: initialize,\n};\n", "(function(factory) {\r\n  if (typeof define === \"function\" && define.amd) {\r\n    // AMD. Register as an anonymous module.\r\n    define([], factory);\r\n  } else if (typeof exports === \"object\") {\r\n    // Node/CommonJS\r\n    module.exports = factory();\r\n  } else {\r\n    // Browser globals\r\n    window.wNumb = factory();\r\n  }\r\n})(function() {\r\n  \"use strict\";\r\n\r\n  var FormatOptions = [\r\n    \"decimals\",\r\n    \"thousand\",\r\n    \"mark\",\r\n    \"prefix\",\r\n    \"suffix\",\r\n    \"encoder\",\r\n    \"decoder\",\r\n    \"negativeBefore\",\r\n    \"negative\",\r\n    \"edit\",\r\n    \"undo\"\r\n  ];\r\n\r\n  // General\r\n\r\n  // Reverse a string\r\n  function strReverse(a) {\r\n    return a\r\n      .split(\"\")\r\n      .reverse()\r\n      .join(\"\");\r\n  }\r\n\r\n  // Check if a string starts with a specified prefix.\r\n  function strStartsWith(input, match) {\r\n    return input.substring(0, match.length) === match;\r\n  }\r\n\r\n  // Check is a string ends in a specified suffix.\r\n  function strEndsWith(input, match) {\r\n    return input.slice(-1 * match.length) === match;\r\n  }\r\n\r\n  // Throw an error if formatting options are incompatible.\r\n  function throwEqualError(F, a, b) {\r\n    if ((F[a] || F[b]) && F[a] === F[b]) {\r\n      throw new Error(a);\r\n    }\r\n  }\r\n\r\n  // Check if a number is finite and not NaN\r\n  function isValidNumber(input) {\r\n    return typeof input === \"number\" && isFinite(input);\r\n  }\r\n\r\n  // Provide rounding-accurate toFixed method.\r\n  // Borrowed: http://stackoverflow.com/a/21323330/775265\r\n  function toFixed(value, exp) {\r\n    value = value.toString().split(\"e\");\r\n    value = Math.round(+(value[0] + \"e\" + (value[1] ? +value[1] + exp : exp)));\r\n    value = value.toString().split(\"e\");\r\n    return (+(value[0] + \"e\" + (value[1] ? +value[1] - exp : -exp))).toFixed(exp);\r\n  }\r\n\r\n  // Formatting\r\n\r\n  // Accept a number as input, output formatted string.\r\n  function formatTo(\r\n    decimals,\r\n    thousand,\r\n    mark,\r\n    prefix,\r\n    suffix,\r\n    encoder,\r\n    decoder,\r\n    negativeBefore,\r\n    negative,\r\n    edit,\r\n    undo,\r\n    input\r\n  ) {\r\n    var originalInput = input,\r\n      inputIsNegative,\r\n      inputPieces,\r\n      inputBase,\r\n      inputDecimals = \"\",\r\n      output = \"\";\r\n\r\n    // Apply user encoder to the input.\r\n    // Expected outcome: number.\r\n    if (encoder) {\r\n      input = encoder(input);\r\n    }\r\n\r\n    // Stop if no valid number was provided, the number is infinite or NaN.\r\n    if (!isValidNumber(input)) {\r\n      return false;\r\n    }\r\n\r\n    // Rounding away decimals might cause a value of -0\r\n    // when using very small ranges. Remove those cases.\r\n    if (decimals !== false && parseFloat(input.toFixed(decimals)) === 0) {\r\n      input = 0;\r\n    }\r\n\r\n    // Formatting is done on absolute numbers,\r\n    // decorated by an optional negative symbol.\r\n    if (input < 0) {\r\n      inputIsNegative = true;\r\n      input = Math.abs(input);\r\n    }\r\n\r\n    // Reduce the number of decimals to the specified option.\r\n    if (decimals !== false) {\r\n      input = toFixed(input, decimals);\r\n    }\r\n\r\n    // Transform the number into a string, so it can be split.\r\n    input = input.toString();\r\n\r\n    // Break the number on the decimal separator.\r\n    if (input.indexOf(\".\") !== -1) {\r\n      inputPieces = input.split(\".\");\r\n\r\n      inputBase = inputPieces[0];\r\n\r\n      if (mark) {\r\n        inputDecimals = mark + inputPieces[1];\r\n      }\r\n    } else {\r\n      // If it isn't split, the entire number will do.\r\n      inputBase = input;\r\n    }\r\n\r\n    // Group numbers in sets of three.\r\n    if (thousand) {\r\n      inputBase = strReverse(inputBase).match(/.{1,3}/g);\r\n      inputBase = strReverse(inputBase.join(strReverse(thousand)));\r\n    }\r\n\r\n    // If the number is negative, prefix with negation symbol.\r\n    if (inputIsNegative && negativeBefore) {\r\n      output += negativeBefore;\r\n    }\r\n\r\n    // Prefix the number\r\n    if (prefix) {\r\n      output += prefix;\r\n    }\r\n\r\n    // Normal negative option comes after the prefix. Defaults to '-'.\r\n    if (inputIsNegative && negative) {\r\n      output += negative;\r\n    }\r\n\r\n    // Append the actual number.\r\n    output += inputBase;\r\n    output += inputDecimals;\r\n\r\n    // Apply the suffix.\r\n    if (suffix) {\r\n      output += suffix;\r\n    }\r\n\r\n    // Run the output through a user-specified post-formatter.\r\n    if (edit) {\r\n      output = edit(output, originalInput);\r\n    }\r\n\r\n    // All done.\r\n    return output;\r\n  }\r\n\r\n  // Accept a sting as input, output decoded number.\r\n  function formatFrom(\r\n    decimals,\r\n    thousand,\r\n    mark,\r\n    prefix,\r\n    suffix,\r\n    encoder,\r\n    decoder,\r\n    negativeBefore,\r\n    negative,\r\n    edit,\r\n    undo,\r\n    input\r\n  ) {\r\n    var originalInput = input,\r\n      inputIsNegative,\r\n      output = \"\";\r\n\r\n    // User defined pre-decoder. Result must be a non empty string.\r\n    if (undo) {\r\n      input = undo(input);\r\n    }\r\n\r\n    // Test the input. Can't be empty.\r\n    if (!input || typeof input !== \"string\") {\r\n      return false;\r\n    }\r\n\r\n    // If the string starts with the negativeBefore value: remove it.\r\n    // Remember is was there, the number is negative.\r\n    if (negativeBefore && strStartsWith(input, negativeBefore)) {\r\n      input = input.replace(negativeBefore, \"\");\r\n      inputIsNegative = true;\r\n    }\r\n\r\n    // Repeat the same procedure for the prefix.\r\n    if (prefix && strStartsWith(input, prefix)) {\r\n      input = input.replace(prefix, \"\");\r\n    }\r\n\r\n    // And again for negative.\r\n    if (negative && strStartsWith(input, negative)) {\r\n      input = input.replace(negative, \"\");\r\n      inputIsNegative = true;\r\n    }\r\n\r\n    // Remove the suffix.\r\n    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/slice\r\n    if (suffix && strEndsWith(input, suffix)) {\r\n      input = input.slice(0, -1 * suffix.length);\r\n    }\r\n\r\n    // Remove the thousand grouping.\r\n    if (thousand) {\r\n      input = input.split(thousand).join(\"\");\r\n    }\r\n\r\n    // Set the decimal separator back to period.\r\n    if (mark) {\r\n      input = input.replace(mark, \".\");\r\n    }\r\n\r\n    // Prepend the negative symbol.\r\n    if (inputIsNegative) {\r\n      output += \"-\";\r\n    }\r\n\r\n    // Add the number\r\n    output += input;\r\n\r\n    // Trim all non-numeric characters (allow '.' and '-');\r\n    output = output.replace(/[^0-9\\.\\-.]/g, \"\");\r\n\r\n    // The value contains no parse-able number.\r\n    if (output === \"\") {\r\n      return false;\r\n    }\r\n\r\n    // Covert to number.\r\n    output = Number(output);\r\n\r\n    // Run the user-specified post-decoder.\r\n    if (decoder) {\r\n      output = decoder(output);\r\n    }\r\n\r\n    // Check is the output is valid, otherwise: return false.\r\n    if (!isValidNumber(output)) {\r\n      return false;\r\n    }\r\n\r\n    return output;\r\n  }\r\n\r\n  // Framework\r\n\r\n  // Validate formatting options\r\n  function validate(inputOptions) {\r\n    var i,\r\n      optionName,\r\n      optionValue,\r\n      filteredOptions = {};\r\n\r\n    if (inputOptions[\"suffix\"] === undefined) {\r\n      inputOptions[\"suffix\"] = inputOptions[\"postfix\"];\r\n    }\r\n\r\n    for (i = 0; i < FormatOptions.length; i += 1) {\r\n      optionName = FormatOptions[i];\r\n      optionValue = inputOptions[optionName];\r\n\r\n      if (optionValue === undefined) {\r\n        // Only default if negativeBefore isn't set.\r\n        if (optionName === \"negative\" && !filteredOptions.negativeBefore) {\r\n          filteredOptions[optionName] = \"-\";\r\n          // Don't set a default for mark when 'thousand' is set.\r\n        } else if (optionName === \"mark\" && filteredOptions.thousand !== \".\") {\r\n          filteredOptions[optionName] = \".\";\r\n        } else {\r\n          filteredOptions[optionName] = false;\r\n        }\r\n\r\n        // Floating points in JS are stable up to 7 decimals.\r\n      } else if (optionName === \"decimals\") {\r\n        if (optionValue >= 0 && optionValue < 8) {\r\n          filteredOptions[optionName] = optionValue;\r\n        } else {\r\n          throw new Error(optionName);\r\n        }\r\n\r\n        // These options, when provided, must be functions.\r\n      } else if (\r\n        optionName === \"encoder\" ||\r\n        optionName === \"decoder\" ||\r\n        optionName === \"edit\" ||\r\n        optionName === \"undo\"\r\n      ) {\r\n        if (typeof optionValue === \"function\") {\r\n          filteredOptions[optionName] = optionValue;\r\n        } else {\r\n          throw new Error(optionName);\r\n        }\r\n\r\n        // Other options are strings.\r\n      } else {\r\n        if (typeof optionValue === \"string\") {\r\n          filteredOptions[optionName] = optionValue;\r\n        } else {\r\n          throw new Error(optionName);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Some values can't be extracted from a\r\n    // string if certain combinations are present.\r\n    throwEqualError(filteredOptions, \"mark\", \"thousand\");\r\n    throwEqualError(filteredOptions, \"prefix\", \"negative\");\r\n    throwEqualError(filteredOptions, \"prefix\", \"negativeBefore\");\r\n\r\n    return filteredOptions;\r\n  }\r\n\r\n  // Pass all options as function arguments\r\n  function passAll(options, method, input) {\r\n    var i,\r\n      args = [];\r\n\r\n    // Add all options in order of FormatOptions\r\n    for (i = 0; i < FormatOptions.length; i += 1) {\r\n      args.push(options[FormatOptions[i]]);\r\n    }\r\n\r\n    // Append the input, then call the method, presenting all\r\n    // options as arguments.\r\n    args.push(input);\r\n    return method.apply(\"\", args);\r\n  }\r\n\r\n  function wNumb(options) {\r\n    if (!(this instanceof wNumb)) {\r\n      return new wNumb(options);\r\n    }\r\n\r\n    if (typeof options !== \"object\") {\r\n      return;\r\n    }\r\n\r\n    options = validate(options);\r\n\r\n    // Call 'formatTo' with proper arguments.\r\n    this.to = function(input) {\r\n      return passAll(options, formatTo, input);\r\n    };\r\n\r\n    // Call 'formatFrom' with proper arguments.\r\n    this.from = function(input) {\r\n      return passAll(options, formatFrom, input);\r\n    };\r\n  }\r\n\r\n  return wNumb;\r\n});\r\n", "class FiltersUrlHandler {\n  constructor() {\n    this.baseUrl = window.location.origin + window.location.pathname;\n    this.oldSearchUrl = null;\n    this.searchUrl = null;\n  }\n\n  setOldSearchUrl() {\n    this.oldSearchUrl = this.searchUrl;\n  }\n\n  getFiltersUrl() {\n    this.setOldSearchUrl();\n    return `${this.baseUrl}?q=${this.searchUrl}`;\n  }\n\n  setSearchUrl() {\n    const searchParams = new URLSearchParams(window.location.search);\n    this.searchUrl = searchParams.get('q');\n    this.oldSearchUrl = searchParams.get('q');\n  }\n\n  setRangeParams(group, { unit, from, to }) {\n    this.removeGroup(group);\n\n    this.appendParam(group, unit);\n    this.appendParam(group, from);\n    this.appendParam(group, to);\n  }\n\n  appendParam(group, prop) {\n    const oldSearchUrl = this.searchUrl || '';\n    let newSearchUrl = oldSearchUrl.length ? oldSearchUrl.split('/') : [];\n    let groupExist = false;\n    const newSearchUrlLength = newSearchUrl.length;\n    group = FiltersUrlHandler.specialEncode(group);\n    prop = FiltersUrlHandler.specialEncode(prop);\n\n    for (let i = 0; i < newSearchUrlLength; i += 1) {\n      const filterGroup = newSearchUrl[i];\n      const filterGroupArray = filterGroup.split('-');\n\n      if (filterGroupArray[0] === group) {\n        newSearchUrl[i] = `${newSearchUrl[i]}-${prop}`;\n        groupExist = true;\n        break;\n      }\n    }\n\n    if (!groupExist) {\n      newSearchUrl = [...newSearchUrl, `${group}-${prop}`];\n    }\n\n    this.searchUrl = FiltersUrlHandler.specialDecode(FiltersUrlHandler.formatSearchUrl(newSearchUrl));\n  }\n\n  removeGroup(group) {\n    const oldSearchUrl = this.searchUrl || '';\n    const newSearchUrl = oldSearchUrl.length ? oldSearchUrl.split('/') : [];\n    const newSearchUrlLength = newSearchUrl.length;\n\n    for (let i = 0; i < newSearchUrlLength; i += 1) {\n      const filterGroup = newSearchUrl[i];\n      const filterGroupArray = filterGroup.split('-');\n\n      if (filterGroupArray[0] === group) {\n        newSearchUrl.splice(i, 1);\n      }\n    }\n\n    this.searchUrl = FiltersUrlHandler.specialDecode(FiltersUrlHandler.formatSearchUrl(newSearchUrl));\n  }\n\n  static toString(value) {\n    return `${value}`;\n  }\n\n  static specialEncode(str) {\n    return FiltersUrlHandler.toString(str).replace('/', '[slash]');\n  }\n\n  static specialDecode(str) {\n    return FiltersUrlHandler.toString(str).replace('[slash]', '/');\n  }\n\n  removeParam(group, prop) {\n    const oldSearchUrl = this.searchUrl || '';\n    const newSearchUrl = oldSearchUrl.length ? oldSearchUrl.split('/') : [];\n    const newSearchUrlLength = newSearchUrl.length;\n\n    for (let i = 0; i < newSearchUrlLength; i += 1) {\n      const filterGroup = newSearchUrl[i];\n      const filterGroupArray = filterGroup.split('-');\n\n      if (filterGroupArray[0] === group) {\n        const filterResult = filterGroupArray.filter((el) => el !== prop);\n\n        if (filterResult.length === 1) {\n          newSearchUrl.splice(i, 1);\n        } else {\n          newSearchUrl[i] = filterResult.join('-');\n        }\n        break;\n      }\n    }\n\n    this.searchUrl = FiltersUrlHandler.specialDecode(FiltersUrlHandler.formatSearchUrl(newSearchUrl));\n  }\n\n  static formatSearchUrl(array) {\n    return array.join('/');\n  }\n}\n\nexport default FiltersUrlHandler;\n", "import $ from 'jquery';\nimport prestashop from '@/js/theme/components/prestashop';;\nimport noUiSlider from 'nouislider';\nimport wNumb from 'wnumb';\nimport FiltersUrlHandler from '@/js/listing/components/filters/FiltersUrlHandler';\n\nclass RangeSlider {\n  constructor(element) {\n    this.$slider = $(element);\n\n    this.setConfig();\n    this.setFormat();\n\n    this.initFilersSlider();\n\n    this.setEvents();\n  }\n\n  getSliderType() {\n    this.sliderType = this.$slider.data('slider-specifications') ? 'price' : 'weight';\n  }\n\n  setConfig() {\n    this.min = this.$slider.data('slider-min');\n    this.max = this.$slider.data('slider-max');\n    this.$parentContainer = this.$slider.closest('.js-input-range-slider-container');\n    this.$inputs = [this.$parentContainer.find('[data-action=\"range-from\"]'), this.$parentContainer.find('[data-action=\"range-to\"]')];\n\n    this.getSliderType();\n\n    if (this.sliderType === 'price') {\n      const {\n        currencySymbol,\n        positivePattern,\n      } = this.$slider.data('slider-specifications');\n\n      this.sign = currencySymbol;\n      this.positivePattern = positivePattern;\n      this.values = this.$slider.data('slider-values');\n      this.signPosition = this.positivePattern.indexOf('¤') === 0 ? 'prefix' : 'suffix';\n    } else if (this.sliderType === 'weight') {\n      const unit = this.$slider.data('slider-unit');\n\n      this.sign = unit;\n      this.values = this.$slider.data('slider-values');\n      this.signPosition = 'suffix';\n    }\n\n    if (!Array.isArray(this.values)) {\n      this.values = [this.min, this.max];\n    }\n  }\n\n  setFormat() {\n    this.format = wNumb({\n      mark: ',',\n      thousand: ' ',\n      decimals: 0,\n      [this.signPosition]:\n        this.signPosition === 'prefix' ? this.sign : ` ${this.sign}`,\n    });\n  }\n\n  initFilersSlider() {\n    this.sliderHandler = noUiSlider.create(this.$slider.get(0), {\n      start: this.values,\n      connect: [false, true, false],\n      range: {\n        min: this.min,\n        max: this.max,\n      },\n      format: this.format,\n    });\n  }\n\n  initFilersSliderInputs() {\n    this.setInputValues(this.values, true);\n  }\n\n  setInputValues(values, formatValue = false) {\n    this.$inputs.forEach((input, i) => {\n      const val = formatValue ? this.format.from(values[i]) : values[i];\n      $(input).val(val);\n    });\n  }\n\n  setEvents() {\n    this.sliderHandler.off('set', this.constructor.handlerSliderSet);\n    this.sliderHandler.on('set', this.constructor.handlerSliderSet);\n    this.sliderHandler.off('update', this.handlerSliderUpdate);\n    this.sliderHandler.on('update', this.handlerSliderUpdate);\n\n    this.$inputs.forEach(($input) => {\n      $input.off('focus', this.handleInputFocus);\n      $input.on('focus', this.handleInputFocus);\n      $input.off('blur', this.handleInputBlur);\n      $input.on('blur', this.handleInputBlur);\n      $input.on('keyup', this.handleInputKeyup);\n    });\n  }\n\n  static getInputAction($input) {\n    return $input.data('action');\n  }\n\n  getInputPositionInValue($input) {\n    const actionPosition = {\n      'range-from': 0,\n      'range-to': 1,\n    };\n\n    return actionPosition[this.constructor.getInputAction($input)];\n  }\n\n  handleInputFocus = ({ target }) => {\n    const $input = $(target);\n    $input.val(this.format.from($input.val()));\n  };\n\n  handleInputBlur = ({ target }) => {\n    const $input = $(target);\n    const value = $input.val();\n    const position = this.getInputPositionInValue($input);\n    const oldValues = this.values;\n    const newValues = [...oldValues];\n    newValues[position] = value;\n\n    if (value !== oldValues[position]) {\n      this.sliderHandler.set(newValues);\n    } else {\n      $input.val(this.format.to(parseFloat($input.val(), 10)));\n    }\n  };\n\n  handleInputKeyup = ({ target, keyCode }) => {\n    if (keyCode !== 13) {\n      return;\n    }\n    const $input = $(target);\n    const value = $input.val();\n    const position = this.getInputPositionInValue($input);\n    const oldValues = this.values;\n    const newValues = [...oldValues];\n    newValues[position] = value;\n\n    if (value !== oldValues[position]) {\n      this.sliderHandler.set(newValues);\n    } else {\n      $input.val(this.format.to(parseFloat($input.val(), 10)));\n    }\n  };\n\n  handlerSliderUpdate = (\n    values,\n  ) => {\n    this.setInputValues(values);\n  };\n\n  static handlerSliderSet(\n    values,\n    handle,\n    unencoded,\n    tap,\n    positions,\n    noUiSliderInstance,\n  ) {\n    const formatFunction = noUiSliderInstance.options.format;\n    const $target = $(noUiSliderInstance.target);\n    const group = $target.data('slider-label');\n    const unit = $target.data('slider-unit');\n    const [from, to] = values.map((val) => formatFunction.from(val));\n\n    const filtersHandler = new FiltersUrlHandler();\n    filtersHandler.setSearchUrl();\n    filtersHandler.setRangeParams(group, { unit, from, to });\n\n    const newUrl = filtersHandler.getFiltersUrl();\n    prestashop.emit('updateFacets', newUrl);\n  }\n}\n\nexport default RangeSlider;\n", "import $ from 'jquery';\nimport RangeSlider from '@/js/listing/components/filters/RangeSlider';\n\nclass FiltersRangeSliders {\n  static init() {\n    const $rangeSliders = $('.js-range-slider');\n\n    $rangeSliders.each((i, el) => {\n      /* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"slider\" }] */\n      const slider = new RangeSlider(el);\n    });\n  }\n}\n\nexport default FiltersRangeSliders;\n", "import prestashop from '@/js/theme/components/prestashop';;\nimport $ from 'jquery';\nimport FiltersRangeSliders from '@/js/listing/components/filters/FiltersRangeSliders';\n\nclass Filters {\n  constructor() {\n    this.$body = $('body');\n    this.setEvents();\n    this.rangeSliders = FiltersRangeSliders;\n    this.rangeSliders.init();\n  }\n\n  setEvents() {\n    prestashop.on('updatedProductList', () => {\n      prestashop.pageLoader.hideLoader();\n      this.rangeSliders.init();\n    });\n\n    prestashop.on('updateFacets', () => {\n      prestashop.pageLoader.showLoader();\n    });\n\n    this.$body.on('click', '.js-search-link', (event) => {\n      event.preventDefault();\n      prestashop.emit('updateFacets', $(event.target).closest('a').get(0).href);\n    });\n\n    this.$body.on('change', '[data-action=\"search-select\"]', ({ target }) => {\n      prestashop.emit('updateFacets', $(target).find('option:selected').data('href'));\n    });\n\n    this.$body.on('click', '.js-search-filters-clear-all', (event) => {\n      prestashop.emit('updateFacets', this.constructor.parseSearchUrl(event));\n    });\n\n    this.$body.on('change', '#search_filters input[data-search-url]', (event) => {\n      prestashop.emit('updateFacets', this.constructor.parseSearchUrl(event));\n    });\n  }\n\n  static parseSearchUrl(event) {\n    if (event.target.dataset.searchUrl !== undefined) {\n      return event.target.dataset.searchUrl;\n    }\n\n    if ($(event.target).parent()[0].dataset.searchUrl === undefined) {\n      throw new Error('Can not parse search URL');\n    }\n\n    return $(event.target).parent()[0].dataset.searchUrl;\n  }\n}\n\nexport default Filters;\n", "import $ from 'jquery';\nimport prestashop from '@/js/theme/components/prestashop';;\nimport Filters from '@/js/listing/components/filters/Filters';\n\nfunction updateProductListDOM(data) {\n  $(prestashop.themeSelectors.listing.searchFilters).replaceWith(\n    data.rendered_facets,\n  );\n  $(prestashop.themeSelectors.listing.activeSearchFilters).replaceWith(\n    data.rendered_active_filters,\n  );\n  $(prestashop.themeSelectors.listing.listTop).replaceWith(\n    data.rendered_products_top,\n  );\n\n  const renderedProducts = $(data.rendered_products);\n  const productSelectors = $(prestashop.themeSelectors.listing.product);\n\n  if (productSelectors.length > 0) {\n    productSelectors.removeClass().addClass(productSelectors.first().attr('class'));\n  } else {\n    productSelectors.removeClass().addClass(renderedProducts.first().attr('class'));\n  }\n\n  $(prestashop.themeSelectors.listing.list).replaceWith(renderedProducts);\n  $(prestashop.themeSelectors.listing.listBottom).replaceWith(data.rendered_products_bottom);\n\n  if (data.rendered_products_header) {\n    $(prestashop.themeSelectors.listing.listHeader).replaceWith(data.rendered_products_header);\n  }\n\n  prestashop.emit('updatedProductList', data);\n}\n\n$(() => {\n  /* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"filters\" }] */\n  const filters = new Filters();\n\n  prestashop.on('updateProductList', (data) => {\n    updateProductListDOM(data);\n    window.scrollTo(0, 0);\n  });\n\n  prestashop.on('updatedProductList', () => {\n    prestashop.pageLazyLoad.update();\n  });\n});\n"], "names": ["PipsMode", "PipsType", "is<PERSON><PERSON>d<PERSON><PERSON><PERSON><PERSON>", "entry", "isValidPartialFormatter", "removeElement", "el", "isSet", "value", "preventDefault", "e", "unique", "array", "a", "closest", "to", "offset", "elem", "orientation", "rect", "doc", "doc<PERSON><PERSON>", "pageOffset", "getPageOffset", "isNumeric", "addClassFor", "element", "className", "duration", "addClass", "removeClass", "limit", "asArray", "countDecimals", "numStr", "pieces", "hasClass", "supportPageOffset", "isCSS1Compat", "x", "y", "getActions", "getSupportsPassive", "supportsPassive", "opts", "getSupportsTouchActionNone", "subRangeRatio", "pa", "pb", "fromPercentage", "range", "startRange", "toPercentage", "isPercentage", "getJ", "arr", "j", "toStepping", "xVal", "xPct", "va", "vb", "fromStepping", "getStep", "xSteps", "snap", "b", "Spectrum", "singleStep", "index", "ordered", "distances", "direction", "xPct_index", "start_factor", "rest_factor", "rest_rel_distance", "range_pct", "rel_range_distance", "abs_distance_counter", "range_counter", "isDown", "size", "stepDecimals", "percentage", "value1", "i", "n", "totalSteps", "highestStep", "step", "defaultFormatter", "cssClasses", "INTERNAL_EVENT_NS", "testStep", "parsed", "testKeyboardPageMultiplier", "testKeyboardMultiplier", "testKeyboardDefaultStep", "testRange", "testStart", "testSnap", "testAnimate", "testAnimationDuration", "testConnect", "connect", "testOrientation", "test<PERSON><PERSON>gin", "testLimit", "testPadding", "totalPadding", "firstValue", "lastValue", "testDirection", "test<PERSON><PERSON><PERSON><PERSON>", "tap", "drag", "fixed", "hover", "unconstrained", "invertConnects", "dragAll", "smoothSteps", "testTooltips", "formatter", "testHandleAttributes", "testAriaFormat", "testFormat", "testKeyboardSupport", "testDocumentElement", "testCssPrefix", "testCssClasses", "key", "testOptions", "options", "tests", "defaults", "name", "d", "msPrefix", "noPrefix", "styles", "scope", "target", "originalOptions", "actions", "supportsTouchActionNone", "scope_Target", "scope_Base", "scope_ConnectBase", "scope_Handles", "scope_Connects", "scope_Pips", "scope_Tooltips", "scope_Spectrum", "scope_Values", "scope_Locations", "scope_HandleNumbers", "scope_ActiveHandlesCount", "scope_Events", "scope_ConnectsInverted", "scope_Document", "scope_DocumentElement", "scope_Body", "scope_DirOffset", "addNodeTo", "addTarget", "div", "addOrigin", "base", "handleNumber", "origin", "handle", "event", "eventKeydown", "attributes_1", "attribute", "addConnect", "add", "addElements", "connectOptions", "addSlider", "textDirection", "addTooltip", "isSliderDisabled", "isHandleDisabled", "handleOrigin", "disable", "enable", "removeTooltips", "removeEvent", "tooltip", "tooltips", "bindEvent", "values", "unencoded", "formattedValue", "aria", "positions", "min", "checkHandlePosition", "max", "now", "text", "getGroup", "pips", "interval", "spread", "mapToRange", "stepped", "generateSpread", "safeIncrement", "increment", "group", "indexes", "firstInRange", "lastInRange", "<PERSON><PERSON><PERSON><PERSON>", "ignoreLast", "prevPct", "current", "q", "low", "high", "newPct", "pctDifference", "pctPos", "type", "steps", "realSteps", "stepSize", "isSteps", "addMarking", "filterFunc", "_a", "_b", "valueSizeClasses", "markerSizeClasses", "valueOrientationClasses", "markerOrientationClasses", "getClasses", "source", "orientationClasses", "sizeClasses", "addSpread", "node", "removePips", "filter", "format", "baseSize", "alt", "attachEvent", "events", "callback", "data", "method", "fixEvent", "methods", "eventName", "eventTarget", "touch", "mouse", "pointer", "isTouchOnTarget", "checkTouch", "targetTouches", "targetTouch", "calcPointToPercentage", "calcPoint", "location", "proposal", "getClosestHandle", "clickedPosition", "smallestDifference", "handlePosition", "differenceWithThisHandle", "clickAtEdge", "isCloser", "isCloserAfter", "documentLeave", "eventEnd", "eventMove", "movement", "<PERSON><PERSON><PERSON><PERSON>", "c", "setZindex", "<PERSON><PERSON><PERSON><PERSON>", "fireEvent", "eventStart", "listeners", "moveEvent", "endEvent", "outEvent", "eventTap", "eventHover", "targetEvent", "scope_Self", "horizontalKeys", "verticalKeys", "largeStepKeys", "edgeKeys", "isLargeDown", "isLargeUp", "isUp", "isMin", "isMax", "getNextStepsForHandle", "bindSliderEvents", "behaviour", "handleBefore", "handleAfter", "eventHolders", "handlesToDrag", "handleNumbersToDrag", "eventHolder", "namespacedEvent", "isInternalNamespace", "namespace", "bind", "tEvent", "tNamespace", "eventType", "reference", "lookBackward", "lookForward", "getValue", "distance", "inRuleOrder", "v", "o", "upward", "locations", "handleNumbers", "proposals", "firstHandle", "f", "state", "transformDirection", "updateHandlePosition", "translation", "translateRule", "handlesAreInOrder", "position", "updateConnect", "dir", "zIndex", "exactInput", "l", "h", "connectWidth", "scaleRule", "resolveToValue", "valueSet", "input", "fireSetEvent", "isInit", "space_1", "valueReset", "valueSetHandle", "valueGet", "destroy", "nearbySteps", "decrement", "getNextSteps", "updateOptions", "optionsToUpdate", "updateAble", "newOptions", "updateConnectOption", "setupSlider", "initialize", "api", "noUiSlider", "factory", "module", "FormatOptions", "strReverse", "strStartsWith", "match", "strEndsWith", "throwEqualError", "F", "isValidNumber", "toFixed", "exp", "formatTo", "decimals", "thousand", "mark", "prefix", "suffix", "encoder", "decoder", "negativeBefore", "negative", "edit", "undo", "originalInput", "inputIsNegative", "inputPieces", "inputBase", "inputDecimals", "output", "formatFrom", "validate", "inputOptions", "optionName", "optionValue", "filteredOptions", "passAll", "args", "wNumb", "FiltersUrlHandler", "searchParams", "unit", "from", "prop", "oldSearchUrl", "newSearchUrl", "groupExist", "newSearchUrlLength", "str", "filterGroupArray", "filterResult", "RangeSlider", "__publicField", "$input", "$", "oldValues", "newValues", "keyCode", "currencySymbol", "positivePattern", "formatValue", "val", "noUiSliderInstance", "formatFunction", "$target", "<PERSON><PERSON><PERSON><PERSON>", "newUrl", "prestashop", "FiltersRangeSliders", "Filters", "updateProductListDOM", "renderedProducts", "productSelectors"], "mappings": "iVACO,IAAIA,IACV,SAAUA,EAAU,CACjBA,EAAS,MAAW,QACpBA,EAAS,MAAW,QACpBA,EAAS,UAAe,YACxBA,EAAS,MAAW,QACpBA,EAAS,OAAY,QACzB,GAAGA,KAAaA,GAAW,CAAA,EAAG,EACvB,IAAIC,GACV,SAAUA,EAAU,CACjBA,EAASA,EAAS,KAAU,EAAE,EAAI,OAClCA,EAASA,EAAS,QAAa,CAAC,EAAI,UACpCA,EAASA,EAAS,WAAgB,CAAC,EAAI,aACvCA,EAASA,EAAS,WAAgB,CAAC,EAAI,YAC3C,GAAGA,IAAaA,EAAW,CAAA,EAAG,EAE9B,SAASC,GAAiBC,EAAO,CAC7B,OAAOC,GAAwBD,CAAK,GAAK,OAAOA,EAAM,MAAS,UACnE,CACA,SAASC,GAAwBD,EAAO,CAEpC,OAAO,OAAOA,GAAU,UAAY,OAAOA,EAAM,IAAO,UAC5D,CACA,SAASE,GAAcC,EAAI,CACvBA,EAAG,cAAc,YAAYA,CAAE,CACnC,CACA,SAASC,GAAMC,EAAO,CAClB,OAAOA,GAAU,IACrB,CAEA,SAASC,GAAeC,EAAG,CACvBA,EAAE,eAAgB,CACtB,CAEA,SAASC,GAAOC,EAAO,CACnB,OAAOA,EAAM,OAAO,SAAUC,EAAG,CAC7B,OAAQ,KAAKA,CAAC,EAAuB,GAAlB,KAAKA,CAAC,EAAI,EAChC,EAAE,EAAE,CACT,CAEA,SAASC,GAAQN,EAAOO,EAAI,CACxB,OAAO,KAAK,MAAMP,EAAQO,CAAE,EAAIA,CACpC,CAEA,SAASC,GAAOC,EAAMC,EAAa,CAC/B,IAAIC,EAAOF,EAAK,sBAAuB,EACnCG,EAAMH,EAAK,cACXI,EAAUD,EAAI,gBACdE,EAAaC,GAAcH,CAAG,EAIlC,MAAI,0BAA0B,KAAK,UAAU,SAAS,IAClDE,EAAW,EAAI,GAEZJ,EAAcC,EAAK,IAAMG,EAAW,EAAID,EAAQ,UAAYF,EAAK,KAAOG,EAAW,EAAID,EAAQ,UAC1G,CAEA,SAASG,EAAUX,EAAG,CAClB,OAAO,OAAOA,GAAM,UAAY,CAAC,MAAMA,CAAC,GAAK,SAASA,CAAC,CAC3D,CAEA,SAASY,GAAYC,EAASC,EAAWC,EAAU,CAC3CA,EAAW,IACXC,EAASH,EAASC,CAAS,EAC3B,WAAW,UAAY,CACnBG,GAAYJ,EAASC,CAAS,CACjC,EAAEC,CAAQ,EAEnB,CAEA,SAASG,GAAMlB,EAAG,CACd,OAAO,KAAK,IAAI,KAAK,IAAIA,EAAG,GAAG,EAAG,CAAC,CACvC,CAGA,SAASmB,GAAQnB,EAAG,CAChB,OAAO,MAAM,QAAQA,CAAC,EAAIA,EAAI,CAACA,CAAC,CACpC,CAEA,SAASoB,GAAcC,EAAQ,CAC3BA,EAAS,OAAOA,CAAM,EACtB,IAAIC,EAASD,EAAO,MAAM,GAAG,EAC7B,OAAOC,EAAO,OAAS,EAAIA,EAAO,CAAC,EAAE,OAAS,CAClD,CAEA,SAASN,EAASvB,EAAIqB,EAAW,CACzBrB,EAAG,WAAa,CAAC,KAAK,KAAKqB,CAAS,EACpCrB,EAAG,UAAU,IAAIqB,CAAS,EAG1BrB,EAAG,WAAa,IAAMqB,CAE9B,CAEA,SAASG,GAAYxB,EAAIqB,EAAW,CAC5BrB,EAAG,WAAa,CAAC,KAAK,KAAKqB,CAAS,EACpCrB,EAAG,UAAU,OAAOqB,CAAS,EAG7BrB,EAAG,UAAYA,EAAG,UAAU,QAAQ,IAAI,OAAO,UAAYqB,EAAU,MAAM,GAAG,EAAE,KAAK,GAAG,EAAI,UAAW,IAAI,EAAG,GAAG,CAEzH,CAEA,SAASS,GAAS9B,EAAIqB,EAAW,CAC7B,OAAOrB,EAAG,UAAYA,EAAG,UAAU,SAASqB,CAAS,EAAI,IAAI,OAAO,MAAQA,EAAY,KAAK,EAAE,KAAKrB,EAAG,SAAS,CACpH,CAEA,SAASiB,GAAcH,EAAK,CACxB,IAAIiB,EAAoB,OAAO,cAAgB,OAC3CC,GAAgBlB,EAAI,YAAc,MAAQ,aAC1CmB,EAAIF,EACF,OAAO,YACPC,EACIlB,EAAI,gBAAgB,WACpBA,EAAI,KAAK,WACfoB,EAAIH,EACF,OAAO,YACPC,EACIlB,EAAI,gBAAgB,UACpBA,EAAI,KAAK,UACnB,MAAO,CACH,EAAGmB,EACH,EAAGC,CACN,CACL,CAIA,SAASC,IAAa,CAGlB,OAAO,OAAO,UAAU,eAClB,CACE,MAAO,cACP,KAAM,cACN,IAAK,WACjB,EACU,OAAO,UAAU,iBACb,CACE,MAAO,gBACP,KAAM,gBACN,IAAK,aACrB,EACc,CACE,MAAO,uBACP,KAAM,sBACN,IAAK,kBACR,CACb,CAGA,SAASC,IAAqB,CAC1B,IAAIC,EAAkB,GAEtB,GAAI,CACA,IAAIC,EAAO,OAAO,eAAe,CAAA,EAAI,UAAW,CAC5C,IAAK,UAAY,CACbD,EAAkB,EACrB,CACb,CAAS,EAED,OAAO,iBAAiB,OAAQ,KAAMC,CAAI,CAClD,OACWlC,EAAG,CAAA,CAEV,OAAOiC,CACX,CACA,SAASE,IAA6B,CAClC,OAAO,OAAO,KAAO,IAAI,UAAY,IAAI,SAAS,eAAgB,MAAM,CAC5E,CAIA,SAASC,GAAcC,EAAIC,EAAI,CAC3B,MAAO,MAAOA,EAAKD,EACvB,CAEA,SAASE,GAAeC,EAAO1C,EAAO2C,EAAY,CAC9C,OAAQ3C,EAAQ,KAAQ0C,EAAMC,EAAa,CAAC,EAAID,EAAMC,CAAU,EACpE,CAEA,SAASC,GAAaF,EAAO1C,EAAO,CAChC,OAAOyC,GAAeC,EAAOA,EAAM,CAAC,EAAI,EAAI1C,EAAQ,KAAK,IAAI0C,EAAM,CAAC,CAAC,EAAI1C,EAAQ0C,EAAM,CAAC,EAAG,CAAC,CAChG,CAEA,SAASG,GAAaH,EAAO1C,EAAO,CAChC,OAAQA,GAAS0C,EAAM,CAAC,EAAIA,EAAM,CAAC,GAAM,IAAMA,EAAM,CAAC,CAC1D,CACA,SAASI,GAAK9C,EAAO+C,EAAK,CAEtB,QADIC,EAAI,EACDhD,GAAS+C,EAAIC,CAAC,GACjBA,GAAK,EAET,OAAOA,CACX,CAEA,SAASC,GAAWC,EAAMC,EAAMnD,EAAO,CACnC,GAAIA,GAASkD,EAAK,MAAM,EAAE,EAAE,CAAC,EACzB,MAAO,KAEX,IAAIF,EAAIF,GAAK9C,EAAOkD,CAAI,EACpBE,EAAKF,EAAKF,EAAI,CAAC,EACfK,EAAKH,EAAKF,CAAC,EACXT,EAAKY,EAAKH,EAAI,CAAC,EACfR,EAAKW,EAAKH,CAAC,EACf,OAAOT,EAAKK,GAAa,CAACQ,EAAIC,CAAE,EAAGrD,CAAK,EAAIsC,GAAcC,EAAIC,CAAE,CACpE,CAEA,SAASc,GAAaJ,EAAMC,EAAMnD,EAAO,CAErC,GAAIA,GAAS,IACT,OAAOkD,EAAK,MAAM,EAAE,EAAE,CAAC,EAE3B,IAAIF,EAAIF,GAAK9C,EAAOmD,CAAI,EACpBC,EAAKF,EAAKF,EAAI,CAAC,EACfK,EAAKH,EAAKF,CAAC,EACXT,EAAKY,EAAKH,EAAI,CAAC,EACfR,EAAKW,EAAKH,CAAC,EACf,OAAOH,GAAa,CAACO,EAAIC,CAAE,GAAIrD,EAAQuC,GAAMD,GAAcC,EAAIC,CAAE,CAAC,CACtE,CAEA,SAASe,GAAQJ,EAAMK,EAAQC,EAAMzD,EAAO,CACxC,GAAIA,IAAU,IACV,OAAOA,EAEX,IAAIgD,EAAIF,GAAK9C,EAAOmD,CAAI,EACpB9C,EAAI8C,EAAKH,EAAI,CAAC,EACdU,EAAIP,EAAKH,CAAC,EAEd,OAAIS,EAEIzD,EAAQK,GAAKqD,EAAIrD,GAAK,EACfqD,EAEJrD,EAENmD,EAAOR,EAAI,CAAC,EAGVG,EAAKH,EAAI,CAAC,EAAI1C,GAAQN,EAAQmD,EAAKH,EAAI,CAAC,EAAGQ,EAAOR,EAAI,CAAC,CAAC,EAFpDhD,CAGf,CAGA,IAAI2D,GAA0B,UAAY,CACtC,SAASA,EAAShE,EAAO8D,EAAMG,EAAY,CACvC,KAAK,KAAO,CAAE,EACd,KAAK,KAAO,CAAE,EACd,KAAK,OAAS,CAAE,EAChB,KAAK,UAAY,CAAE,EACnB,KAAK,qBAAuB,CAAE,EAC9B,KAAK,OAAS,CAACA,GAAc,EAAK,EAClC,KAAK,UAAY,CAAC,EAAK,EACvB,KAAK,KAAOH,EACZ,IAAII,EACAC,EAAU,CAAE,EAUhB,IARA,OAAO,KAAKnE,CAAK,EAAE,QAAQ,SAAUkE,EAAO,CACxCC,EAAQ,KAAK,CAACtC,GAAQ7B,EAAMkE,CAAK,CAAC,EAAGA,CAAK,CAAC,CACvD,CAAS,EAEDC,EAAQ,KAAK,SAAUzD,EAAGqD,EAAG,CACzB,OAAOrD,EAAE,CAAC,EAAE,CAAC,EAAIqD,EAAE,CAAC,EAAE,CAAC,CACnC,CAAS,EAEIG,EAAQ,EAAGA,EAAQC,EAAQ,OAAQD,IACpC,KAAK,iBAAiBC,EAAQD,CAAK,EAAE,CAAC,EAAGC,EAAQD,CAAK,EAAE,CAAC,CAAC,EAM9D,IAFA,KAAK,UAAY,KAAK,OAAO,MAAM,CAAC,EAE/BA,EAAQ,EAAGA,EAAQ,KAAK,UAAU,OAAQA,IAC3C,KAAK,gBAAgBA,EAAO,KAAK,UAAUA,CAAK,CAAC,CAE7D,CACI,OAAAF,EAAS,UAAU,YAAc,SAAU3D,EAAO,CAE9C,QADI+D,EAAY,CAAE,EACTF,EAAQ,EAAGA,EAAQ,KAAK,UAAU,OAAS,EAAGA,IACnDE,EAAUF,CAAK,EAAIpB,GAAe,KAAK,KAAMzC,EAAO6D,CAAK,EAE7D,OAAOE,CACV,EAGDJ,EAAS,UAAU,oBAAsB,SAAU3D,EAAO+D,EAAWC,EAAW,CAC5E,IAAIC,EAAa,EAEjB,GAAIjE,EAAQ,KAAK,KAAK,KAAK,KAAK,OAAS,CAAC,EACtC,KAAOA,EAAQ,KAAK,KAAKiE,EAAa,CAAC,GACnCA,SAGCjE,IAAU,KAAK,KAAK,KAAK,KAAK,OAAS,CAAC,IAC7CiE,EAAa,KAAK,KAAK,OAAS,GAGhC,CAACD,GAAahE,IAAU,KAAK,KAAKiE,EAAa,CAAC,GAChDA,IAEAF,IAAc,OACdA,EAAY,CAAE,GAElB,IAAIG,EACAC,EAAc,EACdC,EAAoBL,EAAUE,CAAU,EACxCI,EAAY,EACZC,EAAqB,EACrBC,EAAuB,EACvBC,EAAgB,EASpB,IAPIR,EACAE,GAAgBlE,EAAQ,KAAK,KAAKiE,CAAU,IAAM,KAAK,KAAKA,EAAa,CAAC,EAAI,KAAK,KAAKA,CAAU,GAGlGC,GAAgB,KAAK,KAAKD,EAAa,CAAC,EAAIjE,IAAU,KAAK,KAAKiE,EAAa,CAAC,EAAI,KAAK,KAAKA,CAAU,GAGnGG,EAAoB,GAEvBC,EAAY,KAAK,KAAKJ,EAAa,EAAIO,CAAa,EAAI,KAAK,KAAKP,EAAaO,CAAa,EAExFT,EAAUE,EAAaO,CAAa,EAAIL,EAAc,IAAMD,EAAe,IAAM,KAEjFI,EAAqBD,EAAYH,EAEjCC,GAAeC,EAAoB,IAAMF,GAAgBH,EAAUE,EAAaO,CAAa,EAE7FN,EAAe,IAIfI,EAAuBP,EAAUE,EAAaO,CAAa,EAAIH,EAAa,IAAOF,EAEnFA,EAAc,GAEdH,GACAO,EAAuBA,EAAuBD,EAE1C,KAAK,KAAK,OAASE,GAAiB,GACpCA,MAIJD,EAAuBA,EAAuBD,EAE1C,KAAK,KAAK,OAASE,GAAiB,GACpCA,KAIRJ,EAAoBL,EAAUE,EAAaO,CAAa,EAAIL,EAEhE,OAAOnE,EAAQuE,CAClB,EACDZ,EAAS,UAAU,WAAa,SAAU3D,EAAO,CAC7C,OAAAA,EAAQiD,GAAW,KAAK,KAAM,KAAK,KAAMjD,CAAK,EACvCA,CACV,EACD2D,EAAS,UAAU,aAAe,SAAU3D,EAAO,CAC/C,OAAOsD,GAAa,KAAK,KAAM,KAAK,KAAMtD,CAAK,CAClD,EACD2D,EAAS,UAAU,QAAU,SAAU3D,EAAO,CAC1C,OAAAA,EAAQuD,GAAQ,KAAK,KAAM,KAAK,OAAQ,KAAK,KAAMvD,CAAK,EACjDA,CACV,EACD2D,EAAS,UAAU,eAAiB,SAAU3D,EAAOyE,EAAQC,EAAM,CAC/D,IAAI1B,EAAIF,GAAK9C,EAAO,KAAK,IAAI,EAE7B,OAAIA,IAAU,KAAQyE,GAAUzE,IAAU,KAAK,KAAKgD,EAAI,CAAC,KACrDA,EAAI,KAAK,IAAIA,EAAI,EAAG,CAAC,IAEjB,KAAK,KAAKA,CAAC,EAAI,KAAK,KAAKA,EAAI,CAAC,GAAK0B,CAC9C,EACDf,EAAS,UAAU,eAAiB,SAAU3D,EAAO,CACjD,IAAIgD,EAAIF,GAAK9C,EAAO,KAAK,IAAI,EAC7B,MAAO,CACH,WAAY,CACR,WAAY,KAAK,KAAKgD,EAAI,CAAC,EAC3B,KAAM,KAAK,UAAUA,EAAI,CAAC,EAC1B,YAAa,KAAK,qBAAqBA,EAAI,CAAC,CAC/C,EACD,SAAU,CACN,WAAY,KAAK,KAAKA,EAAI,CAAC,EAC3B,KAAM,KAAK,UAAUA,EAAI,CAAC,EAC1B,YAAa,KAAK,qBAAqBA,EAAI,CAAC,CAC/C,EACD,UAAW,CACP,WAAY,KAAK,KAAKA,CAAC,EACvB,KAAM,KAAK,UAAUA,CAAC,EACtB,YAAa,KAAK,qBAAqBA,CAAC,CAC3C,CACJ,CACJ,EACDW,EAAS,UAAU,kBAAoB,UAAY,CAC/C,IAAIgB,EAAe,KAAK,UAAU,IAAIlD,EAAa,EACnD,OAAO,KAAK,IAAI,MAAM,KAAMkD,CAAY,CAC3C,EACDhB,EAAS,UAAU,UAAY,UAAY,CACvC,OAAO,KAAK,KAAK,CAAC,IAAM,KAAK,KAAK,KAAK,KAAK,OAAS,CAAC,CACzD,EAEDA,EAAS,UAAU,QAAU,SAAU3D,EAAO,CAC1C,OAAO,KAAK,QAAQ,KAAK,WAAWA,CAAK,CAAC,CAC7C,EACD2D,EAAS,UAAU,iBAAmB,SAAUE,EAAO7D,EAAO,CAC1D,IAAI4E,EAYJ,GAVIf,IAAU,MACVe,EAAa,EAERf,IAAU,MACfe,EAAa,IAGbA,EAAa,WAAWf,CAAK,EAG7B,CAAC7C,EAAU4D,CAAU,GAAK,CAAC5D,EAAUhB,EAAM,CAAC,CAAC,EAC7C,MAAM,IAAI,MAAM,0CAA0C,EAG9D,KAAK,KAAK,KAAK4E,CAAU,EACzB,KAAK,KAAK,KAAK5E,EAAM,CAAC,CAAC,EACvB,IAAI6E,EAAS,OAAO7E,EAAM,CAAC,CAAC,EAIvB4E,EAMD,KAAK,OAAO,KAAK,MAAMC,CAAM,EAAI,GAAQA,CAAM,EAL1C,MAAMA,CAAM,IACb,KAAK,OAAO,CAAC,EAAIA,GAMzB,KAAK,qBAAqB,KAAK,CAAC,CACnC,EACDlB,EAAS,UAAU,gBAAkB,SAAUmB,EAAGC,EAAG,CAEjD,GAAKA,EAIL,IAAI,KAAK,KAAKD,CAAC,IAAM,KAAK,KAAKA,EAAI,CAAC,EAAG,CACnC,KAAK,OAAOA,CAAC,EAAI,KAAK,qBAAqBA,CAAC,EAAI,KAAK,KAAKA,CAAC,EAC3D,MACZ,CAEQ,KAAK,OAAOA,CAAC,EACTrC,GAAe,CAAC,KAAK,KAAKqC,CAAC,EAAG,KAAK,KAAKA,EAAI,CAAC,CAAC,EAAGC,EAAG,CAAC,EAAIzC,GAAc,KAAK,KAAKwC,CAAC,EAAG,KAAK,KAAKA,EAAI,CAAC,CAAC,EACzG,IAAIE,GAAc,KAAK,KAAKF,EAAI,CAAC,EAAI,KAAK,KAAKA,CAAC,GAAK,KAAK,UAAUA,CAAC,EACjEG,EAAc,KAAK,KAAK,OAAOD,EAAW,QAAQ,CAAC,CAAC,EAAI,CAAC,EACzDE,EAAO,KAAK,KAAKJ,CAAC,EAAI,KAAK,UAAUA,CAAC,EAAIG,EAC9C,KAAK,qBAAqBH,CAAC,EAAII,EAClC,EACMvB,CACX,IAgBIwB,GAAmB,CACnB,GAAI,SAAUnF,EAAO,CACjB,OAAOA,IAAU,OAAY,GAAKA,EAAM,QAAQ,CAAC,CACpD,EACD,KAAM,MACV,EACIoF,GAAa,CACb,OAAQ,SACR,KAAM,OACN,OAAQ,SACR,OAAQ,SACR,YAAa,eACb,YAAa,eACb,UAAW,aACX,WAAY,aACZ,SAAU,WACV,WAAY,aACZ,QAAS,UACT,SAAU,WACV,IAAK,MACL,IAAK,MACL,iBAAkB,cAClB,iBAAkB,cAClB,UAAW,YACX,KAAM,aACN,IAAK,YACL,OAAQ,SACR,QAAS,UACT,KAAM,OACN,eAAgB,kBAChB,aAAc,gBACd,OAAQ,SACR,iBAAkB,oBAClB,eAAgB,kBAChB,aAAc,gBACd,YAAa,eACb,UAAW,aACX,MAAO,QACP,gBAAiB,mBACjB,cAAe,iBACf,YAAa,eACb,WAAY,cACZ,SAAU,WACd,EAEIC,EAAoB,CACpB,SAAU,cACV,KAAM,SACV,EAEA,SAASC,GAASC,EAAQ5F,EAAO,CAC7B,GAAI,CAACqB,EAAUrB,CAAK,EAChB,MAAM,IAAI,MAAM,oCAAoC,EAIxD4F,EAAO,WAAa5F,CACxB,CACA,SAAS6F,GAA2BD,EAAQ5F,EAAO,CAC/C,GAAI,CAACqB,EAAUrB,CAAK,EAChB,MAAM,IAAI,MAAM,sDAAsD,EAE1E4F,EAAO,uBAAyB5F,CACpC,CACA,SAAS8F,GAAuBF,EAAQ5F,EAAO,CAC3C,GAAI,CAACqB,EAAUrB,CAAK,EAChB,MAAM,IAAI,MAAM,kDAAkD,EAEtE4F,EAAO,mBAAqB5F,CAChC,CACA,SAAS+F,GAAwBH,EAAQ5F,EAAO,CAC5C,GAAI,CAACqB,EAAUrB,CAAK,EAChB,MAAM,IAAI,MAAM,mDAAmD,EAEvE4F,EAAO,oBAAsB5F,CACjC,CACA,SAASgG,GAAUJ,EAAQ5F,EAAO,CAE9B,GAAI,OAAOA,GAAU,UAAY,MAAM,QAAQA,CAAK,EAChD,MAAM,IAAI,MAAM,uCAAuC,EAG3D,GAAIA,EAAM,MAAQ,QAAaA,EAAM,MAAQ,OACzC,MAAM,IAAI,MAAM,gDAAgD,EAEpE4F,EAAO,SAAW,IAAI5B,GAAShE,EAAO4F,EAAO,MAAQ,GAAOA,EAAO,UAAU,CACjF,CACA,SAASK,GAAUL,EAAQ5F,EAAO,CAI9B,GAHAA,EAAQ6B,GAAQ7B,CAAK,EAGjB,CAAC,MAAM,QAAQA,CAAK,GAAK,CAACA,EAAM,OAChC,MAAM,IAAI,MAAM,0CAA0C,EAG9D4F,EAAO,QAAU5F,EAAM,OAGvB4F,EAAO,MAAQ5F,CACnB,CACA,SAASkG,GAASN,EAAQ5F,EAAO,CAC7B,GAAI,OAAOA,GAAU,UACjB,MAAM,IAAI,MAAM,8CAA8C,EAGlE4F,EAAO,KAAO5F,CAClB,CACA,SAASmG,GAAYP,EAAQ5F,EAAO,CAChC,GAAI,OAAOA,GAAU,UACjB,MAAM,IAAI,MAAM,iDAAiD,EAGrE4F,EAAO,QAAU5F,CACrB,CACA,SAASoG,GAAsBR,EAAQ5F,EAAO,CAC1C,GAAI,OAAOA,GAAU,SACjB,MAAM,IAAI,MAAM,0DAA0D,EAE9E4F,EAAO,kBAAoB5F,CAC/B,CACA,SAASqG,GAAYT,EAAQ5F,EAAO,CAChC,IAAIsG,EAAU,CAAC,EAAK,EAChBnB,EASJ,GAPInF,IAAU,QACVA,EAAQ,CAAC,GAAM,EAAK,EAEfA,IAAU,UACfA,EAAQ,CAAC,GAAO,EAAI,GAGpBA,IAAU,IAAQA,IAAU,GAAO,CACnC,IAAKmF,EAAI,EAAGA,EAAIS,EAAO,QAAST,IAC5BmB,EAAQ,KAAKtG,CAAK,EAEtBsG,EAAQ,KAAK,EAAK,CAC1B,KAES,IAAI,CAAC,MAAM,QAAQtG,CAAK,GAAK,CAACA,EAAM,QAAUA,EAAM,SAAW4F,EAAO,QAAU,EACjF,MAAM,IAAI,MAAM,0DAA0D,EAG1EU,EAAUtG,EAEd4F,EAAO,QAAUU,CACrB,CACA,SAASC,GAAgBX,EAAQ5F,EAAO,CAGpC,OAAQA,EAAK,CACT,IAAK,aACD4F,EAAO,IAAM,EACb,MACJ,IAAK,WACDA,EAAO,IAAM,EACb,MACJ,QACI,MAAM,IAAI,MAAM,8CAA8C,CAC1E,CACA,CACA,SAASY,GAAWZ,EAAQ5F,EAAO,CAC/B,GAAI,CAACqB,EAAUrB,CAAK,EAChB,MAAM,IAAI,MAAM,8CAA8C,EAG9DA,IAAU,IAGd4F,EAAO,OAASA,EAAO,SAAS,YAAY5F,CAAK,EACrD,CACA,SAASyG,GAAUb,EAAQ5F,EAAO,CAC9B,GAAI,CAACqB,EAAUrB,CAAK,EAChB,MAAM,IAAI,MAAM,6CAA6C,EAGjE,GADA4F,EAAO,MAAQA,EAAO,SAAS,YAAY5F,CAAK,EAC5C,CAAC4F,EAAO,OAASA,EAAO,QAAU,EAClC,MAAM,IAAI,MAAM,wFAAwF,CAEhH,CACA,SAASc,GAAYd,EAAQ5F,EAAO,CAChC,IAAIkE,EACJ,GAAI,CAAC7C,EAAUrB,CAAK,GAAK,CAAC,MAAM,QAAQA,CAAK,EACzC,MAAM,IAAI,MAAM,6EAA6E,EAEjG,GAAI,MAAM,QAAQA,CAAK,GAAK,EAAEA,EAAM,SAAW,GAAKqB,EAAUrB,EAAM,CAAC,CAAC,GAAKqB,EAAUrB,EAAM,CAAC,CAAC,GACzF,MAAM,IAAI,MAAM,6EAA6E,EAEjG,GAAIA,IAAU,EAQd,KALK,MAAM,QAAQA,CAAK,IACpBA,EAAQ,CAACA,EAAOA,CAAK,GAGzB4F,EAAO,QAAU,CAACA,EAAO,SAAS,YAAY5F,EAAM,CAAC,CAAC,EAAG4F,EAAO,SAAS,YAAY5F,EAAM,CAAC,CAAC,CAAC,EACzFkE,EAAQ,EAAGA,EAAQ0B,EAAO,SAAS,UAAU,OAAS,EAAG1B,IAE1D,GAAI0B,EAAO,QAAQ,CAAC,EAAE1B,CAAK,EAAI,GAAK0B,EAAO,QAAQ,CAAC,EAAE1B,CAAK,EAAI,EAC3D,MAAM,IAAI,MAAM,4DAA4D,EAGpF,IAAIyC,EAAe3G,EAAM,CAAC,EAAIA,EAAM,CAAC,EACjC4G,EAAahB,EAAO,SAAS,KAAK,CAAC,EACnCiB,EAAYjB,EAAO,SAAS,KAAKA,EAAO,SAAS,KAAK,OAAS,CAAC,EACpE,GAAIe,GAAgBE,EAAYD,GAAc,EAC1C,MAAM,IAAI,MAAM,iEAAiE,EAEzF,CACA,SAASE,GAAclB,EAAQ5F,EAAO,CAIlC,OAAQA,EAAK,CACT,IAAK,MACD4F,EAAO,IAAM,EACb,MACJ,IAAK,MACDA,EAAO,IAAM,EACb,MACJ,QACI,MAAM,IAAI,MAAM,oDAAoD,CAChF,CACA,CACA,SAASmB,GAAcnB,EAAQ5F,EAAO,CAElC,GAAI,OAAOA,GAAU,SACjB,MAAM,IAAI,MAAM,8DAA8D,EAIlF,IAAIgH,EAAMhH,EAAM,QAAQ,KAAK,GAAK,EAC9BiH,EAAOjH,EAAM,QAAQ,MAAM,GAAK,EAChCkH,EAAQlH,EAAM,QAAQ,OAAO,GAAK,EAClC8D,EAAO9D,EAAM,QAAQ,MAAM,GAAK,EAChCmH,EAAQnH,EAAM,QAAQ,OAAO,GAAK,EAClCoH,EAAgBpH,EAAM,QAAQ,eAAe,GAAK,EAClDqH,EAAiBrH,EAAM,QAAQ,iBAAiB,GAAK,EACrDsH,EAAUtH,EAAM,QAAQ,UAAU,GAAK,EACvCuH,EAAcvH,EAAM,QAAQ,cAAc,GAAK,EACnD,GAAIkH,EAAO,CACP,GAAItB,EAAO,UAAY,EACnB,MAAM,IAAI,MAAM,2DAA2D,EAG/EY,GAAWZ,EAAQA,EAAO,MAAM,CAAC,EAAIA,EAAO,MAAM,CAAC,CAAC,CAC5D,CACI,GAAIyB,GAAkBzB,EAAO,UAAY,EACrC,MAAM,IAAI,MAAM,qEAAqE,EAEzF,GAAIwB,IAAkBxB,EAAO,QAAUA,EAAO,OAC1C,MAAM,IAAI,MAAM,2EAA2E,EAE/FA,EAAO,OAAS,CACZ,IAAKoB,GAAOlD,EACZ,KAAMmD,EACN,QAASK,EACT,YAAaC,EACb,MAAOL,EACP,KAAMpD,EACN,MAAOqD,EACP,cAAeC,EACf,eAAgBC,CACnB,CACL,CACA,SAASG,GAAa5B,EAAQ5F,EAAO,CACjC,GAAIA,IAAU,GAGd,GAAIA,IAAU,IAAQC,GAAwBD,CAAK,EAAG,CAClD4F,EAAO,SAAW,CAAE,EACpB,QAAST,EAAI,EAAGA,EAAIS,EAAO,QAAST,IAChCS,EAAO,SAAS,KAAK5F,CAAK,CAEtC,KACS,CAED,GADAA,EAAQ6B,GAAQ7B,CAAK,EACjBA,EAAM,SAAW4F,EAAO,QACxB,MAAM,IAAI,MAAM,oDAAoD,EAExE5F,EAAM,QAAQ,SAAUyH,EAAW,CAC/B,GAAI,OAAOA,GAAc,WAAa,CAACxH,GAAwBwH,CAAS,EACpE,MAAM,IAAI,MAAM,+DAA+D,CAE/F,CAAS,EACD7B,EAAO,SAAW5F,CAC1B,CACA,CACA,SAAS0H,GAAqB9B,EAAQ5F,EAAO,CACzC,GAAIA,EAAM,SAAW4F,EAAO,QACxB,MAAM,IAAI,MAAM,qDAAqD,EAEzEA,EAAO,iBAAmB5F,CAC9B,CACA,SAAS2H,GAAe/B,EAAQ5F,EAAO,CACnC,GAAI,CAACC,GAAwBD,CAAK,EAC9B,MAAM,IAAI,MAAM,gDAAgD,EAEpE4F,EAAO,WAAa5F,CACxB,CACA,SAAS4H,GAAWhC,EAAQ5F,EAAO,CAC/B,GAAI,CAACD,GAAiBC,CAAK,EACvB,MAAM,IAAI,MAAM,wDAAwD,EAE5E4F,EAAO,OAAS5F,CACpB,CACA,SAAS6H,GAAoBjC,EAAQ5F,EAAO,CACxC,GAAI,OAAOA,GAAU,UACjB,MAAM,IAAI,MAAM,yDAAyD,EAE7E4F,EAAO,gBAAkB5F,CAC7B,CACA,SAAS8H,GAAoBlC,EAAQ5F,EAAO,CAExC4F,EAAO,gBAAkB5F,CAC7B,CACA,SAAS+H,GAAcnC,EAAQ5F,EAAO,CAClC,GAAI,OAAOA,GAAU,UAAYA,IAAU,GACvC,MAAM,IAAI,MAAM,sDAAsD,EAE1E4F,EAAO,UAAY5F,CACvB,CACA,SAASgI,GAAepC,EAAQ5F,EAAO,CACnC,GAAI,OAAOA,GAAU,SACjB,MAAM,IAAI,MAAM,6CAA6C,EAE7D,OAAO4F,EAAO,WAAc,UAC5BA,EAAO,WAAa,CAAE,EACtB,OAAO,KAAK5F,CAAK,EAAE,QAAQ,SAAUiI,EAAK,CACtCrC,EAAO,WAAWqC,CAAG,EAAIrC,EAAO,UAAY5F,EAAMiI,CAAG,CACjE,CAAS,GAGDrC,EAAO,WAAa5F,CAE5B,CAEA,SAASkI,GAAYC,EAAS,CAI1B,IAAIvC,EAAS,CACT,OAAQ,KACR,MAAO,KACP,QAAS,KACT,QAAS,GACT,kBAAmB,IACnB,WAAYJ,GACZ,OAAQA,EACX,EAEG4C,EAAQ,CACR,KAAM,CAAE,EAAG,GAAO,EAAGzC,EAAU,EAC/B,uBAAwB,CAAE,EAAG,GAAO,EAAGE,EAA4B,EACnE,mBAAoB,CAAE,EAAG,GAAO,EAAGC,EAAwB,EAC3D,oBAAqB,CAAE,EAAG,GAAO,EAAGC,EAAyB,EAC7D,MAAO,CAAE,EAAG,GAAM,EAAGE,EAAW,EAChC,QAAS,CAAE,EAAG,GAAM,EAAGI,EAAa,EACpC,UAAW,CAAE,EAAG,GAAM,EAAGS,EAAe,EACxC,KAAM,CAAE,EAAG,GAAO,EAAGZ,EAAU,EAC/B,QAAS,CAAE,EAAG,GAAO,EAAGC,EAAa,EACrC,kBAAmB,CAAE,EAAG,GAAO,EAAGC,EAAuB,EACzD,MAAO,CAAE,EAAG,GAAM,EAAGJ,EAAW,EAChC,YAAa,CAAE,EAAG,GAAO,EAAGO,EAAiB,EAC7C,OAAQ,CAAE,EAAG,GAAO,EAAGC,EAAY,EACnC,MAAO,CAAE,EAAG,GAAO,EAAGC,EAAW,EACjC,QAAS,CAAE,EAAG,GAAO,EAAGC,EAAa,EACrC,UAAW,CAAE,EAAG,GAAM,EAAGK,EAAe,EACxC,WAAY,CAAE,EAAG,GAAO,EAAGY,EAAgB,EAC3C,OAAQ,CAAE,EAAG,GAAO,EAAGC,EAAY,EACnC,SAAU,CAAE,EAAG,GAAO,EAAGJ,EAAc,EACvC,gBAAiB,CAAE,EAAG,GAAM,EAAGK,EAAqB,EACpD,gBAAiB,CAAE,EAAG,GAAO,EAAGC,EAAqB,EACrD,UAAW,CAAE,EAAG,GAAM,EAAGC,EAAe,EACxC,WAAY,CAAE,EAAG,GAAM,EAAGC,EAAgB,EAC1C,iBAAkB,CAAE,EAAG,GAAO,EAAGN,EAAsB,CAC1D,EACGW,EAAW,CACX,QAAS,GACT,UAAW,MACX,UAAW,MACX,YAAa,aACb,gBAAiB,GACjB,UAAW,QACX,WAAY5C,GACZ,uBAAwB,EACxB,mBAAoB,EACpB,oBAAqB,EACxB,EAEG0C,EAAQ,QAAU,CAACA,EAAQ,aAC3BA,EAAQ,WAAaA,EAAQ,QAKjC,OAAO,KAAKC,CAAK,EAAE,QAAQ,SAAUE,EAAM,CAEvC,GAAI,CAAClI,GAAM+H,EAAQG,CAAI,CAAC,GAAKD,EAASC,CAAI,IAAM,OAAW,CACvD,GAAIF,EAAME,CAAI,EAAE,EACZ,MAAM,IAAI,MAAM,gBAAkBA,EAAO,gBAAgB,EAE7D,MACZ,CACQF,EAAME,CAAI,EAAE,EAAE1C,EAASxF,GAAM+H,EAAQG,CAAI,CAAC,EAAqBH,EAAQG,CAAI,EAA7BD,EAASC,CAAI,CAAiB,CACpF,CAAK,EAED1C,EAAO,KAAOuC,EAAQ,KAKtB,IAAII,EAAI,SAAS,cAAc,KAAK,EAChCC,EAAWD,EAAE,MAAM,cAAgB,OACnCE,EAAWF,EAAE,MAAM,YAAc,OACrC3C,EAAO,cAAgB6C,EAAW,YAAcD,EAAW,cAAgB,kBAE3E,IAAIE,EAAS,CACT,CAAC,OAAQ,KAAK,EACd,CAAC,QAAS,QAAQ,CACrB,EACD,OAAA9C,EAAO,MAAQ8C,EAAO9C,EAAO,GAAG,EAAEA,EAAO,GAAG,EACrCA,CACX,CAEA,SAAS+C,GAAMC,EAAQT,EAASU,EAAiB,CAC7C,IAAIC,EAAUxG,GAAY,EACtByG,EAA0BrG,GAA4B,EACtDF,EAAkBuG,GAA2BxG,GAAoB,EAGjEyG,EAAeJ,EACfK,EACAC,EACAC,EACAC,EACAC,EACAC,EAEAC,EAAiBpB,EAAQ,SACzBqB,EAAe,CAAE,EACjBC,EAAkB,CAAE,EACpBC,EAAsB,CAAE,EACxBC,EAA2B,EAC3BC,EAAe,CAAE,EACjBC,EAAyB,GAEzBC,EAAiBlB,EAAO,cACxBmB,EAAwB5B,EAAQ,iBAAmB2B,EAAe,gBAClEE,EAAaF,EAAe,KAG5BG,GAAkBH,EAAe,MAAQ,OAAS3B,EAAQ,MAAQ,EAAI,EAAI,IAE9E,SAAS+B,EAAUC,EAAW3I,EAAW,CACrC,IAAI4I,EAAMN,EAAe,cAAc,KAAK,EAC5C,OAAItI,GACAE,EAAS0I,EAAK5I,CAAS,EAE3B2I,EAAU,YAAYC,CAAG,EAClBA,CACf,CAEI,SAASC,EAAUC,EAAMC,EAAc,CACnC,IAAIC,EAASN,EAAUI,EAAMnC,EAAQ,WAAW,MAAM,EAClDsC,EAASP,EAAUM,EAAQrC,EAAQ,WAAW,MAAM,EAWxD,GAVA+B,EAAUO,EAAQtC,EAAQ,WAAW,SAAS,EAC9CsC,EAAO,aAAa,cAAe,OAAOF,CAAY,CAAC,EACnDpC,EAAQ,kBAGRsC,EAAO,aAAa,WAAY,GAAG,EACnCA,EAAO,iBAAiB,UAAW,SAAUC,EAAO,CAChD,OAAOC,GAAaD,EAAOH,CAAY,CACvD,CAAa,GAEDpC,EAAQ,mBAAqB,OAAW,CACxC,IAAIyC,EAAezC,EAAQ,iBAAiBoC,CAAY,EACxD,OAAO,KAAKK,CAAY,EAAE,QAAQ,SAAUC,EAAW,CACnDJ,EAAO,aAAaI,EAAWD,EAAaC,CAAS,CAAC,CACtE,CAAa,CACb,CACQ,OAAAJ,EAAO,aAAa,OAAQ,QAAQ,EACpCA,EAAO,aAAa,mBAAoBtC,EAAQ,IAAM,WAAa,YAAY,EAC3EoC,IAAiB,EACjB7I,EAAS+I,EAAQtC,EAAQ,WAAW,WAAW,EAE1CoC,IAAiBpC,EAAQ,QAAU,GACxCzG,EAAS+I,EAAQtC,EAAQ,WAAW,WAAW,EAEnDqC,EAAO,OAASC,EACTD,CACf,CAEI,SAASM,EAAWR,EAAMS,EAAK,CAC3B,OAAKA,EAGEb,EAAUI,EAAMnC,EAAQ,WAAW,OAAO,EAFtC,EAGnB,CAEI,SAAS6C,EAAYC,EAAgBX,EAAM,CACvCpB,EAAoBgB,EAAUI,EAAMnC,EAAQ,WAAW,QAAQ,EAC/DgB,EAAgB,CAAE,EAClBC,EAAiB,CAAE,EACnBA,EAAe,KAAK0B,EAAW5B,EAAmB+B,EAAe,CAAC,CAAC,CAAC,EAGpE,QAAS9F,EAAI,EAAGA,EAAIgD,EAAQ,QAAShD,IAEjCgE,EAAc,KAAKkB,EAAUC,EAAMnF,CAAC,CAAC,EACrCuE,EAAoBvE,CAAC,EAAIA,EACzBiE,EAAe,KAAK0B,EAAW5B,EAAmB+B,EAAe9F,EAAI,CAAC,CAAC,CAAC,CAEpF,CAEI,SAAS+F,GAAUf,EAAW,CAE1BzI,EAASyI,EAAWhC,EAAQ,WAAW,MAAM,EACzCA,EAAQ,MAAQ,EAChBzG,EAASyI,EAAWhC,EAAQ,WAAW,GAAG,EAG1CzG,EAASyI,EAAWhC,EAAQ,WAAW,GAAG,EAE1CA,EAAQ,MAAQ,EAChBzG,EAASyI,EAAWhC,EAAQ,WAAW,UAAU,EAGjDzG,EAASyI,EAAWhC,EAAQ,WAAW,QAAQ,EAEnD,IAAIgD,EAAgB,iBAAiBhB,CAAS,EAAE,UAChD,OAAIgB,IAAkB,MAClBzJ,EAASyI,EAAWhC,EAAQ,WAAW,gBAAgB,EAGvDzG,EAASyI,EAAWhC,EAAQ,WAAW,gBAAgB,EAEpD+B,EAAUC,EAAWhC,EAAQ,WAAW,IAAI,CAC3D,CACI,SAASiD,EAAWX,EAAQF,EAAc,CACtC,MAAI,CAACpC,EAAQ,UAAY,CAACA,EAAQ,SAASoC,CAAY,EAC5C,GAEJL,EAAUO,EAAO,WAAYtC,EAAQ,WAAW,OAAO,CACtE,CACI,SAASkD,IAAmB,CACxB,OAAOrC,EAAa,aAAa,UAAU,CACnD,CAEI,SAASsC,EAAiBf,EAAc,CACpC,IAAIgB,EAAepC,EAAcoB,CAAY,EAC7C,OAAOgB,EAAa,aAAa,UAAU,CACnD,CACI,SAASC,GAAQjB,EAAc,CACvBA,GAAiB,MACjBpB,EAAcoB,CAAY,EAAE,aAAa,WAAY,EAAE,EACvDpB,EAAcoB,CAAY,EAAE,OAAO,gBAAgB,UAAU,IAG7DvB,EAAa,aAAa,WAAY,EAAE,EACxCG,EAAc,QAAQ,SAAUsB,EAAQ,CACpCA,EAAO,OAAO,gBAAgB,UAAU,CACxD,CAAa,EAEb,CACI,SAASgB,GAAOlB,EAAc,CACtBA,GAAiB,MACjBpB,EAAcoB,CAAY,EAAE,gBAAgB,UAAU,EACtDpB,EAAcoB,CAAY,EAAE,OAAO,aAAa,WAAY,GAAG,IAG/DvB,EAAa,gBAAgB,UAAU,EACvCG,EAAc,QAAQ,SAAUsB,EAAQ,CACpCA,EAAO,gBAAgB,UAAU,EACjCA,EAAO,OAAO,aAAa,WAAY,GAAG,CAC1D,CAAa,EAEb,CACI,SAASiB,IAAiB,CAClBpC,IACAqC,GAAY,SAAWjG,EAAkB,QAAQ,EACjD4D,EAAe,QAAQ,SAAUsC,EAAS,CAClCA,GACA1L,GAAc0L,CAAO,CAEzC,CAAa,EACDtC,EAAiB,KAE7B,CAEI,SAASuC,IAAW,CAChBH,GAAgB,EAEhBpC,EAAiBH,EAAc,IAAIiC,CAAU,EAC7CU,GAAU,SAAWpG,EAAkB,SAAU,SAAUqG,EAAQxB,EAAcyB,EAAW,CACxF,GAAI,GAAC1C,GAAkB,CAACnB,EAAQ,WAG5BmB,EAAeiB,CAAY,IAAM,GAGrC,KAAI0B,EAAiBF,EAAOxB,CAAY,EACpCpC,EAAQ,SAASoC,CAAY,IAAM,KACnC0B,EAAiB9D,EAAQ,SAASoC,CAAY,EAAE,GAAGyB,EAAUzB,CAAY,CAAC,GAE9EjB,EAAeiB,CAAY,EAAE,UAAY0B,EACrD,CAAS,CACT,CACI,SAASC,IAAO,CACZP,GAAY,SAAWjG,EAAkB,IAAI,EAC7CoG,GAAU,SAAWpG,EAAkB,KAAM,SAAUqG,EAAQxB,EAAcyB,EAAWhF,EAAKmF,EAAW,CAEpGzC,EAAoB,QAAQ,SAAUxF,EAAO,CACzC,IAAIuG,EAAStB,EAAcjF,CAAK,EAC5BkI,EAAMC,GAAoB5C,EAAiBvF,EAAO,EAAG,GAAM,GAAM,EAAI,EACrEoI,EAAMD,GAAoB5C,EAAiBvF,EAAO,IAAK,GAAM,GAAM,EAAI,EACvEqI,EAAMJ,EAAUjI,CAAK,EAErBsI,EAAO,OAAOrE,EAAQ,WAAW,GAAG6D,EAAU9H,CAAK,CAAC,CAAC,EAEzDkI,EAAM7C,EAAe,aAAa6C,CAAG,EAAE,QAAQ,CAAC,EAChDE,EAAM/C,EAAe,aAAa+C,CAAG,EAAE,QAAQ,CAAC,EAChDC,EAAMhD,EAAe,aAAagD,CAAG,EAAE,QAAQ,CAAC,EAChD9B,EAAO,SAAS,CAAC,EAAE,aAAa,gBAAiB2B,CAAG,EACpD3B,EAAO,SAAS,CAAC,EAAE,aAAa,gBAAiB6B,CAAG,EACpD7B,EAAO,SAAS,CAAC,EAAE,aAAa,gBAAiB8B,CAAG,EACpD9B,EAAO,SAAS,CAAC,EAAE,aAAa,iBAAkB+B,CAAI,CACtE,CAAa,CACb,CAAS,CACT,CACI,SAASC,GAASC,EAAM,CAEpB,GAAIA,EAAK,OAAS7M,GAAS,OAAS6M,EAAK,OAAS7M,GAAS,MACvD,OAAO0J,EAAe,KAE1B,GAAImD,EAAK,OAAS7M,GAAS,MAAO,CAC9B,GAAI6M,EAAK,OAAS,EACd,MAAM,IAAI,MAAM,wDAAwD,EAO5E,QAJIC,EAAWD,EAAK,OAAS,EACzBE,EAAS,IAAMD,EACfZ,EAAS,CAAE,EAERY,KACHZ,EAAOY,CAAQ,EAAIA,EAAWC,EAElC,OAAAb,EAAO,KAAK,GAAG,EACRc,GAAWd,EAAQW,EAAK,OAAO,CAClD,CACQ,OAAIA,EAAK,OAAS7M,GAAS,UAEhBgN,GAAWH,EAAK,OAAQA,EAAK,OAAO,EAE3CA,EAAK,OAAS7M,GAAS,OAEnB6M,EAAK,QACEA,EAAK,OAAO,IAAI,SAAUrM,EAAO,CAEpC,OAAOkJ,EAAe,aAAaA,EAAe,QAAQA,EAAe,WAAWlJ,CAAK,CAAC,CAAC,CAC/G,CAAiB,EAGEqM,EAAK,OAET,EACf,CACI,SAASG,GAAWd,EAAQe,EAAS,CACjC,OAAOf,EAAO,IAAI,SAAU1L,EAAO,CAC/B,OAAOkJ,EAAe,aAAauD,EAAUvD,EAAe,QAAQlJ,CAAK,EAAIA,CAAK,CAC9F,CAAS,CACT,CACI,SAAS0M,GAAeL,EAAM,CAC1B,SAASM,EAAc3M,EAAO4M,EAAW,CAErC,OAAO,QAAQ5M,EAAQ4M,GAAW,QAAQ,CAAC,CAAC,CACxD,CACQ,IAAIC,EAAQT,GAASC,CAAI,EACrBS,EAAU,CAAE,EACZC,EAAe7D,EAAe,KAAK,CAAC,EACpC8D,EAAc9D,EAAe,KAAKA,EAAe,KAAK,OAAS,CAAC,EAChE+D,EAAc,GACdC,EAAa,GACbC,EAAU,EAEd,OAAAN,EAAQ1M,GAAO0M,EAAM,MAAO,EAAC,KAAK,SAAUxM,EAAGqD,EAAG,CAC9C,OAAOrD,EAAIqD,CACvB,CAAS,CAAC,EAEEmJ,EAAM,CAAC,IAAME,IACbF,EAAM,QAAQE,CAAY,EAC1BE,EAAc,IAGdJ,EAAMA,EAAM,OAAS,CAAC,IAAMG,IAC5BH,EAAM,KAAKG,CAAW,EACtBE,EAAa,IAEjBL,EAAM,QAAQ,SAAUO,EAASvJ,EAAO,CAEpC,IAAIqB,EACAJ,EACAuI,EACAC,EAAMF,EACNG,EAAOV,EAAMhJ,EAAQ,CAAC,EACtB2J,EACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GAAU1B,EAAK,OAAS7M,GAAS,MAiBrC,IAdIuO,KACA7I,EAAOgE,EAAe,UAAUrF,CAAK,GAGpCqB,IACDA,EAAOqI,EAAOD,GAGdC,IAAS,SACTA,EAAOD,GAGXpI,EAAO,KAAK,IAAIA,EAAM,IAAS,EAE1BJ,EAAIwI,EAAKxI,GAAKyI,EAAMzI,EAAI6H,EAAc7H,EAAGI,CAAI,EAAG,CAcjD,IAXAsI,EAAStE,EAAe,WAAWpE,CAAC,EACpC2I,GAAgBD,EAASL,EACzBS,GAAQH,IAAiBpB,EAAK,SAAW,GACzCwB,GAAY,KAAK,MAAMD,EAAK,EAK5BE,GAAWL,GAAgBI,GAGtBR,EAAI,EAAGA,GAAKQ,GAAWR,GAAK,EAK7BK,GAASP,EAAUE,EAAIS,GACvBhB,EAAQY,GAAO,QAAQ,CAAC,CAAC,EAAI,CAACxE,EAAe,aAAawE,EAAM,EAAG,CAAC,EAGxEC,GAAOd,EAAM,QAAQ/H,CAAC,EAAI,GAAKrF,EAAS,WAAasO,GAAUtO,EAAS,WAAaA,EAAS,QAE1F,CAACoE,GAASoJ,GAAenI,IAAMyI,IAC/BI,GAAO,GAEL7I,IAAMyI,GAAQL,IAEhBJ,EAAQU,EAAO,QAAQ,CAAC,CAAC,EAAI,CAAC1I,EAAG6I,EAAI,GAGzCR,EAAUK,CAC1B,CACA,CAAS,EACMV,CACf,CACI,SAASkB,GAAWzB,EAAQ0B,EAAY7G,EAAW,CAC/C,IAAI8G,EAAIC,EACJjN,EAAUuI,EAAe,cAAc,KAAK,EAC5C2E,GAAoBF,EAAK,CAAE,EAC3BA,EAAGzO,EAAS,IAAI,EAAI,GACpByO,EAAGzO,EAAS,OAAO,EAAIqI,EAAQ,WAAW,YAC1CoG,EAAGzO,EAAS,UAAU,EAAIqI,EAAQ,WAAW,WAC7CoG,EAAGzO,EAAS,UAAU,EAAIqI,EAAQ,WAAW,SAC7CoG,GACAG,GAAqBF,EAAK,CAAE,EAC5BA,EAAG1O,EAAS,IAAI,EAAI,GACpB0O,EAAG1O,EAAS,OAAO,EAAIqI,EAAQ,WAAW,aAC1CqG,EAAG1O,EAAS,UAAU,EAAIqI,EAAQ,WAAW,YAC7CqG,EAAG1O,EAAS,UAAU,EAAIqI,EAAQ,WAAW,UAC7CqG,GACAG,EAA0B,CAACxG,EAAQ,WAAW,gBAAiBA,EAAQ,WAAW,aAAa,EAC/FyG,EAA2B,CAACzG,EAAQ,WAAW,iBAAkBA,EAAQ,WAAW,cAAc,EACtGzG,EAASH,EAAS4G,EAAQ,WAAW,IAAI,EACzCzG,EAASH,EAAS4G,EAAQ,MAAQ,EAAIA,EAAQ,WAAW,eAAiBA,EAAQ,WAAW,YAAY,EACzG,SAAS0G,EAAWb,EAAMc,EAAQ,CAC9B,IAAIpO,EAAIoO,IAAW3G,EAAQ,WAAW,MAClC4G,EAAqBrO,EAAIiO,EAA0BC,EACnDI,EAActO,EAAI+N,EAAmBC,EACzC,OAAOI,EAAS,IAAMC,EAAmB5G,EAAQ,GAAG,EAAI,IAAM6G,EAAYhB,CAAI,CAC1F,CACQ,SAASiB,EAAUpO,EAAQR,EAAO2N,EAAM,CAGpC,GADAA,EAAOM,EAAaA,EAAWjO,EAAO2N,CAAI,EAAIA,EAC1CA,IAASlO,EAAS,KAItB,KAAIoP,EAAOhF,EAAU3I,EAAS,EAAK,EACnC2N,EAAK,UAAYL,EAAWb,EAAM7F,EAAQ,WAAW,MAAM,EAC3D+G,EAAK,MAAM/G,EAAQ,KAAK,EAAItH,EAAS,IAEjCmN,EAAOlO,EAAS,UAChBoP,EAAOhF,EAAU3I,EAAS,EAAK,EAC/B2N,EAAK,UAAYL,EAAWb,EAAM7F,EAAQ,WAAW,KAAK,EAC1D+G,EAAK,aAAa,aAAc,OAAO7O,CAAK,CAAC,EAC7C6O,EAAK,MAAM/G,EAAQ,KAAK,EAAItH,EAAS,IACrCqO,EAAK,UAAY,OAAOzH,EAAU,GAAGpH,CAAK,CAAC,GAE3D,CAEQ,cAAO,KAAKuM,CAAM,EAAE,QAAQ,SAAU/L,EAAQ,CAC1CoO,EAAUpO,EAAQ+L,EAAO/L,CAAM,EAAE,CAAC,EAAG+L,EAAO/L,CAAM,EAAE,CAAC,CAAC,CAClE,CAAS,EACMU,CACf,CACI,SAAS4N,IAAa,CACd9F,IACAnJ,GAAcmJ,CAAU,EACxBA,EAAa,KAEzB,CACI,SAASqD,GAAKA,EAAM,CAEhByC,GAAY,EACZ,IAAIvC,EAASG,GAAeL,CAAI,EAC5B0C,EAAS1C,EAAK,OACd2C,EAAS3C,EAAK,QAAU,CACxB,GAAI,SAAUrM,EAAO,CACjB,OAAO,OAAO,KAAK,MAAMA,CAAK,CAAC,CAClC,CACJ,EACD,OAAAgJ,EAAaL,EAAa,YAAYqF,GAAWzB,EAAQwC,EAAQC,CAAM,CAAC,EACjEhG,CACf,CAEI,SAASiG,IAAW,CAChB,IAAItO,EAAOiI,EAAW,sBAAuB,EACzCsG,EAAO,SAAW,CAAC,QAAS,QAAQ,EAAEpH,EAAQ,GAAG,EACrD,OAAOA,EAAQ,MAAQ,EAAInH,EAAK,OAASiI,EAAWsG,CAAG,EAAIvO,EAAK,QAAUiI,EAAWsG,CAAG,CAChG,CAEI,SAASC,GAAYC,EAAQlO,EAASmO,EAAUC,EAAM,CAGlD,IAAIC,EAAS,SAAUlF,EAAO,CAC1B,IAAInK,EAAIsP,GAASnF,EAAOiF,EAAK,WAAYA,EAAK,QAAUpO,CAAO,EAoB/D,GAjBI,CAAChB,GAKD8K,GAAkB,GAAI,CAACsE,EAAK,aAI5B1N,GAAS+G,EAAcb,EAAQ,WAAW,GAAG,GAAK,CAACwH,EAAK,aAIxDF,IAAW3G,EAAQ,OAASvI,EAAE,UAAY,QAAaA,EAAE,QAAU,GAInEoP,EAAK,OAASpP,EAAE,QAChB,MAAO,GAONiC,GACDjC,EAAE,eAAgB,EAEtBA,EAAE,UAAYA,EAAE,OAAO4H,EAAQ,GAAG,EAElCuH,EAASnP,EAAGoP,CAAI,CAEnB,EACGG,EAAU,CAAE,EAEhB,OAAAL,EAAO,MAAM,GAAG,EAAE,QAAQ,SAAUM,EAAW,CAC3CxO,EAAQ,iBAAiBwO,EAAWH,EAAQpN,EAAkB,CAAE,QAAS,EAAM,EAAG,EAAK,EACvFsN,EAAQ,KAAK,CAACC,EAAWH,CAAM,CAAC,CAC5C,CAAS,EACME,CACf,CAEI,SAASD,GAAStP,EAAGY,EAAY6O,EAAa,CAI1C,IAAIC,EAAQ1P,EAAE,KAAK,QAAQ,OAAO,IAAM,EACpC2P,EAAQ3P,EAAE,KAAK,QAAQ,OAAO,IAAM,EACpC4P,EAAU5P,EAAE,KAAK,QAAQ,SAAS,IAAM,EACxC6B,EAAI,EACJC,EAAI,EAQR,GANI9B,EAAE,KAAK,QAAQ,WAAW,IAAM,IAChC4P,EAAU,IAKV5P,EAAE,OAAS,aAAe,CAACA,EAAE,SAAW,CAACA,EAAE,QAC3C,MAAO,GAGX,GAAI0P,EAAO,CAEP,IAAIG,EAAkB,SAAUC,EAAY,CACxC,IAAIzH,EAASyH,EAAW,OACxB,OAAQzH,IAAWoH,GACfA,EAAY,SAASpH,CAAM,GAC1BrI,EAAE,UAAYA,EAAE,aAAY,EAAG,MAAO,IAAKyP,CACnD,EAGD,GAAIzP,EAAE,OAAS,aAAc,CACzB,IAAI+P,EAAgB,MAAM,UAAU,OAAO,KAAK/P,EAAE,QAAS6P,CAAe,EAE1E,GAAIE,EAAc,OAAS,EACvB,MAAO,GAEXlO,EAAIkO,EAAc,CAAC,EAAE,MACrBjO,EAAIiO,EAAc,CAAC,EAAE,KACrC,KACiB,CAED,IAAIC,EAAc,MAAM,UAAU,KAAK,KAAKhQ,EAAE,eAAgB6P,CAAe,EAE7E,GAAI,CAACG,EACD,MAAO,GAEXnO,EAAImO,EAAY,MAChBlO,EAAIkO,EAAY,KAChC,CACA,CACQ,OAAApP,EAAaA,GAAcC,GAAc0I,CAAc,GACnDoG,GAASC,KACT/N,EAAI7B,EAAE,QAAUY,EAAW,EAC3BkB,EAAI9B,EAAE,QAAUY,EAAW,GAE/BZ,EAAE,WAAaY,EACfZ,EAAE,OAAS,CAAC6B,EAAGC,CAAC,EAChB9B,EAAE,OAAS2P,GAASC,EACb5P,CACf,CAEI,SAASiQ,GAAsBC,EAAW,CACtC,IAAIC,EAAWD,EAAY5P,GAAOoI,EAAYd,EAAQ,GAAG,EACrDwI,EAAYD,EAAW,IAAOpB,GAAU,EAI5C,OAAAqB,EAAW/O,GAAM+O,CAAQ,EAClBxI,EAAQ,IAAM,IAAMwI,EAAWA,CAC9C,CAEI,SAASC,GAAiBC,EAAiB,CACvC,IAAIC,EAAqB,IACrBvG,EAAe,GACnB,OAAApB,EAAc,QAAQ,SAAUsB,EAAQvG,EAAO,CAE3C,GAAI,CAAAoH,EAAiBpH,CAAK,EAG1B,KAAI6M,EAAiBtH,EAAgBvF,CAAK,EACtC8M,EAA2B,KAAK,IAAID,EAAiBF,CAAe,EAEpEI,EAAcD,IAA6B,KAAOF,IAAuB,IAEzEI,EAAWF,EAA2BF,EACtCK,EAAgBH,GAA4BF,GAAsBD,EAAkBE,GACpFG,GAAYC,GAAiBF,KAC7B1G,EAAerG,EACf4M,EAAqBE,GAErC,CAAS,EACMzG,CACf,CAEI,SAAS6G,GAAc1G,EAAOiF,EAAM,CAC5BjF,EAAM,OAAS,YACfA,EAAM,OAAO,WAAa,QAC1BA,EAAM,gBAAkB,MACxB2G,GAAS3G,EAAOiF,CAAI,CAEhC,CAEI,SAAS2B,GAAU5G,EAAOiF,EAAM,CAM5B,GAAI,UAAU,WAAW,QAAQ,QAAQ,IAAM,IAAMjF,EAAM,UAAY,GAAKiF,EAAK,kBAAoB,EACjG,OAAO0B,GAAS3G,EAAOiF,CAAI,EAG/B,IAAI4B,GAAYpJ,EAAQ,IAAM,GAAK,IAAMuC,EAAM,UAAYiF,EAAK,gBAE5DgB,EAAYY,EAAW,IAAO5B,EAAK,SACvC6B,GAAYD,EAAW,EAAGZ,EAAUhB,EAAK,UAAWA,EAAK,cAAeA,EAAK,OAAO,CAC5F,CAEI,SAAS0B,GAAS3G,EAAOiF,EAAM,CAEvBA,EAAK,SACLhO,GAAYgO,EAAK,OAAQxH,EAAQ,WAAW,MAAM,EAClDwB,GAA4B,GAGhCgG,EAAK,UAAU,QAAQ,SAAU8B,EAAG,CAChC1H,EAAsB,oBAAoB0H,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,CAChE,CAAS,EACG9H,IAA6B,IAE7BhI,GAAYqH,EAAcb,EAAQ,WAAW,IAAI,EACjDuJ,GAAW,EAEPhH,EAAM,SACNV,EAAW,MAAM,OAAS,GAC1BA,EAAW,oBAAoB,cAAe1J,EAAc,IAGhE6H,EAAQ,OAAO,cACfwH,EAAK,cAAc,QAAQ,SAAUpF,EAAc,CAC/CoH,GAAUpH,EAAcd,EAAgBc,CAAY,EAAG,GAAM,GAAM,GAAO,EAAK,CAC/F,CAAa,EACDoF,EAAK,cAAc,QAAQ,SAAUpF,EAAc,CAC/CqH,EAAU,SAAUrH,CAAY,CAChD,CAAa,GAELoF,EAAK,cAAc,QAAQ,SAAUpF,EAAc,CAC/CqH,EAAU,SAAUrH,CAAY,EAChCqH,EAAU,MAAOrH,CAAY,EAC7BqH,EAAU,MAAOrH,CAAY,CACzC,CAAS,CACT,CAEI,SAASsH,GAAWnH,EAAOiF,EAAM,CAE7B,GAAI,CAAAA,EAAK,cAAc,KAAKrE,CAAgB,EAG5C,KAAIb,EACJ,GAAIkF,EAAK,cAAc,SAAW,EAAG,CACjC,IAAIpE,EAAepC,EAAcwG,EAAK,cAAc,CAAC,CAAC,EACtDlF,EAASc,EAAa,SAAS,CAAC,EAChC5B,GAA4B,EAE5BjI,EAAS+I,EAAQtC,EAAQ,WAAW,MAAM,CACtD,CAEQuC,EAAM,gBAAiB,EAEvB,IAAIoH,EAAY,CAAE,EAEdC,EAAYvC,GAAY1G,EAAQ,KAAMiB,EAAuBuH,GAAW,CAGxE,OAAQ5G,EAAM,OACd,OAAQD,EACR,QAASkF,EAAK,QACd,UAAWmC,EACX,eAAgBpH,EAAM,UACtB,SAAU4E,GAAU,EACpB,WAAY5E,EAAM,WAClB,cAAeiF,EAAK,cACpB,gBAAiBjF,EAAM,QACvB,UAAWjB,EAAgB,MAAO,CAC9C,CAAS,EACGuI,EAAWxC,GAAY1G,EAAQ,IAAKiB,EAAuBsH,GAAU,CACrE,OAAQ3G,EAAM,OACd,OAAQD,EACR,UAAWqH,EACX,YAAa,GACb,cAAenC,EAAK,aAChC,CAAS,EACGsC,EAAWzC,GAAY,WAAYzF,EAAuBqH,GAAe,CACzE,OAAQ1G,EAAM,OACd,OAAQD,EACR,UAAWqH,EACX,YAAa,GACb,cAAenC,EAAK,aAChC,CAAS,EAGDmC,EAAU,KAAK,MAAMA,EAAWC,EAAU,OAAOC,EAAUC,CAAQ,CAAC,EAGhEvH,EAAM,SAENV,EAAW,MAAM,OAAS,iBAAiBU,EAAM,MAAM,EAAE,OAErDvB,EAAc,OAAS,GACvBzH,EAASsH,EAAcb,EAAQ,WAAW,IAAI,EAQlD6B,EAAW,iBAAiB,cAAe1J,GAAgB,EAAK,GAEpEqP,EAAK,cAAc,QAAQ,SAAUpF,EAAc,CAC/CqH,EAAU,QAASrH,CAAY,CAC3C,CAAS,EACT,CAEI,SAAS2H,GAASxH,EAAO,CAErBA,EAAM,gBAAiB,EACvB,IAAIiG,EAAWH,GAAsB9F,EAAM,SAAS,EAChDH,EAAeqG,GAAiBD,CAAQ,EAExCpG,IAAiB,KAKhBpC,EAAQ,OAAO,MAChB7G,GAAY0H,EAAcb,EAAQ,WAAW,IAAKA,EAAQ,iBAAiB,EAE/EwJ,GAAUpH,EAAcoG,EAAU,GAAM,EAAI,EAC5Ce,GAAW,EACXE,EAAU,QAASrH,EAAc,EAAI,EACrCqH,EAAU,SAAUrH,EAAc,EAAI,EACjCpC,EAAQ,OAAO,KAKhB0J,GAAWnH,EAAO,CAAE,cAAe,CAACH,CAAY,CAAC,CAAE,GAJnDqH,EAAU,SAAUrH,EAAc,EAAI,EACtCqH,EAAU,MAAOrH,EAAc,EAAI,GAK/C,CAEI,SAAS4H,GAAWzH,EAAO,CACvB,IAAIiG,EAAWH,GAAsB9F,EAAM,SAAS,EAChD9J,EAAK2I,EAAe,QAAQoH,CAAQ,EACpCtQ,EAAQkJ,EAAe,aAAa3I,CAAE,EAC1C,OAAO,KAAKgJ,CAAY,EAAE,QAAQ,SAAUwI,EAAa,CACrCA,EAAY,MAAM,GAAG,EAAE,CAAC,IAApC,SACAxI,EAAawI,CAAW,EAAE,QAAQ,SAAU1C,EAAU,CAClDA,EAAS,KAAK2C,GAAYhS,CAAK,CACnD,CAAiB,CAEjB,CAAS,CACT,CAGI,SAASsK,GAAaD,EAAOH,EAAc,CACvC,GAAIc,GAAkB,GAAIC,EAAiBf,CAAY,EACnD,MAAO,GAEX,IAAI+H,EAAiB,CAAC,OAAQ,OAAO,EACjCC,EAAe,CAAC,OAAQ,IAAI,EAC5BC,EAAgB,CAAC,WAAY,QAAQ,EACrCC,EAAW,CAAC,OAAQ,KAAK,EACzBtK,EAAQ,KAAO,CAACA,EAAQ,IAExBmK,EAAe,QAAS,EAEnBnK,EAAQ,KAAO,CAACA,EAAQ,MAE7BoK,EAAa,QAAS,EACtBC,EAAc,QAAS,GAG3B,IAAIvK,EAAMyC,EAAM,IAAI,QAAQ,QAAS,EAAE,EACnCgI,EAAczK,IAAQuK,EAAc,CAAC,EACrCG,EAAY1K,IAAQuK,EAAc,CAAC,EACnC1N,EAASmD,IAAQsK,EAAa,CAAC,GAAKtK,IAAQqK,EAAe,CAAC,GAAKI,EACjEE,EAAO3K,IAAQsK,EAAa,CAAC,GAAKtK,IAAQqK,EAAe,CAAC,GAAKK,EAC/DE,EAAQ5K,IAAQwK,EAAS,CAAC,EAC1BK,EAAQ7K,IAAQwK,EAAS,CAAC,EAC9B,GAAI,CAAC3N,GAAU,CAAC8N,GAAQ,CAACC,GAAS,CAACC,EAC/B,MAAO,GAEXpI,EAAM,eAAgB,EACtB,IAAI9J,EACJ,GAAIgS,GAAQ9N,EAAQ,CAChB,IAAIT,EAAYS,EAAS,EAAI,EACzBmJ,EAAQ8E,GAAsBxI,CAAY,EAC1ChF,EAAO0I,EAAM5J,CAAS,EAE1B,GAAIkB,IAAS,KACT,MAAO,GAGPA,IAAS,KACTA,EAAOgE,EAAe,eAAeE,EAAgBc,CAAY,EAAGzF,EAAQqD,EAAQ,mBAAmB,GAEvGwK,GAAaD,EACbnN,GAAQ4C,EAAQ,uBAGhB5C,GAAQ4C,EAAQ,mBAGpB5C,EAAO,KAAK,IAAIA,EAAM,IAAS,EAE/BA,GAAQT,EAAS,GAAK,GAAKS,EAC3B3E,EAAK4I,EAAae,CAAY,EAAIhF,CAC9C,MACiBuN,EAELlS,EAAKuH,EAAQ,SAAS,KAAKA,EAAQ,SAAS,KAAK,OAAS,CAAC,EAI3DvH,EAAKuH,EAAQ,SAAS,KAAK,CAAC,EAEhC,OAAAwJ,GAAUpH,EAAchB,EAAe,WAAW3I,CAAE,EAAG,GAAM,EAAI,EACjEgR,EAAU,QAASrH,CAAY,EAC/BqH,EAAU,SAAUrH,CAAY,EAChCqH,EAAU,SAAUrH,CAAY,EAChCqH,EAAU,MAAOrH,CAAY,EACtB,EACf,CAEI,SAASyI,GAAiBC,EAAW,CAE5BA,EAAU,OACX9J,EAAc,QAAQ,SAAUsB,EAAQvG,EAAO,CAG3CsL,GAAY1G,EAAQ,MAAO2B,EAAO,SAAS,CAAC,EAAGoH,GAAY,CACvD,cAAe,CAAC3N,CAAK,CACzC,CAAiB,CACjB,CAAa,EAGD+O,EAAU,KACVzD,GAAY1G,EAAQ,MAAOG,EAAYiJ,GAAU,CAAA,CAAE,EAGnDe,EAAU,OACVzD,GAAY1G,EAAQ,KAAMG,EAAYkJ,GAAY,CAC9C,MAAO,EACvB,CAAa,EAGDc,EAAU,MACV7J,EAAe,QAAQ,SAAU9C,EAASpC,EAAO,CAC7C,GAAI,EAAAoC,IAAY,IAASpC,IAAU,GAAKA,IAAUkF,EAAe,OAAS,GAG1E,KAAI8J,EAAe/J,EAAcjF,EAAQ,CAAC,EACtCiP,EAAchK,EAAcjF,CAAK,EACjCkP,EAAe,CAAC9M,CAAO,EACvB+M,EAAgB,CAACH,EAAcC,CAAW,EAC1CG,EAAsB,CAACpP,EAAQ,EAAGA,CAAK,EAC3CxC,EAAS4E,EAAS6B,EAAQ,WAAW,SAAS,EAK1C8K,EAAU,QACVG,EAAa,KAAKF,EAAa,SAAS,CAAC,CAAC,EAC1CE,EAAa,KAAKD,EAAY,SAAS,CAAC,CAAC,GAEzCF,EAAU,UACVI,EAAgBlK,EAChBmK,EAAsB5J,GAE1B0J,EAAa,QAAQ,SAAUG,EAAa,CACxC/D,GAAY1G,EAAQ,MAAOyK,EAAa1B,GAAY,CAChD,QAASwB,EACT,cAAeC,EACf,QAAShN,CACjC,CAAqB,CACrB,CAAiB,EACjB,CAAa,CAEb,CAEI,SAASwF,GAAU0H,EAAiB9D,EAAU,CAC1C9F,EAAa4J,CAAe,EAAI5J,EAAa4J,CAAe,GAAK,CAAE,EACnE5J,EAAa4J,CAAe,EAAE,KAAK9D,CAAQ,EAEvC8D,EAAgB,MAAM,GAAG,EAAE,CAAC,IAAM,UAClCrK,EAAc,QAAQ,SAAUzI,EAAGwD,EAAO,CACtC0N,EAAU,SAAU1N,CAAK,CACzC,CAAa,CAEb,CACI,SAASuP,GAAoBC,EAAW,CACpC,OAAOA,IAAchO,EAAkB,MAAQgO,IAAchO,EAAkB,QACvF,CAEI,SAASiG,GAAY6H,EAAiB,CAClC,IAAI9I,EAAQ8I,GAAmBA,EAAgB,MAAM,GAAG,EAAE,CAAC,EACvDE,EAAYhJ,EAAQ8I,EAAgB,UAAU9I,EAAM,MAAM,EAAI8I,EAClE,OAAO,KAAK5J,CAAY,EAAE,QAAQ,SAAU+J,EAAM,CAC9C,IAAIC,EAASD,EAAK,MAAM,GAAG,EAAE,CAAC,EAC1BE,EAAaF,EAAK,UAAUC,EAAO,MAAM,GACxC,CAAClJ,GAASA,IAAUkJ,KAAY,CAACF,GAAaA,IAAcG,KAEzD,CAACJ,GAAoBI,CAAU,GAAKH,IAAcG,IAClD,OAAOjK,EAAa+J,CAAI,CAG5C,CAAS,CACT,CAEI,SAAS/B,EAAU7B,EAAWxF,EAAcvD,EAAK,CAC7C,OAAO,KAAK4C,CAAY,EAAE,QAAQ,SAAUwI,EAAa,CACrD,IAAI0B,EAAY1B,EAAY,MAAM,GAAG,EAAE,CAAC,EACpCrC,IAAc+D,GACdlK,EAAawI,CAAW,EAAE,QAAQ,SAAU1C,EAAU,CAClDA,EAAS,KAET2C,GAEA7I,EAAa,IAAIrB,EAAQ,OAAO,EAAE,EAElCoC,EAEAf,EAAa,MAAO,EAEpBxC,GAAO,GAEPyC,EAAgB,MAAO,EAEvB4I,EAAU,CAC9B,CAAiB,CAEjB,CAAS,CACT,CAEI,SAAShG,GAAoB0H,EAAWxJ,EAAc3J,EAAIoT,EAAcC,EAAaC,EAAU3M,EAAa,CACxG,IAAI4M,EA4CJ,OAzCIhL,EAAc,OAAS,GAAK,CAAChB,EAAQ,OAAO,gBACxC6L,GAAgBzJ,EAAe,IAC/B4J,EAAW5K,EAAe,oBAAoBwK,EAAUxJ,EAAe,CAAC,EAAGpC,EAAQ,OAAQ,EAAK,EAChGvH,EAAK,KAAK,IAAIA,EAAIuT,CAAQ,GAE1BF,GAAe1J,EAAepB,EAAc,OAAS,IACrDgL,EAAW5K,EAAe,oBAAoBwK,EAAUxJ,EAAe,CAAC,EAAGpC,EAAQ,OAAQ,EAAI,EAC/FvH,EAAK,KAAK,IAAIA,EAAIuT,CAAQ,IAM9BhL,EAAc,OAAS,GAAKhB,EAAQ,QAChC6L,GAAgBzJ,EAAe,IAC/B4J,EAAW5K,EAAe,oBAAoBwK,EAAUxJ,EAAe,CAAC,EAAGpC,EAAQ,MAAO,EAAK,EAC/FvH,EAAK,KAAK,IAAIA,EAAIuT,CAAQ,GAE1BF,GAAe1J,EAAepB,EAAc,OAAS,IACrDgL,EAAW5K,EAAe,oBAAoBwK,EAAUxJ,EAAe,CAAC,EAAGpC,EAAQ,MAAO,EAAI,EAC9FvH,EAAK,KAAK,IAAIA,EAAIuT,CAAQ,IAK9BhM,EAAQ,UACJoC,IAAiB,IACjB4J,EAAW5K,EAAe,oBAAoB,EAAGpB,EAAQ,QAAQ,CAAC,EAAG,EAAK,EAC1EvH,EAAK,KAAK,IAAIA,EAAIuT,CAAQ,GAE1B5J,IAAiBpB,EAAc,OAAS,IACxCgL,EAAW5K,EAAe,oBAAoB,IAAKpB,EAAQ,QAAQ,CAAC,EAAG,EAAI,EAC3EvH,EAAK,KAAK,IAAIA,EAAIuT,CAAQ,IAG7B5M,IACD3G,EAAK2I,EAAe,QAAQ3I,CAAE,GAGlCA,EAAKgB,GAAMhB,CAAE,EAETA,IAAOmT,EAAUxJ,CAAY,GAAK,CAAC2J,EAC5B,GAEJtT,CACf,CAEI,SAASwT,GAAYC,EAAG3T,EAAG,CACvB,IAAI4T,EAAInM,EAAQ,IAChB,OAAQmM,EAAI5T,EAAI2T,GAAK,MAAQC,EAAID,EAAI3T,EAC7C,CAGI,SAAS8Q,GAAY+C,EAAQ5D,EAAU6D,EAAWC,EAAenO,EAAS,CACtE,IAAIoO,EAAYF,EAAU,MAAO,EAE7BG,EAAcF,EAAc,CAAC,EAC7BlN,EAAcY,EAAQ,OAAO,YAC7BpE,EAAI,CAAC,CAACwQ,EAAQA,CAAM,EACpBK,EAAI,CAACL,EAAQ,CAACA,CAAM,EAExBE,EAAgBA,EAAc,MAAO,EAGjCF,GACAE,EAAc,QAAS,EAGvBA,EAAc,OAAS,EACvBA,EAAc,QAAQ,SAAUlK,EAAc+J,EAAG,CAC7C,IAAI1T,EAAKyL,GAAoBqI,EAAWnK,EAAcmK,EAAUnK,CAAY,EAAIoG,EAAU5M,EAAEuQ,CAAC,EAAGM,EAAEN,CAAC,EAAG,GAAO/M,CAAW,EAEpH3G,IAAO,GACP+P,EAAW,GAGXA,EAAW/P,EAAK8T,EAAUnK,CAAY,EACtCmK,EAAUnK,CAAY,EAAI3J,EAE9C,CAAa,EAIDmD,EAAI6Q,EAAI,CAAC,EAAI,EAEjB,IAAIC,EAAQ,GAEZJ,EAAc,QAAQ,SAAUlK,EAAc+J,EAAG,CAC7CO,EACIlD,GAAUpH,EAAciK,EAAUjK,CAAY,EAAIoG,EAAU5M,EAAEuQ,CAAC,EAAGM,EAAEN,CAAC,EAAG,GAAO/M,CAAW,GAAKsN,CAC/G,CAAS,EAEGA,IACAJ,EAAc,QAAQ,SAAUlK,EAAc,CAC1CqH,EAAU,SAAUrH,CAAY,EAChCqH,EAAU,QAASrH,CAAY,CAC/C,CAAa,EAEGjE,GAAW,MACXsL,EAAU,OAAQ+C,CAAW,EAG7C,CAKI,SAASG,GAAmBpU,EAAGqD,EAAG,CAC9B,OAAOoE,EAAQ,IAAM,IAAMzH,EAAIqD,EAAIrD,CAC3C,CAEI,SAASqU,GAAqBxK,EAAc3J,EAAI,CAE5C6I,EAAgBc,CAAY,EAAI3J,EAEhC4I,EAAae,CAAY,EAAIhB,EAAe,aAAa3I,CAAE,EAC3D,IAAIoU,EAAcF,GAAmBlU,EAAI,CAAC,EAAIqJ,GAC1CgL,EAAgB,aAAeb,GAAYY,EAAc,IAAK,GAAG,EAAI,IAGzE,GAFA7L,EAAcoB,CAAY,EAAE,MAAMpC,EAAQ,aAAa,EAAI8M,EAEvD9M,EAAQ,OAAO,gBAAkBsB,EAAgB,OAAS,EAAG,CAE7D,IAAIyL,EAAoBzL,EAAgB,MAAM,SAAU0L,EAAUjR,EAAOsQ,EAAW,CAChF,OAAOtQ,IAAU,GAAKiR,GAAYX,EAAUtQ,EAAQ,CAAC,CACrE,CAAa,EACD,GAAI2F,IAA2B,CAACqL,EAAmB,CAE/C7N,GAAgB,EAEhB,MAChB,CACA,CACQ+N,GAAc7K,CAAY,EAC1B6K,GAAc7K,EAAe,CAAC,EAC1BV,IAEAuL,GAAc7K,EAAe,CAAC,EAC9B6K,GAAc7K,EAAe,CAAC,EAE1C,CAII,SAASmH,IAAY,CACjBhI,EAAoB,QAAQ,SAAUa,EAAc,CAChD,IAAI8K,EAAM5L,EAAgBc,CAAY,EAAI,GAAK,GAAK,EAChD+K,EAAS,GAAKnM,EAAc,OAASkM,EAAM9K,GAC/CpB,EAAcoB,CAAY,EAAE,MAAM,OAAS,OAAO+K,CAAM,CACpE,CAAS,CACT,CAGI,SAAS3D,GAAUpH,EAAc3J,EAAIoT,EAAcC,EAAasB,EAAYhO,EAAa,CAIrF,OAHKgO,IACD3U,EAAKyL,GAAoB5C,EAAiBc,EAAc3J,EAAIoT,EAAcC,EAAa,GAAO1M,CAAW,GAEzG3G,IAAO,GACA,IAEXmU,GAAqBxK,EAAc3J,CAAE,EAC9B,GACf,CAEI,SAASwU,GAAclR,EAAO,CAE1B,GAAKkF,EAAelF,CAAK,EAIzB,KAAIsQ,EAAY/K,EAAgB,MAAO,EACnCI,GACA2K,EAAU,KAAK,SAAU9T,EAAGqD,EAAG,CAC3B,OAAOrD,EAAIqD,CAC3B,CAAa,EAEL,IAAIyR,EAAI,EACJC,EAAI,IACJvR,IAAU,IACVsR,EAAIhB,EAAUtQ,EAAQ,CAAC,GAEvBA,IAAUkF,EAAe,OAAS,IAClCqM,EAAIjB,EAAUtQ,CAAK,GAMvB,IAAIwR,EAAeD,EAAID,EACnBP,EAAgB,aAAeb,GAAYU,GAAmBU,EAAGE,CAAY,EAAI,IAAK,GAAG,EAAI,IAC7FC,EAAY,SAAWvB,GAAYsB,EAAe,IAAK,GAAG,EAAI,IAClEtM,EAAelF,CAAK,EAAE,MAAMiE,EAAQ,aAAa,EAC7C8M,EAAgB,IAAMU,EAClC,CAEI,SAASC,GAAehV,EAAI2J,EAAc,CAetC,OAZI3J,IAAO,MAAQA,IAAO,IAASA,IAAO,SAItC,OAAOA,GAAO,WACdA,EAAK,OAAOA,CAAE,GAElBA,EAAKuH,EAAQ,OAAO,KAAKvH,CAAE,EACvBA,IAAO,KACPA,EAAK2I,EAAe,WAAW3I,CAAE,GAGjCA,IAAO,IAAS,MAAMA,CAAE,GACjB6I,EAAgBc,CAAY,EAEhC3J,CACf,CAEI,SAASiV,GAASC,EAAOC,EAAcR,EAAY,CAC/C,IAAIxJ,EAASlK,GAAQiU,CAAK,EACtBE,EAASvM,EAAgB,CAAC,IAAM,OAEpCsM,EAAeA,IAAiB,OAAY,GAAOA,EAG/C5N,EAAQ,SAAW,CAAC6N,GACpB1U,GAAY0H,EAAcb,EAAQ,WAAW,IAAKA,EAAQ,iBAAiB,EAG/EuB,EAAoB,QAAQ,SAAUa,EAAc,CAChDoH,GAAUpH,EAAcqL,GAAe7J,EAAOxB,CAAY,EAAGA,CAAY,EAAG,GAAM,GAAOgL,CAAU,CAC/G,CAAS,EACD,IAAIpQ,EAAIuE,EAAoB,SAAW,EAAI,EAAI,EAE/C,GAAIsM,GAAUzM,EAAe,cACzBgM,EAAa,GACb9L,EAAgB,CAAC,EAAI,EACjBC,EAAoB,OAAS,GAAG,CAChC,IAAIuM,EAAU,KAAOvM,EAAoB,OAAS,GAClDA,EAAoB,QAAQ,SAAUa,EAAc,CAChDd,EAAgBc,CAAY,EAAIA,EAAe0L,CACnE,CAAiB,CACjB,CAIQ,KAAO9Q,EAAIuE,EAAoB,OAAQ,EAAEvE,EACrCuE,EAAoB,QAAQ,SAAUa,EAAc,CAChDoH,GAAUpH,EAAcd,EAAgBc,CAAY,EAAG,GAAM,GAAMgL,CAAU,CAC7F,CAAa,EAEL7D,GAAW,EACXhI,EAAoB,QAAQ,SAAUa,EAAc,CAChDqH,EAAU,SAAUrH,CAAY,EAE5BwB,EAAOxB,CAAY,IAAM,MAAQwL,GACjCnE,EAAU,MAAOrH,CAAY,CAE7C,CAAS,CACT,CAEI,SAAS2L,GAAWH,EAAc,CAC9BF,GAAS1N,EAAQ,MAAO4N,CAAY,CAC5C,CAEI,SAASI,GAAe5L,EAAclK,EAAO0V,EAAcR,EAAY,CAGnE,GADAhL,EAAe,OAAOA,CAAY,EAC9B,EAAEA,GAAgB,GAAKA,EAAeb,EAAoB,QAC1D,MAAM,IAAI,MAAM,2CAA6Ca,CAAY,EAI7EoH,GAAUpH,EAAcqL,GAAevV,EAAOkK,CAAY,EAAG,GAAM,GAAMgL,CAAU,EACnF3D,EAAU,SAAUrH,CAAY,EAC5BwL,GACAnE,EAAU,MAAOrH,CAAY,CAEzC,CAEI,SAAS6L,GAASpK,EAAW,CAEzB,GADIA,IAAc,SAAUA,EAAY,IACpCA,EAEA,OAAOxC,EAAa,SAAW,EAAIA,EAAa,CAAC,EAAIA,EAAa,MAAM,CAAC,EAE7E,IAAIuC,EAASvC,EAAa,IAAIrB,EAAQ,OAAO,EAAE,EAE/C,OAAI4D,EAAO,SAAW,EACXA,EAAO,CAAC,EAEZA,CACf,CAEI,SAASsK,IAAU,CAOf,IALA1K,GAAYjG,EAAkB,IAAI,EAClCiG,GAAYjG,EAAkB,QAAQ,EACtC,OAAO,KAAKyC,EAAQ,UAAU,EAAE,QAAQ,SAAUF,EAAK,CACnDtG,GAAYqH,EAAcb,EAAQ,WAAWF,CAAG,CAAC,CAC7D,CAAS,EACMe,EAAa,YAChBA,EAAa,YAAYA,EAAa,UAAU,EAEpD,OAAOA,EAAa,UAC5B,CACI,SAAS+J,GAAsBxI,EAAc,CACzC,IAAImG,EAAWjH,EAAgBc,CAAY,EACvC+L,EAAc/M,EAAe,eAAemH,CAAQ,EACpDrQ,EAAQmJ,EAAae,CAAY,EACjC0C,EAAYqJ,EAAY,SAAS,KACjCC,EAAY,KAEhB,GAAIpO,EAAQ,KACR,MAAO,CACH9H,EAAQiW,EAAY,WAAW,YAAc,KAC7CA,EAAY,UAAU,WAAajW,GAAS,IAC/C,EAID4M,IAAc,IACV5M,EAAQ4M,EAAYqJ,EAAY,UAAU,aAC1CrJ,EAAYqJ,EAAY,UAAU,WAAajW,GAInDA,EAAQiW,EAAY,SAAS,WAC7BC,EAAYD,EAAY,SAAS,KAE5BA,EAAY,WAAW,OAAS,GACrCC,EAAY,GAIZA,EAAYlW,EAAQiW,EAAY,WAAW,YAG3C5F,IAAa,IACbzD,EAAY,KAEPyD,IAAa,IAClB6F,EAAY,MAGhB,IAAIvR,EAAeuE,EAAe,kBAAmB,EAErD,OAAI0D,IAAc,MAAQA,IAAc,KACpCA,EAAY,OAAOA,EAAU,QAAQjI,CAAY,CAAC,GAElDuR,IAAc,MAAQA,IAAc,KACpCA,EAAY,OAAOA,EAAU,QAAQvR,CAAY,CAAC,GAE/C,CAACuR,EAAWtJ,CAAS,CACpC,CAEI,SAASuJ,IAAe,CACpB,OAAO9M,EAAoB,IAAIqJ,EAAqB,CAC5D,CAEI,SAAS0D,GAAcC,EAAiBX,EAAc,CAIlD,IAAI1B,EAAI+B,GAAU,EACdO,EAAa,CACb,SACA,QACA,UACA,QACA,UACA,OACA,OACA,SACA,OACA,WACA,SACH,EAEDA,EAAW,QAAQ,SAAUrO,EAAM,CAE3BoO,EAAgBpO,CAAI,IAAM,SAC1BO,EAAgBP,CAAI,EAAIoO,EAAgBpO,CAAI,EAE5D,CAAS,EACD,IAAIsO,EAAa1O,GAAYW,CAAe,EAE5C8N,EAAW,QAAQ,SAAUrO,EAAM,CAC3BoO,EAAgBpO,CAAI,IAAM,SAC1BH,EAAQG,CAAI,EAAIsO,EAAWtO,CAAI,EAE/C,CAAS,EACDiB,EAAiBqN,EAAW,SAE5BzO,EAAQ,OAASyO,EAAW,OAC5BzO,EAAQ,MAAQyO,EAAW,MAC3BzO,EAAQ,QAAUyO,EAAW,QAEzBzO,EAAQ,KACRuE,GAAKvE,EAAQ,IAAI,EAGjBgH,GAAY,EAGZhH,EAAQ,SACR0D,GAAU,EAGVH,GAAgB,EAGpBjC,EAAkB,CAAE,EACpBoM,GAASzV,GAAMsW,EAAgB,KAAK,EAAIA,EAAgB,MAAQrC,EAAG0B,CAAY,EAE3EW,EAAgB,SAChBG,GAAqB,CAEjC,CACI,SAASA,IAAsB,CAE3B,KAAO3N,EAAkB,YACrBA,EAAkB,YAAYA,EAAkB,UAAU,EAG9D,QAAS/D,EAAI,EAAGA,GAAKgD,EAAQ,QAAShD,IAClCiE,EAAejE,CAAC,EAAI2F,EAAW5B,EAAmBf,EAAQ,QAAQhD,CAAC,CAAC,EACpEiQ,GAAcjQ,CAAC,EAInB6N,GAAiB,CAAE,KAAM7K,EAAQ,OAAO,KAAM,MAAO,GAAM,CACnE,CAEI,SAASd,IAAiB,CACtBwC,EAAyB,CAACA,EAC1BxD,GAAY8B,EAEZA,EAAQ,QAAQ,IAAI,SAAUpE,EAAG,CAAE,MAAO,CAACA,CAAE,CAAE,CAAC,EAChD8S,GAAqB,CAC7B,CAEI,SAASC,IAAc,CAGnB7N,EAAaiC,GAAUlC,CAAY,EACnCgC,EAAY7C,EAAQ,QAASc,CAAU,EAEvC+J,GAAiB7K,EAAQ,MAAM,EAE/B0N,GAAS1N,EAAQ,KAAK,EAClBA,EAAQ,MACRuE,GAAKvE,EAAQ,IAAI,EAEjBA,EAAQ,UACR0D,GAAU,EAEdK,GAAM,CACd,CACI4K,GAAa,EACb,IAAIzE,GAAa,CACb,QAASgE,GACT,MAAOG,GACP,GAAI1K,GACJ,IAAKH,GACL,IAAKyK,GACL,IAAKP,GACL,UAAWM,GACX,MAAOD,GACP,QAAS1K,GACT,OAAQC,GAER,cAAe,SAAU8I,EAAQ5D,EAAU8D,EAAe,CACtDjD,GAAY+C,EAAQ5D,EAAUlH,EAAiBgL,CAAa,CAC/D,EACD,QAAS5L,EACT,cAAe4N,GACf,OAAQzN,EACR,WAAYmG,GACZ,eAAgBzD,GAChB,aAAc,UAAY,CACtB,OAAOjC,EAAgB,MAAO,CACjC,EACD,YAAa,UAAY,CACrB,OAAOH,CACV,EACD,WAAY,UAAY,CACpB,OAAOH,CACV,EACD,KAAMuD,EACT,EACD,OAAO2F,EACX,CAEA,SAAS0E,GAAWnO,EAAQC,EAAiB,CACzC,GAAI,CAACD,GAAU,CAACA,EAAO,SACnB,MAAM,IAAI,MAAM,sDAAwDA,CAAM,EAGlF,GAAIA,EAAO,WACP,MAAM,IAAI,MAAM,6CAA6C,EAGjE,IAAIT,EAAUD,GAAYW,CAAe,EACrCmO,EAAMrO,GAAMC,EAAQT,EAASU,CAAe,EAChD,OAAAD,EAAO,WAAaoO,EACbA,CACX,CAGA,MAAeC,GAAA,CAEX,WAAYjT,GAGZ,WAAYyB,GACZ,OAAQsR,EACZ,uECzxEC,SAASG,EAAS,CAMfC,EAAA,QAAiBD,GAKrB,GAAG,UAAW,CAGZ,IAAIE,EAAgB,CAClB,WACA,WACA,OACA,SACA,SACA,UACA,UACA,iBACA,WACA,OACA,MACJ,EAKE,SAASC,EAAW3W,EAAG,CACrB,OAAOA,EACJ,MAAM,EAAE,EACR,QAAS,EACT,KAAK,EAAE,CACX,CAGD,SAAS4W,EAAcxB,EAAOyB,EAAO,CACnC,OAAOzB,EAAM,UAAU,EAAGyB,EAAM,MAAM,IAAMA,CAC7C,CAGD,SAASC,EAAY1B,EAAOyB,EAAO,CACjC,OAAOzB,EAAM,MAAM,GAAKyB,EAAM,MAAM,IAAMA,CAC3C,CAGD,SAASE,EAAgBC,EAAGhX,EAAGqD,EAAG,CAChC,IAAK2T,EAAEhX,CAAC,GAAKgX,EAAE3T,CAAC,IAAM2T,EAAEhX,CAAC,IAAMgX,EAAE3T,CAAC,EAChC,MAAM,IAAI,MAAMrD,CAAC,CAEpB,CAGD,SAASiX,EAAc7B,EAAO,CAC5B,OAAO,OAAOA,GAAU,UAAY,SAASA,CAAK,CACnD,CAID,SAAS8B,EAAQvX,EAAOwX,EAAK,CAC3B,OAAAxX,EAAQA,EAAM,SAAU,EAAC,MAAM,GAAG,EAClCA,EAAQ,KAAK,MAAM,EAAEA,EAAM,CAAC,EAAI,KAAOA,EAAM,CAAC,EAAI,CAACA,EAAM,CAAC,EAAIwX,EAAMA,GAAK,EACzExX,EAAQA,EAAM,SAAU,EAAC,MAAM,GAAG,GAC1B,EAAEA,EAAM,CAAC,EAAI,KAAOA,EAAM,CAAC,EAAI,CAACA,EAAM,CAAC,EAAIwX,EAAM,CAACA,KAAO,QAAQA,CAAG,CAC7E,CAKD,SAASC,EACPC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,GACAC,EACA3C,EACA,CACA,IAAI4C,EAAgB5C,EAClB6C,EACAC,GACAC,EACAC,GAAgB,GAChBC,EAAS,GASX,OALIX,IACFtC,EAAQsC,EAAQtC,CAAK,GAIlB6B,EAAc7B,CAAK,GAMpBiC,IAAa,IAAS,WAAWjC,EAAM,QAAQiC,CAAQ,CAAC,IAAM,IAChEjC,EAAQ,GAKNA,EAAQ,IACV6C,EAAkB,GAClB7C,EAAQ,KAAK,IAAIA,CAAK,GAIpBiC,IAAa,KACfjC,EAAQ8B,EAAQ9B,EAAOiC,CAAQ,GAIjCjC,EAAQA,EAAM,WAGVA,EAAM,QAAQ,GAAG,IAAM,IACzB8C,GAAc9C,EAAM,MAAM,GAAG,EAE7B+C,EAAYD,GAAY,CAAC,EAErBX,IACFa,GAAgBb,EAAOW,GAAY,CAAC,IAItCC,EAAY/C,EAIVkC,IACFa,EAAYxB,EAAWwB,CAAS,EAAE,MAAM,SAAS,EACjDA,EAAYxB,EAAWwB,EAAU,KAAKxB,EAAWW,CAAQ,CAAC,CAAC,GAIzDW,GAAmBL,IACrBS,GAAUT,GAIRJ,IACFa,GAAUb,GAIRS,GAAmBJ,IACrBQ,GAAUR,GAIZQ,GAAUF,EACVE,GAAUD,GAGNX,IACFY,GAAUZ,GAIRK,KACFO,EAASP,GAAKO,EAAQL,CAAa,GAI9BK,GA1EE,EA2EV,CAGD,SAASC,EACPjB,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,GACAC,EACA3C,EACA,CACA,IACE6C,EACAI,EAAS,GAuEX,OApEIN,IACF3C,EAAQ2C,EAAK3C,CAAK,GAIhB,CAACA,GAAS,OAAOA,GAAU,WAM3BwC,GAAkBhB,EAAcxB,EAAOwC,CAAc,IACvDxC,EAAQA,EAAM,QAAQwC,EAAgB,EAAE,EACxCK,EAAkB,IAIhBT,GAAUZ,EAAcxB,EAAOoC,CAAM,IACvCpC,EAAQA,EAAM,QAAQoC,EAAQ,EAAE,GAI9BK,GAAYjB,EAAcxB,EAAOyC,CAAQ,IAC3CzC,EAAQA,EAAM,QAAQyC,EAAU,EAAE,EAClCI,EAAkB,IAKhBR,GAAUX,EAAY1B,EAAOqC,CAAM,IACrCrC,EAAQA,EAAM,MAAM,EAAG,GAAKqC,EAAO,MAAM,GAIvCH,IACFlC,EAAQA,EAAM,MAAMkC,CAAQ,EAAE,KAAK,EAAE,GAInCC,IACFnC,EAAQA,EAAM,QAAQmC,EAAM,GAAG,GAI7BU,IACFI,GAAU,KAIZA,GAAUjD,EAGViD,EAASA,EAAO,QAAQ,eAAgB,EAAE,EAGtCA,IAAW,MAKfA,EAAS,OAAOA,CAAM,EAGlBV,IACFU,EAASV,EAAQU,CAAM,GAIrB,CAACpB,EAAcoB,CAAM,GAChB,GAGFA,CACR,CAKD,SAASE,EAASC,EAAc,CAC9B,IAAI/T,EACFgU,EACAC,EACAC,EAAkB,CAAA,EAMpB,IAJIH,EAAa,SAAc,SAC7BA,EAAa,OAAYA,EAAa,SAGnC/T,EAAI,EAAGA,EAAIiS,EAAc,OAAQjS,GAAK,EAIzC,GAHAgU,EAAa/B,EAAcjS,CAAC,EAC5BiU,EAAcF,EAAaC,CAAU,EAEjCC,IAAgB,OAEdD,IAAe,YAAc,CAACE,EAAgB,eAChDA,EAAgBF,CAAU,EAAI,IAErBA,IAAe,QAAUE,EAAgB,WAAa,IAC/DA,EAAgBF,CAAU,EAAI,IAE9BE,EAAgBF,CAAU,EAAI,WAIvBA,IAAe,WACxB,GAAIC,GAAe,GAAKA,EAAc,EACpCC,EAAgBF,CAAU,EAAIC,MAE9B,OAAM,IAAI,MAAMD,CAAU,UAK5BA,IAAe,WACfA,IAAe,WACfA,IAAe,QACfA,IAAe,OAEf,GAAI,OAAOC,GAAgB,WACzBC,EAAgBF,CAAU,EAAIC,MAE9B,OAAM,IAAI,MAAMD,CAAU,UAKxB,OAAOC,GAAgB,SACzBC,EAAgBF,CAAU,EAAIC,MAE9B,OAAM,IAAI,MAAMD,CAAU,EAOhC,OAAA1B,EAAgB4B,EAAiB,OAAQ,UAAU,EACnD5B,EAAgB4B,EAAiB,SAAU,UAAU,EACrD5B,EAAgB4B,EAAiB,SAAU,gBAAgB,EAEpDA,CACR,CAGD,SAASC,EAAQnR,EAASyH,EAAQkG,EAAO,CACvC,IAAI3Q,EACFoU,EAAO,CAAA,EAGT,IAAKpU,EAAI,EAAGA,EAAIiS,EAAc,OAAQjS,GAAK,EACzCoU,EAAK,KAAKpR,EAAQiP,EAAcjS,CAAC,CAAC,CAAC,EAKrC,OAAAoU,EAAK,KAAKzD,CAAK,EACRlG,EAAO,MAAM,GAAI2J,CAAI,CAC7B,CAED,SAASC,EAAMrR,EAAS,CACtB,GAAI,EAAE,gBAAgBqR,GACpB,OAAO,IAAIA,EAAMrR,CAAO,EAGtB,OAAOA,GAAY,WAIvBA,EAAU8Q,EAAS9Q,CAAO,EAG1B,KAAK,GAAK,SAAS2N,EAAO,CACxB,OAAOwD,EAAQnR,EAAS2P,EAAUhC,CAAK,CAC7C,EAGI,KAAK,KAAO,SAASA,EAAO,CAC1B,OAAOwD,EAAQnR,EAAS6Q,EAAYlD,CAAK,CAC/C,EACG,CAED,OAAO0D,CACT,CAAC,+CC5XD,MAAMC,CAAkB,CACtB,aAAc,CACZ,KAAK,QAAU,OAAO,SAAS,OAAS,OAAO,SAAS,SACxD,KAAK,aAAe,KACpB,KAAK,UAAY,IACrB,CAEE,iBAAkB,CAChB,KAAK,aAAe,KAAK,SAC7B,CAEE,eAAgB,CACd,YAAK,gBAAiB,EACf,GAAG,KAAK,OAAO,MAAM,KAAK,SAAS,EAC9C,CAEE,cAAe,CACb,MAAMC,EAAe,IAAI,gBAAgB,OAAO,SAAS,MAAM,EAC/D,KAAK,UAAYA,EAAa,IAAI,GAAG,EACrC,KAAK,aAAeA,EAAa,IAAI,GAAG,CAC5C,CAEE,eAAexM,EAAO,CAAE,KAAAyM,EAAM,KAAAC,EAAM,GAAAhZ,CAAE,EAAI,CACxC,KAAK,YAAYsM,CAAK,EAEtB,KAAK,YAAYA,EAAOyM,CAAI,EAC5B,KAAK,YAAYzM,EAAO0M,CAAI,EAC5B,KAAK,YAAY1M,EAAOtM,CAAE,CAC9B,CAEE,YAAYsM,EAAO2M,EAAM,CACvB,MAAMC,EAAe,KAAK,WAAa,GACvC,IAAIC,EAAeD,EAAa,OAASA,EAAa,MAAM,GAAG,EAAI,CAAE,EACjEE,EAAa,GACjB,MAAMC,EAAqBF,EAAa,OACxC7M,EAAQuM,EAAkB,cAAcvM,CAAK,EAC7C2M,EAAOJ,EAAkB,cAAcI,CAAI,EAE3C,QAAS1U,EAAI,EAAGA,EAAI8U,EAAoB9U,GAAK,EAI3C,GAHoB4U,EAAa5U,CAAC,EACG,MAAM,GAAG,EAEzB,CAAC,IAAM+H,EAAO,CACjC6M,EAAa5U,CAAC,EAAI,GAAG4U,EAAa5U,CAAC,CAAC,IAAI0U,CAAI,GAC5CG,EAAa,GACb,KACR,CAGSA,IACHD,EAAe,CAAC,GAAGA,EAAc,GAAG7M,CAAK,IAAI2M,CAAI,EAAE,GAGrD,KAAK,UAAYJ,EAAkB,cAAcA,EAAkB,gBAAgBM,CAAY,CAAC,CACpG,CAEE,YAAY7M,EAAO,CACjB,MAAM4M,EAAe,KAAK,WAAa,GACjCC,EAAeD,EAAa,OAASA,EAAa,MAAM,GAAG,EAAI,CAAE,EACjEG,EAAqBF,EAAa,OAExC,QAAS5U,EAAI,EAAGA,EAAI8U,EAAoB9U,GAAK,EACvB4U,EAAa5U,CAAC,EACG,MAAM,GAAG,EAEzB,CAAC,IAAM+H,GAC1B6M,EAAa,OAAO5U,EAAG,CAAC,EAI5B,KAAK,UAAYsU,EAAkB,cAAcA,EAAkB,gBAAgBM,CAAY,CAAC,CACpG,CAEE,OAAO,SAAS1Z,EAAO,CACrB,MAAO,GAAGA,CAAK,EACnB,CAEE,OAAO,cAAc6Z,EAAK,CACxB,OAAOT,EAAkB,SAASS,CAAG,EAAE,QAAQ,IAAK,SAAS,CACjE,CAEE,OAAO,cAAcA,EAAK,CACxB,OAAOT,EAAkB,SAASS,CAAG,EAAE,QAAQ,UAAW,GAAG,CACjE,CAEE,YAAYhN,EAAO2M,EAAM,CACvB,MAAMC,EAAe,KAAK,WAAa,GACjCC,EAAeD,EAAa,OAASA,EAAa,MAAM,GAAG,EAAI,CAAE,EACjEG,EAAqBF,EAAa,OAExC,QAAS5U,EAAI,EAAGA,EAAI8U,EAAoB9U,GAAK,EAAG,CAE9C,MAAMgV,EADcJ,EAAa5U,CAAC,EACG,MAAM,GAAG,EAE9C,GAAIgV,EAAiB,CAAC,IAAMjN,EAAO,CACjC,MAAMkN,EAAeD,EAAiB,OAAQha,GAAOA,IAAO0Z,CAAI,EAE5DO,EAAa,SAAW,EAC1BL,EAAa,OAAO5U,EAAG,CAAC,EAExB4U,EAAa5U,CAAC,EAAIiV,EAAa,KAAK,GAAG,EAEzC,KACR,CACA,CAEI,KAAK,UAAYX,EAAkB,cAAcA,EAAkB,gBAAgBM,CAAY,CAAC,CACpG,CAEE,OAAO,gBAAgBtZ,EAAO,CAC5B,OAAOA,EAAM,KAAK,GAAG,CACzB,CACA,CC1GA,MAAM4Z,EAAY,CAChB,YAAY9Y,EAAS,CA2GrB+Y,GAAA,wBAAmB,CAAC,CAAE,OAAA1R,KAAa,CACjC,MAAM2R,EAASC,EAAE5R,CAAM,EACvB2R,EAAO,IAAI,KAAK,OAAO,KAAKA,EAAO,IAAG,CAAE,CAAC,CAC1C,GAEDD,GAAA,uBAAkB,CAAC,CAAE,OAAA1R,KAAa,CAChC,MAAM2R,EAASC,EAAE5R,CAAM,EACjBvI,EAAQka,EAAO,IAAK,EACpBpF,EAAW,KAAK,wBAAwBoF,CAAM,EAC9CE,EAAY,KAAK,OACjBC,EAAY,CAAC,GAAGD,CAAS,EAC/BC,EAAUvF,CAAQ,EAAI9U,EAElBA,IAAUoa,EAAUtF,CAAQ,EAC9B,KAAK,cAAc,IAAIuF,CAAS,EAEhCH,EAAO,IAAI,KAAK,OAAO,GAAG,WAAWA,EAAO,IAAG,EAAI,EAAE,CAAC,CAAC,CAE1D,GAEDD,GAAA,wBAAmB,CAAC,CAAE,OAAA1R,EAAQ,QAAA+R,KAAc,CAC1C,GAAIA,IAAY,GACd,OAEF,MAAMJ,EAASC,EAAE5R,CAAM,EACjBvI,EAAQka,EAAO,IAAK,EACpBpF,EAAW,KAAK,wBAAwBoF,CAAM,EAC9CE,EAAY,KAAK,OACjBC,EAAY,CAAC,GAAGD,CAAS,EAC/BC,EAAUvF,CAAQ,EAAI9U,EAElBA,IAAUoa,EAAUtF,CAAQ,EAC9B,KAAK,cAAc,IAAIuF,CAAS,EAEhCH,EAAO,IAAI,KAAK,OAAO,GAAG,WAAWA,EAAO,IAAG,EAAI,EAAE,CAAC,CAAC,CAE1D,GAEDD,GAAA,2BACEvO,GACG,CACH,KAAK,eAAeA,CAAM,CAC3B,GApJC,KAAK,QAAUyO,EAAEjZ,CAAO,EAExB,KAAK,UAAW,EAChB,KAAK,UAAW,EAEhB,KAAK,iBAAkB,EAEvB,KAAK,UAAW,CACpB,CAEE,eAAgB,CACd,KAAK,WAAa,KAAK,QAAQ,KAAK,uBAAuB,EAAI,QAAU,QAC7E,CAEE,WAAY,CAQV,GAPA,KAAK,IAAM,KAAK,QAAQ,KAAK,YAAY,EACzC,KAAK,IAAM,KAAK,QAAQ,KAAK,YAAY,EACzC,KAAK,iBAAmB,KAAK,QAAQ,QAAQ,kCAAkC,EAC/E,KAAK,QAAU,CAAC,KAAK,iBAAiB,KAAK,4BAA4B,EAAG,KAAK,iBAAiB,KAAK,0BAA0B,CAAC,EAEhI,KAAK,cAAe,EAEhB,KAAK,aAAe,QAAS,CAC/B,KAAM,CACJ,eAAAqZ,EACA,gBAAAC,CACD,EAAG,KAAK,QAAQ,KAAK,uBAAuB,EAE7C,KAAK,KAAOD,EACZ,KAAK,gBAAkBC,EACvB,KAAK,OAAS,KAAK,QAAQ,KAAK,eAAe,EAC/C,KAAK,aAAe,KAAK,gBAAgB,QAAQ,GAAG,IAAM,EAAI,SAAW,QAC/E,SAAe,KAAK,aAAe,SAAU,CACvC,MAAMlB,EAAO,KAAK,QAAQ,KAAK,aAAa,EAE5C,KAAK,KAAOA,EACZ,KAAK,OAAS,KAAK,QAAQ,KAAK,eAAe,EAC/C,KAAK,aAAe,QAC1B,CAES,MAAM,QAAQ,KAAK,MAAM,IAC5B,KAAK,OAAS,CAAC,KAAK,IAAK,KAAK,GAAG,EAEvC,CAEE,WAAY,CACV,KAAK,OAASH,GAAM,CAClB,KAAM,IACN,SAAU,IACV,SAAU,EACV,CAAC,KAAK,YAAY,EAChB,KAAK,eAAiB,SAAW,KAAK,KAAO,IAAI,KAAK,IAAI,EAClE,CAAK,CACL,CAEE,kBAAmB,CACjB,KAAK,cAAgBvC,GAAW,OAAO,KAAK,QAAQ,IAAI,CAAC,EAAG,CAC1D,MAAO,KAAK,OACZ,QAAS,CAAC,GAAO,GAAM,EAAK,EAC5B,MAAO,CACL,IAAK,KAAK,IACV,IAAK,KAAK,GACX,EACD,OAAQ,KAAK,MACnB,CAAK,CACL,CAEE,wBAAyB,CACvB,KAAK,eAAe,KAAK,OAAQ,EAAI,CACzC,CAEE,eAAelL,EAAQ+O,EAAc,GAAO,CAC1C,KAAK,QAAQ,QAAQ,CAAChF,EAAO3Q,IAAM,CACjC,MAAM4V,EAAMD,EAAc,KAAK,OAAO,KAAK/O,EAAO5G,CAAC,CAAC,EAAI4G,EAAO5G,CAAC,EAChEqV,EAAE1E,CAAK,EAAE,IAAIiF,CAAG,CACtB,CAAK,CACL,CAEE,WAAY,CACV,KAAK,cAAc,IAAI,MAAO,KAAK,YAAY,gBAAgB,EAC/D,KAAK,cAAc,GAAG,MAAO,KAAK,YAAY,gBAAgB,EAC9D,KAAK,cAAc,IAAI,SAAU,KAAK,mBAAmB,EACzD,KAAK,cAAc,GAAG,SAAU,KAAK,mBAAmB,EAExD,KAAK,QAAQ,QAASR,GAAW,CAC/BA,EAAO,IAAI,QAAS,KAAK,gBAAgB,EACzCA,EAAO,GAAG,QAAS,KAAK,gBAAgB,EACxCA,EAAO,IAAI,OAAQ,KAAK,eAAe,EACvCA,EAAO,GAAG,OAAQ,KAAK,eAAe,EACtCA,EAAO,GAAG,QAAS,KAAK,gBAAgB,CAC9C,CAAK,CACL,CAEE,OAAO,eAAeA,EAAQ,CAC5B,OAAOA,EAAO,KAAK,QAAQ,CAC/B,CAEE,wBAAwBA,EAAQ,CAM9B,MALuB,CACrB,aAAc,EACd,WAAY,CACb,EAEqB,KAAK,YAAY,eAAeA,CAAM,CAAC,CACjE,CA8CE,OAAO,iBACLxO,EACAtB,EACAuB,EACAhF,EACAmF,EACA6O,EACA,CACA,MAAMC,EAAiBD,EAAmB,QAAQ,OAC5CE,EAAUV,EAAEQ,EAAmB,MAAM,EACrC9N,EAAQgO,EAAQ,KAAK,cAAc,EACnCvB,EAAOuB,EAAQ,KAAK,aAAa,EACjC,CAACtB,EAAMhZ,CAAE,EAAImL,EAAO,IAAKgP,GAAQE,EAAe,KAAKF,CAAG,CAAC,EAEzDI,EAAiB,IAAI1B,EAC3B0B,EAAe,aAAc,EAC7BA,EAAe,eAAejO,EAAO,CAAE,KAAAyM,EAAM,KAAAC,EAAM,GAAAhZ,EAAI,EAEvD,MAAMwa,EAASD,EAAe,cAAe,EAC7CE,EAAW,KAAK,eAAgBD,CAAM,CAC1C,CACA,CChLA,MAAME,EAAoB,CACxB,OAAO,MAAO,CACUd,EAAE,kBAAkB,EAE5B,KAAK,CAACrV,EAAGhF,IAAO,CAEb,IAAIka,GAAYla,CAAE,CACvC,CAAK,CACL,CACA,CCRA,MAAMob,EAAQ,CACZ,aAAc,CACZ,KAAK,MAAQf,EAAE,MAAM,EACrB,KAAK,UAAW,EAChB,KAAK,aAAec,GACpB,KAAK,aAAa,KAAM,CAC5B,CAEE,WAAY,CACVD,EAAW,GAAG,qBAAsB,IAAM,CACxCA,EAAW,WAAW,WAAY,EAClC,KAAK,aAAa,KAAM,CAC9B,CAAK,EAEDA,EAAW,GAAG,eAAgB,IAAM,CAClCA,EAAW,WAAW,WAAY,CACxC,CAAK,EAED,KAAK,MAAM,GAAG,QAAS,kBAAoB3Q,GAAU,CACnDA,EAAM,eAAgB,EACtB2Q,EAAW,KAAK,eAAgBb,EAAE9P,EAAM,MAAM,EAAE,QAAQ,GAAG,EAAE,IAAI,CAAC,EAAE,IAAI,CAC9E,CAAK,EAED,KAAK,MAAM,GAAG,SAAU,gCAAiC,CAAC,CAAE,OAAA9B,KAAa,CACvEyS,EAAW,KAAK,eAAgBb,EAAE5R,CAAM,EAAE,KAAK,iBAAiB,EAAE,KAAK,MAAM,CAAC,CACpF,CAAK,EAED,KAAK,MAAM,GAAG,QAAS,+BAAiC8B,GAAU,CAChE2Q,EAAW,KAAK,eAAgB,KAAK,YAAY,eAAe3Q,CAAK,CAAC,CAC5E,CAAK,EAED,KAAK,MAAM,GAAG,SAAU,yCAA2CA,GAAU,CAC3E2Q,EAAW,KAAK,eAAgB,KAAK,YAAY,eAAe3Q,CAAK,CAAC,CAC5E,CAAK,CACL,CAEE,OAAO,eAAeA,EAAO,CAC3B,GAAIA,EAAM,OAAO,QAAQ,YAAc,OACrC,OAAOA,EAAM,OAAO,QAAQ,UAG9B,GAAI8P,EAAE9P,EAAM,MAAM,EAAE,SAAS,CAAC,EAAE,QAAQ,YAAc,OACpD,MAAM,IAAI,MAAM,0BAA0B,EAG5C,OAAO8P,EAAE9P,EAAM,MAAM,EAAE,OAAM,EAAG,CAAC,EAAE,QAAQ,SAC/C,CACA,CC/CA,SAAS8Q,GAAqB7L,EAAM,CAClC6K,EAAEa,EAAW,eAAe,QAAQ,aAAa,EAAE,YACjD1L,EAAK,eACN,EACD6K,EAAEa,EAAW,eAAe,QAAQ,mBAAmB,EAAE,YACvD1L,EAAK,uBACN,EACD6K,EAAEa,EAAW,eAAe,QAAQ,OAAO,EAAE,YAC3C1L,EAAK,qBACN,EAED,MAAM8L,EAAmBjB,EAAE7K,EAAK,iBAAiB,EAC3C+L,EAAmBlB,EAAEa,EAAW,eAAe,QAAQ,OAAO,EAEhEK,EAAiB,OAAS,EAC5BA,EAAiB,YAAa,EAAC,SAASA,EAAiB,QAAQ,KAAK,OAAO,CAAC,EAE9EA,EAAiB,YAAa,EAAC,SAASD,EAAiB,QAAQ,KAAK,OAAO,CAAC,EAGhFjB,EAAEa,EAAW,eAAe,QAAQ,IAAI,EAAE,YAAYI,CAAgB,EACtEjB,EAAEa,EAAW,eAAe,QAAQ,UAAU,EAAE,YAAY1L,EAAK,wBAAwB,EAErFA,EAAK,0BACP6K,EAAEa,EAAW,eAAe,QAAQ,UAAU,EAAE,YAAY1L,EAAK,wBAAwB,EAG3F0L,EAAW,KAAK,qBAAsB1L,CAAI,CAC5C,CAEA6K,EAAE,IAAM,CAEU,IAAIe,GAEpBF,EAAW,GAAG,oBAAsB1L,GAAS,CAC3C6L,GAAqB7L,CAAI,EACzB,OAAO,SAAS,EAAG,CAAC,CACxB,CAAG,EAED0L,EAAW,GAAG,qBAAsB,IAAM,CACxCA,EAAW,aAAa,OAAQ,CACpC,CAAG,CACH,CAAC", "x_google_ignoreList": [0, 1]}