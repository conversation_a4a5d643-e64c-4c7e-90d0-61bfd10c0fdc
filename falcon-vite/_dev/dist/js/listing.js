var At=Object.defineProperty;var Ut=(t,e,a)=>e in t?At(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a;var le=(t,e,a)=>Ut(t,typeof e!="symbol"?e+"":e,a);import M from"jquery";import{p as F}from"./prestashop-8nb8P3l5.js";function Vt(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var ee;(function(t){t.Range="range",t.Steps="steps",t.Positions="positions",t.Count="count",t.Values="values"})(ee||(ee={}));var I;(function(t){t[t.None=-1]="None",t[t.NoValue=0]="NoValue",t[t.LargeValue=1]="LargeValue",t[t.SmallValue=2]="SmallValue"})(I||(I={}));function Dt(t){return pe(t)&&typeof t.from=="function"}function pe(t){return typeof t=="object"&&typeof t.to=="function"}function Te(t){t.parentElement.removeChild(t)}function De(t){return t!=null}function We(t){t.preventDefault()}function kt(t){return t.filter(function(e){return this[e]?!1:this[e]=!0},{})}function Lt(t,e){return Math.round(t/e)*e}function Mt(t,e){var a=t.getBoundingClientRect(),o=t.ownerDocument,n=o.documentElement,p=Ze(o);return/webkit.*Chrome.*Mobile/i.test(navigator.userAgent)&&(p.x=0),e?a.top+p.y-n.clientTop:a.left+p.x-n.clientLeft}function K(t){return typeof t=="number"&&!isNaN(t)&&isFinite(t)}function Ke(t,e,a){a>0&&(z(t,e),setTimeout(function(){de(t,e)},a))}function Xe(t){return Math.max(Math.min(t,100),0)}function ve(t){return Array.isArray(t)?t:[t]}function _t(t){t=String(t);var e=t.split(".");return e.length>1?e[1].length:0}function z(t,e){t.classList&&!/\s/.test(e)?t.classList.add(e):t.className+=" "+e}function de(t,e){t.classList&&!/\s/.test(e)?t.classList.remove(e):t.className=t.className.replace(new RegExp("(^|\\b)"+e.split(" ").join("|")+"(\\b|$)","gi")," ")}function Ot(t,e){return t.classList?t.classList.contains(e):new RegExp("\\b"+e+"\\b").test(t.className)}function Ze(t){var e=window.pageXOffset!==void 0,a=(t.compatMode||"")==="CSS1Compat",o=e?window.pageXOffset:a?t.documentElement.scrollLeft:t.body.scrollLeft,n=e?window.pageYOffset:a?t.documentElement.scrollTop:t.body.scrollTop;return{x:o,y:n}}function Ft(){return window.navigator.pointerEnabled?{start:"pointerdown",move:"pointermove",end:"pointerup"}:window.navigator.msPointerEnabled?{start:"MSPointerDown",move:"MSPointerMove",end:"MSPointerUp"}:{start:"mousedown touchstart",move:"mousemove touchmove",end:"mouseup touchend"}}function jt(){var t=!1;try{var e=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("test",null,e)}catch(a){}return t}function Ht(){return window.CSS&&CSS.supports&&CSS.supports("touch-action","none")}function Le(t,e){return 100/(e-t)}function ke(t,e,a){return e*100/(t[a+1]-t[a])}function Rt(t,e){return ke(t,t[0]<0?e+Math.abs(t[0]):e-t[0],0)}function $t(t,e){return e*(t[1]-t[0])/100+t[0]}function fe(t,e){for(var a=1;t>=e[a];)a+=1;return a}function zt(t,e,a){if(a>=t.slice(-1)[0])return 100;var o=fe(a,t),n=t[o-1],p=t[o],h=e[o-1],x=e[o];return h+Rt([n,p],a)/Le(h,x)}function It(t,e,a){if(a>=100)return t.slice(-1)[0];var o=fe(a,e),n=t[o-1],p=t[o],h=e[o-1],x=e[o];return $t([n,p],(a-h)*Le(h,x))}function Nt(t,e,a,o){if(o===100)return o;var n=fe(o,t),p=t[n-1],h=t[n];return a?o-p>(h-p)/2?h:p:e[n-1]?t[n-1]+Lt(o-t[n-1],e[n-1]):o}var Qe=function(){function t(e,a,o){this.xPct=[],this.xVal=[],this.xSteps=[],this.xNumSteps=[],this.xHighestCompleteStep=[],this.xSteps=[o||!1],this.xNumSteps=[!1],this.snap=a;var n,p=[];for(Object.keys(e).forEach(function(h){p.push([ve(e[h]),h])}),p.sort(function(h,x){return h[0][0]-x[0][0]}),n=0;n<p.length;n++)this.handleEntryPoint(p[n][1],p[n][0]);for(this.xNumSteps=this.xSteps.slice(0),n=0;n<this.xNumSteps.length;n++)this.handleStepPoint(n,this.xNumSteps[n])}return t.prototype.getDistance=function(e){for(var a=[],o=0;o<this.xNumSteps.length-1;o++)a[o]=ke(this.xVal,e,o);return a},t.prototype.getAbsoluteDistance=function(e,a,o){var n=0;if(e<this.xPct[this.xPct.length-1])for(;e>this.xPct[n+1];)n++;else e===this.xPct[this.xPct.length-1]&&(n=this.xPct.length-2);!o&&e===this.xPct[n+1]&&n++,a===null&&(a=[]);var p,h=1,x=a[n],C=0,S=0,V=0,_=0;for(o?p=(e-this.xPct[n])/(this.xPct[n+1]-this.xPct[n]):p=(this.xPct[n+1]-e)/(this.xPct[n+1]-this.xPct[n]);x>0;)C=this.xPct[n+1+_]-this.xPct[n+_],a[n+_]*h+100-p*100>100?(S=C*p,h=(x-100*p)/a[n+_],p=1):(S=a[n+_]*C/100*h,h=0),o?(V=V-S,this.xPct.length+_>=1&&_--):(V=V+S,this.xPct.length-_>=1&&_++),x=a[n+_]*h;return e+V},t.prototype.toStepping=function(e){return e=zt(this.xVal,this.xPct,e),e},t.prototype.fromStepping=function(e){return It(this.xVal,this.xPct,e)},t.prototype.getStep=function(e){return e=Nt(this.xPct,this.xSteps,this.snap,e),e},t.prototype.getDefaultStep=function(e,a,o){var n=fe(e,this.xPct);return(e===100||a&&e===this.xPct[n-1])&&(n=Math.max(n-1,1)),(this.xVal[n]-this.xVal[n-1])/o},t.prototype.getNearbySteps=function(e){var a=fe(e,this.xPct);return{stepBefore:{startValue:this.xVal[a-2],step:this.xNumSteps[a-2],highestStep:this.xHighestCompleteStep[a-2]},thisStep:{startValue:this.xVal[a-1],step:this.xNumSteps[a-1],highestStep:this.xHighestCompleteStep[a-1]},stepAfter:{startValue:this.xVal[a],step:this.xNumSteps[a],highestStep:this.xHighestCompleteStep[a]}}},t.prototype.countStepDecimals=function(){var e=this.xNumSteps.map(_t);return Math.max.apply(null,e)},t.prototype.hasNoSize=function(){return this.xVal[0]===this.xVal[this.xVal.length-1]},t.prototype.convert=function(e){return this.getStep(this.toStepping(e))},t.prototype.handleEntryPoint=function(e,a){var o;if(e==="min"?o=0:e==="max"?o=100:o=parseFloat(e),!K(o)||!K(a[0]))throw new Error("noUiSlider: 'range' value isn't numeric.");this.xPct.push(o),this.xVal.push(a[0]);var n=Number(a[1]);o?this.xSteps.push(isNaN(n)?!1:n):isNaN(n)||(this.xSteps[0]=n),this.xHighestCompleteStep.push(0)},t.prototype.handleStepPoint=function(e,a){if(a){if(this.xVal[e]===this.xVal[e+1]){this.xSteps[e]=this.xHighestCompleteStep[e]=this.xVal[e];return}this.xSteps[e]=ke([this.xVal[e],this.xVal[e+1]],a,0)/Le(this.xPct[e],this.xPct[e+1]);var o=(this.xVal[e+1]-this.xVal[e])/this.xNumSteps[e],n=Math.ceil(Number(o.toFixed(3))-1),p=this.xVal[e]+this.xNumSteps[e]*n;this.xHighestCompleteStep[e]=p}},t}(),Ye={to:function(t){return t===void 0?"":t.toFixed(2)},from:Number},et={target:"target",base:"base",origin:"origin",handle:"handle",handleLower:"handle-lower",handleUpper:"handle-upper",touchArea:"touch-area",horizontal:"horizontal",vertical:"vertical",background:"background",connect:"connect",connects:"connects",ltr:"ltr",rtl:"rtl",textDirectionLtr:"txt-dir-ltr",textDirectionRtl:"txt-dir-rtl",draggable:"draggable",drag:"state-drag",tap:"state-tap",active:"active",tooltip:"tooltip",pips:"pips",pipsHorizontal:"pips-horizontal",pipsVertical:"pips-vertical",marker:"marker",markerHorizontal:"marker-horizontal",markerVertical:"marker-vertical",markerNormal:"marker-normal",markerLarge:"marker-large",markerSub:"marker-sub",value:"value",valueHorizontal:"value-horizontal",valueVertical:"value-vertical",valueNormal:"value-normal",valueLarge:"value-large",valueSub:"value-sub"},Q={tooltips:".__tooltips",aria:".__aria"};function Bt(t,e){if(!K(e))throw new Error("noUiSlider: 'step' is not numeric.");t.singleStep=e}function Gt(t,e){if(!K(e))throw new Error("noUiSlider: 'keyboardPageMultiplier' is not numeric.");t.keyboardPageMultiplier=e}function qt(t,e){if(!K(e))throw new Error("noUiSlider: 'keyboardMultiplier' is not numeric.");t.keyboardMultiplier=e}function Tt(t,e){if(!K(e))throw new Error("noUiSlider: 'keyboardDefaultStep' is not numeric.");t.keyboardDefaultStep=e}function Wt(t,e){if(typeof e!="object"||Array.isArray(e))throw new Error("noUiSlider: 'range' is not an object.");if(e.min===void 0||e.max===void 0)throw new Error("noUiSlider: Missing 'min' or 'max' in 'range'.");t.spectrum=new Qe(e,t.snap||!1,t.singleStep)}function Kt(t,e){if(e=ve(e),!Array.isArray(e)||!e.length)throw new Error("noUiSlider: 'start' option is incorrect.");t.handles=e.length,t.start=e}function Xt(t,e){if(typeof e!="boolean")throw new Error("noUiSlider: 'snap' option must be a boolean.");t.snap=e}function Yt(t,e){if(typeof e!="boolean")throw new Error("noUiSlider: 'animate' option must be a boolean.");t.animate=e}function Jt(t,e){if(typeof e!="number")throw new Error("noUiSlider: 'animationDuration' option must be a number.");t.animationDuration=e}function tt(t,e){var a=[!1],o;if(e==="lower"?e=[!0,!1]:e==="upper"&&(e=[!1,!0]),e===!0||e===!1){for(o=1;o<t.handles;o++)a.push(e);a.push(!1)}else{if(!Array.isArray(e)||!e.length||e.length!==t.handles+1)throw new Error("noUiSlider: 'connect' option doesn't match handle count.");a=e}t.connect=a}function Zt(t,e){switch(e){case"horizontal":t.ort=0;break;case"vertical":t.ort=1;break;default:throw new Error("noUiSlider: 'orientation' option is invalid.")}}function rt(t,e){if(!K(e))throw new Error("noUiSlider: 'margin' option must be numeric.");e!==0&&(t.margin=t.spectrum.getDistance(e))}function Qt(t,e){if(!K(e))throw new Error("noUiSlider: 'limit' option must be numeric.");if(t.limit=t.spectrum.getDistance(e),!t.limit||t.handles<2)throw new Error("noUiSlider: 'limit' option is only supported on linear sliders with 2 or more handles.")}function er(t,e){var a;if(!K(e)&&!Array.isArray(e))throw new Error("noUiSlider: 'padding' option must be numeric or array of exactly 2 numbers.");if(Array.isArray(e)&&!(e.length===2||K(e[0])||K(e[1])))throw new Error("noUiSlider: 'padding' option must be numeric or array of exactly 2 numbers.");if(e!==0){for(Array.isArray(e)||(e=[e,e]),t.padding=[t.spectrum.getDistance(e[0]),t.spectrum.getDistance(e[1])],a=0;a<t.spectrum.xNumSteps.length-1;a++)if(t.padding[0][a]<0||t.padding[1][a]<0)throw new Error("noUiSlider: 'padding' option must be a positive number(s).");var o=e[0]+e[1],n=t.spectrum.xVal[0],p=t.spectrum.xVal[t.spectrum.xVal.length-1];if(o/(p-n)>1)throw new Error("noUiSlider: 'padding' option must not exceed 100% of the range.")}}function tr(t,e){switch(e){case"ltr":t.dir=0;break;case"rtl":t.dir=1;break;default:throw new Error("noUiSlider: 'direction' option was not recognized.")}}function rr(t,e){if(typeof e!="string")throw new Error("noUiSlider: 'behaviour' must be a string containing options.");var a=e.indexOf("tap")>=0,o=e.indexOf("drag")>=0,n=e.indexOf("fixed")>=0,p=e.indexOf("snap")>=0,h=e.indexOf("hover")>=0,x=e.indexOf("unconstrained")>=0,C=e.indexOf("invert-connects")>=0,S=e.indexOf("drag-all")>=0,V=e.indexOf("smooth-steps")>=0;if(n){if(t.handles!==2)throw new Error("noUiSlider: 'fixed' behaviour must be used with 2 handles");rt(t,t.start[1]-t.start[0])}if(C&&t.handles!==2)throw new Error("noUiSlider: 'invert-connects' behaviour must be used with 2 handles");if(x&&(t.margin||t.limit))throw new Error("noUiSlider: 'unconstrained' behaviour cannot be used with margin or limit");t.events={tap:a||p,drag:o,dragAll:S,smoothSteps:V,fixed:n,snap:p,hover:h,unconstrained:x,invertConnects:C}}function ir(t,e){if(e!==!1)if(e===!0||pe(e)){t.tooltips=[];for(var a=0;a<t.handles;a++)t.tooltips.push(e)}else{if(e=ve(e),e.length!==t.handles)throw new Error("noUiSlider: must pass a formatter for all handles.");e.forEach(function(o){if(typeof o!="boolean"&&!pe(o))throw new Error("noUiSlider: 'tooltips' must be passed a formatter or 'false'.")}),t.tooltips=e}}function sr(t,e){if(e.length!==t.handles)throw new Error("noUiSlider: must pass a attributes for all handles.");t.handleAttributes=e}function ar(t,e){if(!pe(e))throw new Error("noUiSlider: 'ariaFormat' requires 'to' method.");t.ariaFormat=e}function nr(t,e){if(!Dt(e))throw new Error("noUiSlider: 'format' requires 'to' and 'from' methods.");t.format=e}function or(t,e){if(typeof e!="boolean")throw new Error("noUiSlider: 'keyboardSupport' option must be a boolean.");t.keyboardSupport=e}function lr(t,e){t.documentElement=e}function fr(t,e){if(typeof e!="string"&&e!==!1)throw new Error("noUiSlider: 'cssPrefix' must be a string or `false`.");t.cssPrefix=e}function cr(t,e){if(typeof e!="object")throw new Error("noUiSlider: 'cssClasses' must be an object.");typeof t.cssPrefix=="string"?(t.cssClasses={},Object.keys(e).forEach(function(a){t.cssClasses[a]=t.cssPrefix+e[a]})):t.cssClasses=e}function it(t){var e={margin:null,limit:null,padding:null,animate:!0,animationDuration:300,ariaFormat:Ye,format:Ye},a={step:{r:!1,t:Bt},keyboardPageMultiplier:{r:!1,t:Gt},keyboardMultiplier:{r:!1,t:qt},keyboardDefaultStep:{r:!1,t:Tt},start:{r:!0,t:Kt},connect:{r:!0,t:tt},direction:{r:!0,t:tr},snap:{r:!1,t:Xt},animate:{r:!1,t:Yt},animationDuration:{r:!1,t:Jt},range:{r:!0,t:Wt},orientation:{r:!1,t:Zt},margin:{r:!1,t:rt},limit:{r:!1,t:Qt},padding:{r:!1,t:er},behaviour:{r:!0,t:rr},ariaFormat:{r:!1,t:ar},format:{r:!1,t:nr},tooltips:{r:!1,t:ir},keyboardSupport:{r:!0,t:or},documentElement:{r:!1,t:lr},cssPrefix:{r:!0,t:fr},cssClasses:{r:!0,t:cr},handleAttributes:{r:!1,t:sr}},o={connect:!1,direction:"ltr",behaviour:"tap",orientation:"horizontal",keyboardSupport:!0,cssPrefix:"noUi-",cssClasses:et,keyboardPageMultiplier:5,keyboardMultiplier:1,keyboardDefaultStep:10};t.format&&!t.ariaFormat&&(t.ariaFormat=t.format),Object.keys(a).forEach(function(C){if(!De(t[C])&&o[C]===void 0){if(a[C].r)throw new Error("noUiSlider: '"+C+"' is required.");return}a[C].t(e,De(t[C])?t[C]:o[C])}),e.pips=t.pips;var n=document.createElement("div"),p=n.style.msTransform!==void 0,h=n.style.transform!==void 0;e.transformRule=h?"transform":p?"msTransform":"webkitTransform";var x=[["left","top"],["right","bottom"]];return e.style=x[e.dir][e.ort],e}function ur(t,e,a){var o=Ft(),n=Ht(),p=n&&jt(),h=t,x,C,S,V,_,N,w=e.spectrum,v=[],d=[],g=[],U=0,E={},Y=!1,X=t.ownerDocument,T=e.documentElement||X.documentElement,W=X.body,ie=X.dir==="rtl"||e.ort===1?0:100;function B(r,i){var s=X.createElement("div");return i&&z(s,i),r.appendChild(s),s}function b(r,i){var s=B(r,e.cssClasses.origin),l=B(s,e.cssClasses.handle);if(B(l,e.cssClasses.touchArea),l.setAttribute("data-handle",String(i)),e.keyboardSupport&&(l.setAttribute("tabindex","0"),l.addEventListener("keydown",function(c){return mt(c,i)})),e.handleAttributes!==void 0){var f=e.handleAttributes[i];Object.keys(f).forEach(function(c){l.setAttribute(c,f[c])})}return l.setAttribute("role","slider"),l.setAttribute("aria-orientation",e.ort?"vertical":"horizontal"),i===0?z(l,e.cssClasses.handleLower):i===e.handles-1&&z(l,e.cssClasses.handleUpper),s.handle=l,s}function J(r,i){return i?B(r,e.cssClasses.connect):!1}function H(r,i){C=B(i,e.cssClasses.connects),S=[],V=[],V.push(J(C,r[0]));for(var s=0;s<e.handles;s++)S.push(b(i,s)),g[s]=s,V.push(J(C,r[s+1]))}function se(r){z(r,e.cssClasses.target),e.dir===0?z(r,e.cssClasses.ltr):z(r,e.cssClasses.rtl),e.ort===0?z(r,e.cssClasses.horizontal):z(r,e.cssClasses.vertical);var i=getComputedStyle(r).direction;return i==="rtl"?z(r,e.cssClasses.textDirectionRtl):z(r,e.cssClasses.textDirectionLtr),B(r,e.cssClasses.base)}function Z(r,i){return!e.tooltips||!e.tooltips[i]?!1:B(r.firstChild,e.cssClasses.tooltip)}function ae(){return h.hasAttribute("disabled")}function G(r){var i=S[r];return i.hasAttribute("disabled")}function st(r){r!=null?(S[r].setAttribute("disabled",""),S[r].handle.removeAttribute("tabindex")):(h.setAttribute("disabled",""),S.forEach(function(i){i.handle.removeAttribute("tabindex")}))}function at(r){r!=null?(S[r].removeAttribute("disabled"),S[r].handle.setAttribute("tabindex","0")):(h.removeAttribute("disabled"),S.forEach(function(i){i.removeAttribute("disabled"),i.handle.setAttribute("tabindex","0")}))}function me(){N&&(ne("update"+Q.tooltips),N.forEach(function(r){r&&Te(r)}),N=null)}function Me(){me(),N=S.map(Z),xe("update"+Q.tooltips,function(r,i,s){if(!(!N||!e.tooltips)&&N[i]!==!1){var l=r[i];e.tooltips[i]!==!0&&(l=e.tooltips[i].to(s[i])),N[i].innerHTML=l}})}function nt(){ne("update"+Q.aria),xe("update"+Q.aria,function(r,i,s,l,f){g.forEach(function(c){var m=S[c],u=ce(d,c,0,!0,!0,!0),y=ce(d,c,100,!0,!0,!0),A=f[c],D=String(e.ariaFormat.to(s[c]));u=w.fromStepping(u).toFixed(1),y=w.fromStepping(y).toFixed(1),A=w.fromStepping(A).toFixed(1),m.children[0].setAttribute("aria-valuemin",u),m.children[0].setAttribute("aria-valuemax",y),m.children[0].setAttribute("aria-valuenow",A),m.children[0].setAttribute("aria-valuetext",D)})})}function ot(r){if(r.mode===ee.Range||r.mode===ee.Steps)return w.xVal;if(r.mode===ee.Count){if(r.values<2)throw new Error("noUiSlider: 'values' (>= 2) required for mode 'count'.");for(var i=r.values-1,s=100/i,l=[];i--;)l[i]=i*s;return l.push(100),_e(l,r.stepped)}return r.mode===ee.Positions?_e(r.values,r.stepped):r.mode===ee.Values?r.stepped?r.values.map(function(f){return w.fromStepping(w.getStep(w.toStepping(f)))}):r.values:[]}function _e(r,i){return r.map(function(s){return w.fromStepping(i?w.getStep(s):s)})}function lt(r){function i(A,D){return Number((A+D).toFixed(7))}var s=ot(r),l={},f=w.xVal[0],c=w.xVal[w.xVal.length-1],m=!1,u=!1,y=0;return s=kt(s.slice().sort(function(A,D){return A-D})),s[0]!==f&&(s.unshift(f),m=!0),s[s.length-1]!==c&&(s.push(c),u=!0),s.forEach(function(A,D){var k,P,O,$=A,j=s[D+1],R,Pe,ye,Ae,Be,Ue,Ge,qe=r.mode===ee.Steps;for(qe&&(k=w.xNumSteps[D]),k||(k=j-$),j===void 0&&(j=$),k=Math.max(k,1e-7),P=$;P<=j;P=i(P,k)){for(R=w.toStepping(P),Pe=R-y,Be=Pe/(r.density||1),Ue=Math.round(Be),Ge=Pe/Ue,O=1;O<=Ue;O+=1)ye=y+O*Ge,l[ye.toFixed(5)]=[w.fromStepping(ye),0];Ae=s.indexOf(P)>-1?I.LargeValue:qe?I.SmallValue:I.NoValue,!D&&m&&P!==j&&(Ae=0),P===j&&u||(l[R.toFixed(5)]=[P,Ae]),y=R}}),l}function ft(r,i,s){var l,f,c=X.createElement("div"),m=(l={},l[I.None]="",l[I.NoValue]=e.cssClasses.valueNormal,l[I.LargeValue]=e.cssClasses.valueLarge,l[I.SmallValue]=e.cssClasses.valueSub,l),u=(f={},f[I.None]="",f[I.NoValue]=e.cssClasses.markerNormal,f[I.LargeValue]=e.cssClasses.markerLarge,f[I.SmallValue]=e.cssClasses.markerSub,f),y=[e.cssClasses.valueHorizontal,e.cssClasses.valueVertical],A=[e.cssClasses.markerHorizontal,e.cssClasses.markerVertical];z(c,e.cssClasses.pips),z(c,e.ort===0?e.cssClasses.pipsHorizontal:e.cssClasses.pipsVertical);function D(P,O){var $=O===e.cssClasses.value,j=$?y:A,R=$?m:u;return O+" "+j[e.ort]+" "+R[P]}function k(P,O,$){if($=i?i(O,$):$,$!==I.None){var j=B(c,!1);j.className=D($,e.cssClasses.marker),j.style[e.style]=P+"%",$>I.NoValue&&(j=B(c,!1),j.className=D($,e.cssClasses.value),j.setAttribute("data-value",String(O)),j.style[e.style]=P+"%",j.innerHTML=String(s.to(O)))}}return Object.keys(r).forEach(function(P){k(P,r[P][0],r[P][1])}),c}function ge(){_&&(Te(_),_=null)}function Se(r){ge();var i=lt(r),s=r.filter,l=r.format||{to:function(f){return String(Math.round(f))}};return _=h.appendChild(ft(i,s,l)),_}function Oe(){var r=x.getBoundingClientRect(),i="offset"+["Width","Height"][e.ort];return e.ort===0?r.width||x[i]:r.height||x[i]}function te(r,i,s,l){var f=function(m){var u=ct(m,l.pageOffset,l.target||i);if(!u||ae()&&!l.doNotReject||Ot(h,e.cssClasses.tap)&&!l.doNotReject||r===o.start&&u.buttons!==void 0&&u.buttons>1||l.hover&&u.buttons)return!1;p||u.preventDefault(),u.calcPoint=u.points[e.ort],s(u,l)},c=[];return r.split(" ").forEach(function(m){i.addEventListener(m,f,p?{passive:!0}:!1),c.push([m,f])}),c}function ct(r,i,s){var l=r.type.indexOf("touch")===0,f=r.type.indexOf("mouse")===0,c=r.type.indexOf("pointer")===0,m=0,u=0;if(r.type.indexOf("MSPointer")===0&&(c=!0),r.type==="mousedown"&&!r.buttons&&!r.touches)return!1;if(l){var y=function(k){var P=k.target;return P===s||s.contains(P)||r.composed&&r.composedPath().shift()===s};if(r.type==="touchstart"){var A=Array.prototype.filter.call(r.touches,y);if(A.length>1)return!1;m=A[0].pageX,u=A[0].pageY}else{var D=Array.prototype.find.call(r.changedTouches,y);if(!D)return!1;m=D.pageX,u=D.pageY}}return i=i||Ze(X),(f||c)&&(m=r.clientX+i.x,u=r.clientY+i.y),r.pageOffset=i,r.points=[m,u],r.cursor=f||c,r}function Fe(r){var i=r-Mt(x,e.ort),s=i*100/Oe();return s=Xe(s),e.dir?100-s:s}function ut(r){var i=100,s=!1;return S.forEach(function(l,f){if(!G(f)){var c=d[f],m=Math.abs(c-r),u=m===100&&i===100,y=m<i,A=m<=i&&r>c;(y||A||u)&&(s=f,i=m)}}),s}function ht(r,i){r.type==="mouseout"&&r.target.nodeName==="HTML"&&r.relatedTarget===null&&we(r,i)}function dt(r,i){if(navigator.appVersion.indexOf("MSIE 9")===-1&&r.buttons===0&&i.buttonsProperty!==0)return we(r,i);var s=(e.dir?-1:1)*(r.calcPoint-i.startCalcPoint),l=s*100/i.baseSize;He(s>0,l,i.locations,i.handleNumbers,i.connect)}function we(r,i){i.handle&&(de(i.handle,e.cssClasses.active),U-=1),i.listeners.forEach(function(s){T.removeEventListener(s[0],s[1])}),U===0&&(de(h,e.cssClasses.drag),Ce(),r.cursor&&(W.style.cursor="",W.removeEventListener("selectstart",We))),e.events.smoothSteps&&(i.handleNumbers.forEach(function(s){re(s,d[s],!0,!0,!1,!1)}),i.handleNumbers.forEach(function(s){L("update",s)})),i.handleNumbers.forEach(function(s){L("change",s),L("set",s),L("end",s)})}function be(r,i){if(!i.handleNumbers.some(G)){var s;if(i.handleNumbers.length===1){var l=S[i.handleNumbers[0]];s=l.children[0],U+=1,z(s,e.cssClasses.active)}r.stopPropagation();var f=[],c=te(o.move,T,dt,{target:r.target,handle:s,connect:i.connect,listeners:f,startCalcPoint:r.calcPoint,baseSize:Oe(),pageOffset:r.pageOffset,handleNumbers:i.handleNumbers,buttonsProperty:r.buttons,locations:d.slice()}),m=te(o.end,T,we,{target:r.target,handle:s,listeners:f,doNotReject:!0,handleNumbers:i.handleNumbers}),u=te("mouseout",T,ht,{target:r.target,handle:s,listeners:f,doNotReject:!0,handleNumbers:i.handleNumbers});f.push.apply(f,c.concat(m,u)),r.cursor&&(W.style.cursor=getComputedStyle(r.target).cursor,S.length>1&&z(h,e.cssClasses.drag),W.addEventListener("selectstart",We,!1)),i.handleNumbers.forEach(function(y){L("start",y)})}}function pt(r){r.stopPropagation();var i=Fe(r.calcPoint),s=ut(i);s!==!1&&(e.events.snap||Ke(h,e.cssClasses.tap,e.animationDuration),re(s,i,!0,!0),Ce(),L("slide",s,!0),L("update",s,!0),e.events.snap?be(r,{handleNumbers:[s]}):(L("change",s,!0),L("set",s,!0)))}function vt(r){var i=Fe(r.calcPoint),s=w.getStep(i),l=w.fromStepping(s);Object.keys(E).forEach(function(f){f.split(".")[0]==="hover"&&E[f].forEach(function(c){c.call(he,l)})})}function mt(r,i){if(ae()||G(i))return!1;var s=["Left","Right"],l=["Down","Up"],f=["PageDown","PageUp"],c=["Home","End"];e.dir&&!e.ort?s.reverse():e.ort&&!e.dir&&(l.reverse(),f.reverse());var m=r.key.replace("Arrow",""),u=m===f[0],y=m===f[1],A=m===l[0]||m===s[0]||u,D=m===l[1]||m===s[1]||y,k=m===c[0],P=m===c[1];if(!A&&!D&&!k&&!P)return!0;r.preventDefault();var O;if(D||A){var $=A?0:1,j=Ie(i),R=j[$];if(R===null)return!1;R===!1&&(R=w.getDefaultStep(d[i],A,e.keyboardDefaultStep)),y||u?R*=e.keyboardPageMultiplier:R*=e.keyboardMultiplier,R=Math.max(R,1e-7),R=(A?-1:1)*R,O=v[i]+R}else P?O=e.spectrum.xVal[e.spectrum.xVal.length-1]:O=e.spectrum.xVal[0];return re(i,w.toStepping(O),!0,!0),L("slide",i),L("update",i),L("change",i),L("set",i),!1}function je(r){r.fixed||S.forEach(function(i,s){te(o.start,i.children[0],be,{handleNumbers:[s]})}),r.tap&&te(o.start,x,pt,{}),r.hover&&te(o.move,x,vt,{hover:!0}),r.drag&&V.forEach(function(i,s){if(!(i===!1||s===0||s===V.length-1)){var l=S[s-1],f=S[s],c=[i],m=[l,f],u=[s-1,s];z(i,e.cssClasses.draggable),r.fixed&&(c.push(l.children[0]),c.push(f.children[0])),r.dragAll&&(m=S,u=g),c.forEach(function(y){te(o.start,y,be,{handles:m,handleNumbers:u,connect:i})})}})}function xe(r,i){E[r]=E[r]||[],E[r].push(i),r.split(".")[0]==="update"&&S.forEach(function(s,l){L("update",l)})}function gt(r){return r===Q.aria||r===Q.tooltips}function ne(r){var i=r&&r.split(".")[0],s=i?r.substring(i.length):r;Object.keys(E).forEach(function(l){var f=l.split(".")[0],c=l.substring(f.length);(!i||i===f)&&(!s||s===c)&&(!gt(c)||s===c)&&delete E[l]})}function L(r,i,s){Object.keys(E).forEach(function(l){var f=l.split(".")[0];r===f&&E[l].forEach(function(c){c.call(he,v.map(e.format.to),i,v.slice(),s||!1,d.slice(),he)})})}function ce(r,i,s,l,f,c,m){var u;return S.length>1&&!e.events.unconstrained&&(l&&i>0&&(u=w.getAbsoluteDistance(r[i-1],e.margin,!1),s=Math.max(s,u)),f&&i<S.length-1&&(u=w.getAbsoluteDistance(r[i+1],e.margin,!0),s=Math.min(s,u))),S.length>1&&e.limit&&(l&&i>0&&(u=w.getAbsoluteDistance(r[i-1],e.limit,!1),s=Math.min(s,u)),f&&i<S.length-1&&(u=w.getAbsoluteDistance(r[i+1],e.limit,!0),s=Math.max(s,u))),e.padding&&(i===0&&(u=w.getAbsoluteDistance(0,e.padding[0],!1),s=Math.max(s,u)),i===S.length-1&&(u=w.getAbsoluteDistance(100,e.padding[1],!0),s=Math.min(s,u))),m||(s=w.getStep(s)),s=Xe(s),s===r[i]&&!c?!1:s}function Ee(r,i){var s=e.ort;return(s?i:r)+", "+(s?r:i)}function He(r,i,s,l,f){var c=s.slice(),m=l[0],u=e.events.smoothSteps,y=[!r,r],A=[r,!r];l=l.slice(),r&&l.reverse(),l.length>1?l.forEach(function(k,P){var O=ce(c,k,c[k]+i,y[P],A[P],!1,u);O===!1?i=0:(i=O-c[k],c[k]=O)}):y=A=[!0];var D=!1;l.forEach(function(k,P){D=re(k,s[k]+i,y[P],A[P],!1,u)||D}),D&&(l.forEach(function(k){L("update",k),L("slide",k)}),f!=null&&L("drag",m))}function Re(r,i){return e.dir?100-r-i:r}function St(r,i){d[r]=i,v[r]=w.fromStepping(i);var s=Re(i,0)-ie,l="translate("+Ee(s+"%","0")+")";if(S[r].style[e.transformRule]=l,e.events.invertConnects&&d.length>1){var f=d.every(function(c,m,u){return m===0||c>=u[m-1]});if(Y!==!f){Pt();return}}oe(r),oe(r+1),Y&&(oe(r-1),oe(r+2))}function Ce(){g.forEach(function(r){var i=d[r]>50?-1:1,s=3+(S.length+i*r);S[r].style.zIndex=String(s)})}function re(r,i,s,l,f,c){return f||(i=ce(d,r,i,s,l,!1,c)),i===!1?!1:(St(r,i),!0)}function oe(r){if(V[r]){var i=d.slice();Y&&i.sort(function(u,y){return u-y});var s=0,l=100;r!==0&&(s=i[r-1]),r!==V.length-1&&(l=i[r]);var f=l-s,c="translate("+Ee(Re(s,f)+"%","0")+")",m="scale("+Ee(f/100,"1")+")";V[r].style[e.transformRule]=c+" "+m}}function $e(r,i){return r===null||r===!1||r===void 0||(typeof r=="number"&&(r=String(r)),r=e.format.from(r),r!==!1&&(r=w.toStepping(r)),r===!1||isNaN(r))?d[i]:r}function ue(r,i,s){var l=ve(r),f=d[0]===void 0;i=i===void 0?!0:i,e.animate&&!f&&Ke(h,e.cssClasses.tap,e.animationDuration),g.forEach(function(u){re(u,$e(l[u],u),!0,!1,s)});var c=g.length===1?0:1;if(f&&w.hasNoSize()&&(s=!0,d[0]=0,g.length>1)){var m=100/(g.length-1);g.forEach(function(u){d[u]=u*m})}for(;c<g.length;++c)g.forEach(function(u){re(u,d[u],!0,!0,s)});Ce(),g.forEach(function(u){L("update",u),l[u]!==null&&i&&L("set",u)})}function wt(r){ue(e.start,r)}function bt(r,i,s,l){if(r=Number(r),!(r>=0&&r<g.length))throw new Error("noUiSlider: invalid handle number, got: "+r);re(r,$e(i,r),!0,!0,l),L("update",r),s&&L("set",r)}function ze(r){if(r===void 0&&(r=!1),r)return v.length===1?v[0]:v.slice(0);var i=v.map(e.format.to);return i.length===1?i[0]:i}function xt(){for(ne(Q.aria),ne(Q.tooltips),Object.keys(e.cssClasses).forEach(function(r){de(h,e.cssClasses[r])});h.firstChild;)h.removeChild(h.firstChild);delete h.noUiSlider}function Ie(r){var i=d[r],s=w.getNearbySteps(i),l=v[r],f=s.thisStep.step,c=null;if(e.snap)return[l-s.stepBefore.startValue||null,s.stepAfter.startValue-l||null];f!==!1&&l+f>s.stepAfter.startValue&&(f=s.stepAfter.startValue-l),l>s.thisStep.startValue?c=s.thisStep.step:s.stepBefore.step===!1?c=!1:c=l-s.stepBefore.highestStep,i===100?f=null:i===0&&(c=null);var m=w.countStepDecimals();return f!==null&&f!==!1&&(f=Number(f.toFixed(m))),c!==null&&c!==!1&&(c=Number(c.toFixed(m))),[c,f]}function Et(){return g.map(Ie)}function Ct(r,i){var s=ze(),l=["margin","limit","padding","range","animate","snap","step","format","pips","tooltips","connect"];l.forEach(function(c){r[c]!==void 0&&(a[c]=r[c])});var f=it(a);l.forEach(function(c){r[c]!==void 0&&(e[c]=f[c])}),w=f.spectrum,e.margin=f.margin,e.limit=f.limit,e.padding=f.padding,e.pips?Se(e.pips):ge(),e.tooltips?Me():me(),d=[],ue(De(r.start)?r.start:s,i),r.connect&&Ne()}function Ne(){for(;C.firstChild;)C.removeChild(C.firstChild);for(var r=0;r<=e.handles;r++)V[r]=J(C,e.connect[r]),oe(r);je({drag:e.events.drag,fixed:!0})}function Pt(){Y=!Y,tt(e,e.connect.map(function(r){return!r})),Ne()}function yt(){x=se(h),H(e.connect,x),je(e.events),ue(e.start),e.pips&&Se(e.pips),e.tooltips&&Me(),nt()}yt();var he={destroy:xt,steps:Et,on:xe,off:ne,get:ze,set:ue,setHandle:bt,reset:wt,disable:st,enable:at,__moveHandles:function(r,i,s){He(r,i,d,s)},options:a,updateOptions:Ct,target:h,removePips:ge,removeTooltips:me,getPositions:function(){return d.slice()},getTooltips:function(){return N},getOrigins:function(){return S},pips:Se};return he}function hr(t,e){if(!t||!t.nodeName)throw new Error("noUiSlider: create requires a single element, got: "+t);if(t.noUiSlider)throw new Error("noUiSlider: Slider was already initialized.");var a=it(e),o=ur(t,a,e);return t.noUiSlider=o,o}const dr={__spectrum:Qe,cssClasses:et,create:hr};var Ve={exports:{}},Je;function pr(){return Je||(Je=1,function(t,e){(function(a){t.exports=a()})(function(){var a=["decimals","thousand","mark","prefix","suffix","encoder","decoder","negativeBefore","negative","edit","undo"];function o(v){return v.split("").reverse().join("")}function n(v,d){return v.substring(0,d.length)===d}function p(v,d){return v.slice(-1*d.length)===d}function h(v,d,g){if((v[d]||v[g])&&v[d]===v[g])throw new Error(d)}function x(v){return typeof v=="number"&&isFinite(v)}function C(v,d){return v=v.toString().split("e"),v=Math.round(+(v[0]+"e"+(v[1]?+v[1]+d:d))),v=v.toString().split("e"),(+(v[0]+"e"+(v[1]?+v[1]-d:-d))).toFixed(d)}function S(v,d,g,U,E,Y,X,T,W,ie,B,b){var J=b,H,se,Z,ae="",G="";return Y&&(b=Y(b)),x(b)?(v!==!1&&parseFloat(b.toFixed(v))===0&&(b=0),b<0&&(H=!0,b=Math.abs(b)),v!==!1&&(b=C(b,v)),b=b.toString(),b.indexOf(".")!==-1?(se=b.split("."),Z=se[0],g&&(ae=g+se[1])):Z=b,d&&(Z=o(Z).match(/.{1,3}/g),Z=o(Z.join(o(d)))),H&&T&&(G+=T),U&&(G+=U),H&&W&&(G+=W),G+=Z,G+=ae,E&&(G+=E),ie&&(G=ie(G,J)),G):!1}function V(v,d,g,U,E,Y,X,T,W,ie,B,b){var J,H="";return B&&(b=B(b)),!b||typeof b!="string"||(T&&n(b,T)&&(b=b.replace(T,""),J=!0),U&&n(b,U)&&(b=b.replace(U,"")),W&&n(b,W)&&(b=b.replace(W,""),J=!0),E&&p(b,E)&&(b=b.slice(0,-1*E.length)),d&&(b=b.split(d).join("")),g&&(b=b.replace(g,".")),J&&(H+="-"),H+=b,H=H.replace(/[^0-9\.\-.]/g,""),H==="")||(H=Number(H),X&&(H=X(H)),!x(H))?!1:H}function _(v){var d,g,U,E={};for(v.suffix===void 0&&(v.suffix=v.postfix),d=0;d<a.length;d+=1)if(g=a[d],U=v[g],U===void 0)g==="negative"&&!E.negativeBefore?E[g]="-":g==="mark"&&E.thousand!=="."?E[g]=".":E[g]=!1;else if(g==="decimals")if(U>=0&&U<8)E[g]=U;else throw new Error(g);else if(g==="encoder"||g==="decoder"||g==="edit"||g==="undo")if(typeof U=="function")E[g]=U;else throw new Error(g);else if(typeof U=="string")E[g]=U;else throw new Error(g);return h(E,"mark","thousand"),h(E,"prefix","negative"),h(E,"prefix","negativeBefore"),E}function N(v,d,g){var U,E=[];for(U=0;U<a.length;U+=1)E.push(v[a[U]]);return E.push(g),d.apply("",E)}function w(v){if(!(this instanceof w))return new w(v);typeof v=="object"&&(v=_(v),this.to=function(d){return N(v,S,d)},this.from=function(d){return N(v,V,d)})}return w})}(Ve)),Ve.exports}var vr=pr();const mr=Vt(vr);class q{constructor(){this.baseUrl=window.location.origin+window.location.pathname,this.oldSearchUrl=null,this.searchUrl=null}setOldSearchUrl(){this.oldSearchUrl=this.searchUrl}getFiltersUrl(){return this.setOldSearchUrl(),`${this.baseUrl}?q=${this.searchUrl}`}setSearchUrl(){const e=new URLSearchParams(window.location.search);this.searchUrl=e.get("q"),this.oldSearchUrl=e.get("q")}setRangeParams(e,{unit:a,from:o,to:n}){this.removeGroup(e),this.appendParam(e,a),this.appendParam(e,o),this.appendParam(e,n)}appendParam(e,a){const o=this.searchUrl||"";let n=o.length?o.split("/"):[],p=!1;const h=n.length;e=q.specialEncode(e),a=q.specialEncode(a);for(let x=0;x<h;x+=1)if(n[x].split("-")[0]===e){n[x]=`${n[x]}-${a}`,p=!0;break}p||(n=[...n,`${e}-${a}`]),this.searchUrl=q.specialDecode(q.formatSearchUrl(n))}removeGroup(e){const a=this.searchUrl||"",o=a.length?a.split("/"):[],n=o.length;for(let p=0;p<n;p+=1)o[p].split("-")[0]===e&&o.splice(p,1);this.searchUrl=q.specialDecode(q.formatSearchUrl(o))}static toString(e){return`${e}`}static specialEncode(e){return q.toString(e).replace("/","[slash]")}static specialDecode(e){return q.toString(e).replace("[slash]","/")}removeParam(e,a){const o=this.searchUrl||"",n=o.length?o.split("/"):[],p=n.length;for(let h=0;h<p;h+=1){const C=n[h].split("-");if(C[0]===e){const S=C.filter(V=>V!==a);S.length===1?n.splice(h,1):n[h]=S.join("-");break}}this.searchUrl=q.specialDecode(q.formatSearchUrl(n))}static formatSearchUrl(e){return e.join("/")}}class gr{constructor(e){le(this,"handleInputFocus",({target:e})=>{const a=M(e);a.val(this.format.from(a.val()))});le(this,"handleInputBlur",({target:e})=>{const a=M(e),o=a.val(),n=this.getInputPositionInValue(a),p=this.values,h=[...p];h[n]=o,o!==p[n]?this.sliderHandler.set(h):a.val(this.format.to(parseFloat(a.val(),10)))});le(this,"handleInputKeyup",({target:e,keyCode:a})=>{if(a!==13)return;const o=M(e),n=o.val(),p=this.getInputPositionInValue(o),h=this.values,x=[...h];x[p]=n,n!==h[p]?this.sliderHandler.set(x):o.val(this.format.to(parseFloat(o.val(),10)))});le(this,"handlerSliderUpdate",e=>{this.setInputValues(e)});this.$slider=M(e),this.setConfig(),this.setFormat(),this.initFilersSlider(),this.setEvents()}getSliderType(){this.sliderType=this.$slider.data("slider-specifications")?"price":"weight"}setConfig(){if(this.min=this.$slider.data("slider-min"),this.max=this.$slider.data("slider-max"),this.$parentContainer=this.$slider.closest(".js-input-range-slider-container"),this.$inputs=[this.$parentContainer.find('[data-action="range-from"]'),this.$parentContainer.find('[data-action="range-to"]')],this.getSliderType(),this.sliderType==="price"){const{currencySymbol:e,positivePattern:a}=this.$slider.data("slider-specifications");this.sign=e,this.positivePattern=a,this.values=this.$slider.data("slider-values"),this.signPosition=this.positivePattern.indexOf("¤")===0?"prefix":"suffix"}else if(this.sliderType==="weight"){const e=this.$slider.data("slider-unit");this.sign=e,this.values=this.$slider.data("slider-values"),this.signPosition="suffix"}Array.isArray(this.values)||(this.values=[this.min,this.max])}setFormat(){this.format=mr({mark:",",thousand:" ",decimals:0,[this.signPosition]:this.signPosition==="prefix"?this.sign:` ${this.sign}`})}initFilersSlider(){this.sliderHandler=dr.create(this.$slider.get(0),{start:this.values,connect:[!1,!0,!1],range:{min:this.min,max:this.max},format:this.format})}initFilersSliderInputs(){this.setInputValues(this.values,!0)}setInputValues(e,a=!1){this.$inputs.forEach((o,n)=>{const p=a?this.format.from(e[n]):e[n];M(o).val(p)})}setEvents(){this.sliderHandler.off("set",this.constructor.handlerSliderSet),this.sliderHandler.on("set",this.constructor.handlerSliderSet),this.sliderHandler.off("update",this.handlerSliderUpdate),this.sliderHandler.on("update",this.handlerSliderUpdate),this.$inputs.forEach(e=>{e.off("focus",this.handleInputFocus),e.on("focus",this.handleInputFocus),e.off("blur",this.handleInputBlur),e.on("blur",this.handleInputBlur),e.on("keyup",this.handleInputKeyup)})}static getInputAction(e){return e.data("action")}getInputPositionInValue(e){return{"range-from":0,"range-to":1}[this.constructor.getInputAction(e)]}static handlerSliderSet(e,a,o,n,p,h){const x=h.options.format,C=M(h.target),S=C.data("slider-label"),V=C.data("slider-unit"),[_,N]=e.map(d=>x.from(d)),w=new q;w.setSearchUrl(),w.setRangeParams(S,{unit:V,from:_,to:N});const v=w.getFiltersUrl();F.emit("updateFacets",v)}}class Sr{static init(){M(".js-range-slider").each((a,o)=>{new gr(o)})}}class wr{constructor(){this.$body=M("body"),this.setEvents(),this.rangeSliders=Sr,this.rangeSliders.init()}setEvents(){F.on("updatedProductList",()=>{F.pageLoader.hideLoader(),this.rangeSliders.init()}),F.on("updateFacets",()=>{F.pageLoader.showLoader()}),this.$body.on("click",".js-search-link",e=>{e.preventDefault(),F.emit("updateFacets",M(e.target).closest("a").get(0).href)}),this.$body.on("change",'[data-action="search-select"]',({target:e})=>{F.emit("updateFacets",M(e).find("option:selected").data("href"))}),this.$body.on("click",".js-search-filters-clear-all",e=>{F.emit("updateFacets",this.constructor.parseSearchUrl(e))}),this.$body.on("change","#search_filters input[data-search-url]",e=>{F.emit("updateFacets",this.constructor.parseSearchUrl(e))})}static parseSearchUrl(e){if(e.target.dataset.searchUrl!==void 0)return e.target.dataset.searchUrl;if(M(e.target).parent()[0].dataset.searchUrl===void 0)throw new Error("Can not parse search URL");return M(e.target).parent()[0].dataset.searchUrl}}function br(t){M(F.themeSelectors.listing.searchFilters).replaceWith(t.rendered_facets),M(F.themeSelectors.listing.activeSearchFilters).replaceWith(t.rendered_active_filters),M(F.themeSelectors.listing.listTop).replaceWith(t.rendered_products_top);const e=M(t.rendered_products),a=M(F.themeSelectors.listing.product);a.length>0?a.removeClass().addClass(a.first().attr("class")):a.removeClass().addClass(e.first().attr("class")),M(F.themeSelectors.listing.list).replaceWith(e),M(F.themeSelectors.listing.listBottom).replaceWith(t.rendered_products_bottom),t.rendered_products_header&&M(F.themeSelectors.listing.listHeader).replaceWith(t.rendered_products_header),F.emit("updatedProductList",t)}M(()=>{new wr,F.on("updateProductList",t=>{br(t),window.scrollTo(0,0)}),F.on("updatedProductList",()=>{F.pageLazyLoad.update()})});
//# sourceMappingURL=listing.js.map
