{"version": 3, "file": "toast.7kZxK40-.js", "sources": ["../../../node_modules/.pnpm/bootstrap@5.3.6_@popperjs+core@2.11.8/node_modules/bootstrap/js/src/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport { defineJQueryPlugin, reflow } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${D<PERSON>A_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this.isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout': {\n        this._hasMouseInteraction = isInteracting\n        break\n      }\n\n      case 'focusin':\n      case 'focusout': {\n        this._hasKeyboardInteraction = isInteracting\n        break\n      }\n\n      default: {\n        break\n      }\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "EVENT_MOUSEOVER", "EVENT_MOUSEOUT", "EVENT_FOCUSIN", "EVENT_FOCUSOUT", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "CLASS_NAME_FADE", "CLASS_NAME_HIDE", "CLASS_NAME_SHOW", "CLASS_NAME_SHOWING", "DefaultType", "<PERSON><PERSON><PERSON>", "Toast", "BaseComponent", "element", "config", "EventHandler", "complete", "reflow", "event", "isInteracting", "nextElement", "data", "enableDismissTrigger", "defineJQueryPlugin"], "mappings": "mKAgBA,MAAMA,EAAO,QACPC,EAAW,WACXC,EAAY,IAAID,CAAQ,GAExBE,EAAkB,YAAYD,CAAS,GACvCE,EAAiB,WAAWF,CAAS,GACrCG,EAAgB,UAAUH,CAAS,GACnCI,EAAiB,WAAWJ,CAAS,GACrCK,EAAa,OAAOL,CAAS,GAC7BM,EAAe,SAASN,CAAS,GACjCO,EAAa,OAAOP,CAAS,GAC7BQ,EAAc,QAAQR,CAAS,GAE/BS,EAAkB,OAClBC,EAAkB,OAClBC,EAAkB,OAClBC,EAAqB,UAErBC,EAAc,CAClB,UAAW,UACX,SAAU,UACV,MAAO,QACT,EAEMC,EAAU,CACd,UAAW,GACX,SAAU,GACV,MAAO,GACT,EAMA,MAAMC,UAAcC,CAAc,CAChC,YAAYC,EAASC,EAAQ,CAC3B,MAAMD,EAASC,CAAM,EAErB,KAAK,SAAW,KAChB,KAAK,qBAAuB,GAC5B,KAAK,wBAA0B,GAC/B,KAAK,cAAa,CACtB,CAGE,WAAW,SAAU,CACnB,OAAOJ,CACX,CAEE,WAAW,aAAc,CACvB,OAAOD,CACX,CAEE,WAAW,MAAO,CAChB,OAAOf,CACX,CAGE,MAAO,CAGL,GAFkBqB,EAAa,QAAQ,KAAK,SAAUZ,CAAU,EAElD,iBACZ,OAGF,KAAK,cAAa,EAEd,KAAK,QAAQ,WACf,KAAK,SAAS,UAAU,IAAIE,CAAe,EAG7C,MAAMW,EAAW,IAAM,CACrB,KAAK,SAAS,UAAU,OAAOR,CAAkB,EACjDO,EAAa,QAAQ,KAAK,SAAUX,CAAW,EAE/C,KAAK,mBAAkB,CAC7B,EAEI,KAAK,SAAS,UAAU,OAAOE,CAAe,EAC9CW,EAAO,KAAK,QAAQ,EACpB,KAAK,SAAS,UAAU,IAAIV,EAAiBC,CAAkB,EAE/D,KAAK,eAAeQ,EAAU,KAAK,SAAU,KAAK,QAAQ,SAAS,CACvE,CAEE,MAAO,CAOL,GANI,CAAC,KAAK,WAIQD,EAAa,QAAQ,KAAK,SAAUd,CAAU,EAElD,iBACZ,OAGF,MAAMe,EAAW,IAAM,CACrB,KAAK,SAAS,UAAU,IAAIV,CAAe,EAC3C,KAAK,SAAS,UAAU,OAAOE,EAAoBD,CAAe,EAClEQ,EAAa,QAAQ,KAAK,SAAUb,CAAY,CACtD,EAEI,KAAK,SAAS,UAAU,IAAIM,CAAkB,EAC9C,KAAK,eAAeQ,EAAU,KAAK,SAAU,KAAK,QAAQ,SAAS,CACvE,CAEE,SAAU,CACR,KAAK,cAAa,EAEd,KAAK,WACP,KAAK,SAAS,UAAU,OAAOT,CAAe,EAGhD,MAAM,QAAO,CACjB,CAEE,SAAU,CACR,OAAO,KAAK,SAAS,UAAU,SAASA,CAAe,CAC3D,CAGE,oBAAqB,CACd,KAAK,QAAQ,WAId,KAAK,sBAAwB,KAAK,0BAItC,KAAK,SAAW,WAAW,IAAM,CAC/B,KAAK,KAAI,CACf,EAAO,KAAK,QAAQ,KAAK,GACzB,CAEE,eAAeW,EAAOC,EAAe,CACnC,OAAQD,EAAM,KAAI,CAChB,IAAK,YACL,IAAK,WAAY,CACf,KAAK,qBAAuBC,EAC5B,KACR,CAEM,IAAK,UACL,IAAK,WAAY,CACf,KAAK,wBAA0BA,EAC/B,KACR,CAKA,CAEI,GAAIA,EAAe,CACjB,KAAK,cAAa,EAClB,MACN,CAEI,MAAMC,EAAcF,EAAM,cACtB,KAAK,WAAaE,GAAe,KAAK,SAAS,SAASA,CAAW,GAIvE,KAAK,mBAAkB,CAC3B,CAEE,eAAgB,CACdL,EAAa,GAAG,KAAK,SAAUlB,EAAiBqB,GAAS,KAAK,eAAeA,EAAO,EAAI,CAAC,EACzFH,EAAa,GAAG,KAAK,SAAUjB,EAAgBoB,GAAS,KAAK,eAAeA,EAAO,EAAK,CAAC,EACzFH,EAAa,GAAG,KAAK,SAAUhB,EAAemB,GAAS,KAAK,eAAeA,EAAO,EAAI,CAAC,EACvFH,EAAa,GAAG,KAAK,SAAUf,EAAgBkB,GAAS,KAAK,eAAeA,EAAO,EAAK,CAAC,CAC7F,CAEE,eAAgB,CACd,aAAa,KAAK,QAAQ,EAC1B,KAAK,SAAW,IACpB,CAGE,OAAO,gBAAgBJ,EAAQ,CAC7B,OAAO,KAAK,KAAK,UAAY,CAC3B,MAAMO,EAAOV,EAAM,oBAAoB,KAAMG,CAAM,EAEnD,GAAI,OAAOA,GAAW,SAAU,CAC9B,GAAI,OAAOO,EAAKP,CAAM,GAAM,YAC1B,MAAM,IAAI,UAAU,oBAAoBA,CAAM,GAAG,EAGnDO,EAAKP,CAAM,EAAE,IAAI,CACzB,CACK,CAAA,CACL,CACA,CAMAQ,EAAqBX,CAAK,EAM1BY,EAAmBZ,CAAK", "x_google_ignoreList": [0]}