import{B as T,g as u,E as a,S as r,f as v,c as b,d as S}from"../selector-engine/selector-engine.DUbyXJpR.js";const g="scrollspy",m="bs.scrollspy",h=`.${m}`,L=".data-api",A=`activate${h}`,p=`click${h}`,O=`load${h}${L}`,C="dropdown-item",i="active",D='[data-bs-spy="scroll"]',c="[href]",I=".nav, .list-group",d=".nav-link",N=".nav-item",y=".list-group-item",w=`${d}, ${N} > ${d}, ${y}`,M=".dropdown",R=".dropdown-toggle",$={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},P={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class l extends T{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=getComputedStyle(this._element).overflowY==="visible"?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return $}static get DefaultType(){return P}static get NAME(){return g}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=u(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,typeof t.threshold=="string"&&(t.threshold=t.threshold.split(",").map(e=>Number.parseFloat(e))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(a.off(this._config.target,p),a.on(this._config.target,p,c,t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const s=this._rootElement||window,n=e.offsetTop-this._element.offsetTop;if(s.scrollTo){s.scrollTo({top:n,behavior:"smooth"});return}s.scrollTop=n}}))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(e=>this._observerCallback(e),t)}_observerCallback(t){const e=o=>this._targetLinks.get(`#${o.target.id}`),s=o=>{this._previousScrollData.visibleEntryTop=o.target.offsetTop,this._process(e(o))},n=(this._rootElement||document.documentElement).scrollTop,_=n>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=n;for(const o of t){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(o));continue}const f=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(_&&f){if(s(o),!n)return;continue}!_&&!f&&s(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=r.find(c,this._config.target);for(const e of t){if(!e.hash||v(e))continue;const s=r.findOne(decodeURI(e.hash),this._element);b(s)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,s))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(i),this._activateParents(t),a.trigger(this._element,A,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains(C)){r.findOne(R,t.closest(M)).classList.add(i);return}for(const e of r.parents(t,I))for(const s of r.prev(e,w))s.classList.add(i)}_clearActiveClass(t){t.classList.remove(i);const e=r.find(`${c}.${i}`,t);for(const s of e)s.classList.remove(i)}static jQueryInterface(t){return this.each(function(){const e=l.getOrCreateInstance(this,t);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t]()}})}}a.on(window,O,()=>{for(const E of r.find(D))l.getOrCreateInstance(E)});S(l);export{l as default};
//# sourceMappingURL=scrollspy.Cj27ihKs.js.map
