var l=Object.defineProperty,c=Object.defineProperties;var u=Object.getOwnPropertyDescriptors;var a=Object.getOwnPropertySymbols;var d=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable;var p=(o,t,e)=>t in o?l(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,r=(o,t)=>{for(var e in t||(t={}))d.call(t,e)&&p(o,e,t[e]);if(a)for(var e of a(t))f.call(t,e)&&p(o,e,t[e]);return o},n=(o,t)=>c(o,u(t));import s from"./tooltip.aIpwpUuN.js";import{d as h}from"../selector-engine/selector-engine.DUbyXJpR.js";import"../index/index.3YuNPc_l.js";const v="popover",T=".popover-header",g=".popover-body",m=n(r({},s.Default),{content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"}),y=n(r({},s.DefaultType),{content:"(null|string|element|function)"});class i extends s{static get Default(){return m}static get DefaultType(){return y}static get NAME(){return v}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[T]:this._getTitle(),[g]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each(function(){const e=i.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]=="undefined")throw new TypeError(`No method named "${t}"`);e[t]()}})}}h(i);export{i as default};
//# sourceMappingURL=popover.DmOaZhen.js.map
