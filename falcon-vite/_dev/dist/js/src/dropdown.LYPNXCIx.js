var S=Object.defineProperty;var g=Object.getOwnPropertySymbols;var D=Object.prototype.hasOwnProperty,M=Object.prototype.propertyIsEnumerable;var T=(i,e,t)=>e in i?S(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t,h=(i,e)=>{for(var t in e||(e={}))D.call(e,t)&&T(i,t,e[t]);if(g)for(var t of g(e))M.call(e,t)&&T(i,t,e[t]);return i};import{P as R,c as I}from"../index/index.3YuNPc_l.js";import{B as v,S as a,f as A,E as o,n as N,M as C,i as y,g as B,e as H,c as $,h as V,b as l,d as K}from"../selector-engine/selector-engine.DUbyXJpR.js";const P="dropdown",k="bs.dropdown",d=`.${k}`,E=".data-api",x="Escape",b="Tab",U="ArrowUp",O="ArrowDown",W=2,Y=`hide${d}`,j=`hidden${d}`,G=`show${d}`,q=`shown${d}`,w=`click${d}${E}`,L=`keydown${d}${E}`,Q=`keyup${d}${E}`,u="show",F="dropup",J="dropend",z="dropstart",X="dropup-center",Z="dropdown-center",p='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',ee=`${p}.${u}`,f=".dropdown-menu",te=".navbar",ne=".navbar-nav",se=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",oe=l()?"top-end":"top-start",re=l()?"top-start":"top-end",ie=l()?"bottom-end":"bottom-start",ae=l()?"bottom-start":"bottom-end",ce=l()?"left-start":"right-start",pe=l()?"right-start":"left-start",de="top",ue="bottom",le={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},_e={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class r extends v{constructor(e,t){super(e,t),this._popper=null,this._parent=this._element.parentNode,this._menu=a.next(this._element,f)[0]||a.prev(this._element,f)[0]||a.findOne(f,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return le}static get DefaultType(){return _e}static get NAME(){return P}toggle(){return this._isShown()?this.hide():this.show()}show(){if(A(this._element)||this._isShown())return;const e={relatedTarget:this._element};if(!o.trigger(this._element,G,e).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(ne))for(const s of[].concat(...document.body.children))o.on(s,"mouseover",N);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(u),this._element.classList.add(u),o.trigger(this._element,q,e)}}hide(){if(A(this._element)||!this._isShown())return;const e={relatedTarget:this._element};this._completeHide(e)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(e){if(!o.trigger(this._element,Y,e).defaultPrevented){if("ontouchstart"in document.documentElement)for(const s of[].concat(...document.body.children))o.off(s,"mouseover",N);this._popper&&this._popper.destroy(),this._menu.classList.remove(u),this._element.classList.remove(u),this._element.setAttribute("aria-expanded","false"),C.removeDataAttribute(this._menu,"popper"),o.trigger(this._element,j,e),this._element.focus()}}_getConfig(e){if(e=super._getConfig(e),typeof e.reference=="object"&&!y(e.reference)&&typeof e.reference.getBoundingClientRect!="function")throw new TypeError(`${P.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return e}_createPopper(){if(typeof R=="undefined")throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org/docs/v2/)");let e=this._element;this._config.reference==="parent"?e=this._parent:y(this._config.reference)?e=B(this._config.reference):typeof this._config.reference=="object"&&(e=this._config.reference);const t=this._getPopperConfig();this._popper=I(e,this._menu,t)}_isShown(){return this._menu.classList.contains(u)}_getPlacement(){const e=this._parent;if(e.classList.contains(J))return ce;if(e.classList.contains(z))return pe;if(e.classList.contains(X))return de;if(e.classList.contains(Z))return ue;const t=getComputedStyle(this._menu).getPropertyValue("--bs-position").trim()==="end";return e.classList.contains(F)?t?re:oe:t?ae:ie}_detectNavbar(){return this._element.closest(te)!==null}_getOffset(){const{offset:e}=this._config;return typeof e=="string"?e.split(",").map(t=>Number.parseInt(t,10)):typeof e=="function"?t=>e(t,this._element):e}_getPopperConfig(){const e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||this._config.display==="static")&&(C.setDataAttribute(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),h(h({},e),H(this._config.popperConfig,[void 0,e]))}_selectMenuItem({key:e,target:t}){const s=a.find(se,this._menu).filter(n=>$(n));s.length&&V(s,t,e===O,!s.includes(t)).focus()}static jQueryInterface(e){return this.each(function(){const t=r.getOrCreateInstance(this,e);if(typeof e=="string"){if(typeof t[e]=="undefined")throw new TypeError(`No method named "${e}"`);t[e]()}})}static clearMenus(e){if(e.button===W||e.type==="keyup"&&e.key!==b)return;const t=a.find(ee);for(const s of t){const n=r.getInstance(s);if(!n||n._config.autoClose===!1)continue;const _=e.composedPath(),c=_.includes(n._menu);if(_.includes(n._element)||n._config.autoClose==="inside"&&!c||n._config.autoClose==="outside"&&c||n._menu.contains(e.target)&&(e.type==="keyup"&&e.key===b||/input|select|option|textarea|form/i.test(e.target.tagName)))continue;const m={relatedTarget:n._element};e.type==="click"&&(m.clickEvent=e),n._completeHide(m)}}static dataApiKeydownHandler(e){const t=/input|textarea/i.test(e.target.tagName),s=e.key===x,n=[U,O].includes(e.key);if(!n&&!s||t&&!s)return;e.preventDefault();const _=this.matches(p)?this:a.prev(this,p)[0]||a.next(this,p)[0]||a.findOne(p,e.delegateTarget.parentNode),c=r.getOrCreateInstance(_);if(n){e.stopPropagation(),c.show(),c._selectMenuItem(e);return}c._isShown()&&(e.stopPropagation(),c.hide(),_.focus())}}o.on(document,L,p,r.dataApiKeydownHandler);o.on(document,L,f,r.dataApiKeydownHandler);o.on(document,w,r.clearMenus);o.on(document,Q,r.clearMenus);o.on(document,w,p,function(i){i.preventDefault(),r.getOrCreateInstance(this).toggle()});K(r);export{r as default};
//# sourceMappingURL=dropdown.LYPNXCIx.js.map
