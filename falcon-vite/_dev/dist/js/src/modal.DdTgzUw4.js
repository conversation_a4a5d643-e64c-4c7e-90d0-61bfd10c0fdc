import{C as D,e as c,r as S,E as s,g as L,a as M,S as a,M as m,i as $,B as I,b as E,c as V,d as B}from"../selector-engine/selector-engine.DUbyXJpR.js";import{e as W}from"../component-functions/component-functions.MEtW7SSs.js";const y="backdrop",R="fade",g="show",p=`mousedown.bs.${y}`,Y={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},K={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class P extends D{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return Y}static get DefaultType(){return K}static get NAME(){return y}show(t){if(!this._config.isVisible){c(t);return}this._append();const e=this._getElement();this._config.isAnimated&&S(e),e.classList.add(g),this._emulateAnimation(()=>{c(t)})}hide(t){if(!this._config.isVisible){c(t);return}this._getElement().classList.remove(g),this._emulateAnimation(()=>{this.dispose(),c(t)})}dispose(){this._isAppended&&(s.off(this._element,p),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add(R),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=L(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),s.on(t,p,()=>{c(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(t){M(t,this._getElement(),this._config.isAnimated)}}const F="focustrap",H="bs.focustrap",u=`.${H}`,x=`focusin${u}`,j=`keydown.tab${u}`,z="Tab",G="forward",A="backward",q={autofocus:!0,trapElement:null},U={autofocus:"boolean",trapElement:"element"};class Q extends D{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return q}static get DefaultType(){return U}static get NAME(){return F}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),s.off(document,u),s.on(document,x,t=>this._handleFocusin(t)),s.on(document,j,t=>this._handleKeydown(t)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,s.off(document,u))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const i=a.focusableChildren(e);i.length===0?e.focus():this._lastTabNavDirection===A?i[i.length-1].focus():i[0].focus()}_handleKeydown(t){t.key===z&&(this._lastTabNavDirection=t.shiftKey?A:G)}}const b=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",T=".sticky-top",_="padding-right",w="margin-right";class J{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,_,e=>e+t),this._setElementAttributes(b,_,e=>e+t),this._setElementAttributes(T,w,e=>e-t)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,_),this._resetElementAttributes(b,_),this._resetElementAttributes(T,w)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,i){const n=this.getWidth(),d=r=>{if(r!==this._element&&window.innerWidth>r.clientWidth+n)return;this._saveInitialAttribute(r,e);const O=window.getComputedStyle(r).getPropertyValue(e);r.style.setProperty(e,`${i(Number.parseFloat(O))}px`)};this._applyManipulationCallback(t,d)}_saveInitialAttribute(t,e){const i=t.style.getPropertyValue(e);i&&m.setDataAttribute(t,e,i)}_resetElementAttributes(t,e){const i=n=>{const d=m.getDataAttribute(n,e);if(d===null){n.style.removeProperty(e);return}m.removeDataAttribute(n,e),n.style.setProperty(e,d)};this._applyManipulationCallback(t,i)}_applyManipulationCallback(t,e){if($(t)){e(t);return}for(const i of a.find(t,this._element))e(i)}}const X="modal",Z="bs.modal",o=`.${Z}`,tt=".data-api",et="Escape",it=`hide${o}`,st=`hidePrevented${o}`,C=`hidden${o}`,k=`show${o}`,nt=`shown${o}`,ot=`resize${o}`,at=`click.dismiss${o}`,lt=`mousedown.dismiss${o}`,rt=`keydown.dismiss${o}`,ct=`click${o}${tt}`,N="modal-open",ht="fade",v="show",f="modal-static",dt=".modal.show",_t=".modal-dialog",ut=".modal-body",mt='[data-bs-toggle="modal"]',ft={backdrop:!0,focus:!0,keyboard:!0},Et={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class l extends I{constructor(t,e){super(t,e),this._dialog=a.findOne(_t,this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new J,this._addEventListeners()}static get Default(){return ft}static get DefaultType(){return Et}static get NAME(){return X}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||s.trigger(this._element,k,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(N),this._adjustDialog(),this._backdrop.show(()=>this._showElement(t)))}hide(){!this._isShown||this._isTransitioning||s.trigger(this._element,it).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(v),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){s.off(window,o),s.off(this._dialog,o),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new P({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new Q({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const e=a.findOne(ut,this._dialog);e&&(e.scrollTop=0),S(this._element),this._element.classList.add(v);const i=()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,s.trigger(this._element,nt,{relatedTarget:t})};this._queueCallback(i,this._dialog,this._isAnimated())}_addEventListeners(){s.on(this._element,rt,t=>{if(t.key===et){if(this._config.keyboard){this.hide();return}this._triggerBackdropTransition()}}),s.on(window,ot,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),s.on(this._element,lt,t=>{s.one(this._element,at,e=>{if(!(this._element!==t.target||this._element!==e.target)){if(this._config.backdrop==="static"){this._triggerBackdropTransition();return}this._config.backdrop&&this.hide()}})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(N),this._resetAdjustments(),this._scrollBar.reset(),s.trigger(this._element,C)})}_isAnimated(){return this._element.classList.contains(ht)}_triggerBackdropTransition(){if(s.trigger(this._element,st).defaultPrevented)return;const e=this._element.scrollHeight>document.documentElement.clientHeight,i=this._element.style.overflowY;i==="hidden"||this._element.classList.contains(f)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(f),this._queueCallback(()=>{this._element.classList.remove(f),this._queueCallback(()=>{this._element.style.overflowY=i},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),i=e>0;if(i&&!t){const n=E()?"paddingLeft":"paddingRight";this._element.style[n]=`${e}px`}if(!i&&t){const n=E()?"paddingRight":"paddingLeft";this._element.style[n]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,e){return this.each(function(){const i=l.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof i[t]=="undefined")throw new TypeError(`No method named "${t}"`);i[t](e)}})}}s.on(document,ct,mt,function(h){const t=a.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&h.preventDefault(),s.one(t,k,n=>{n.defaultPrevented||s.one(t,C,()=>{V(this)&&this.focus()})});const e=a.findOne(dt);e&&l.getInstance(e).hide(),l.getOrCreateInstance(t).toggle(this)});W(l);B(l);export{l as default};
//# sourceMappingURL=modal.DdTgzUw4.js.map
