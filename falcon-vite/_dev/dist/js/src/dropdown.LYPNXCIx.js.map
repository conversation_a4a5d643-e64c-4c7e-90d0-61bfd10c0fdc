{"version": 3, "file": "dropdown.LYPNXCIx.js", "sources": ["../../../node_modules/.pnpm/bootstrap@5.3.6_@popperjs+core@2.11.8/node_modules/bootstrap/js/src/dropdown.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  execute,\n  getElement,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center'\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)'\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE}.${CLASS_NAME_SHOW}`\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR = '.navbar'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\nconst PLACEMENT_TOPCENTER = 'top'\nconst PLACEMENT_BOTTOMCENTER = 'bottom'\n\nconst Default = {\n  autoClose: true,\n  boundary: 'clippingParents',\n  display: 'dynamic',\n  offset: [0, 2],\n  popperConfig: null,\n  reference: 'toggle'\n}\n\nconst DefaultType = {\n  autoClose: '(boolean|string)',\n  boundary: '(string|element)',\n  display: 'string',\n  offset: '(array|string|function)',\n  popperConfig: '(null|object|function)',\n  reference: '(string|element|object)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._popper = null\n    this._parent = this._element.parentNode // dropdown wrapper\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    this._menu = SelectorEngine.next(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.prev(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.findOne(SELECTOR_MENU, this._parent)\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._createPopper()\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n\n    // Explicitly return focus to the trigger element\n    this._element.focus()\n  }\n\n  _getConfig(config) {\n    config = super._getConfig(config)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org/docs/v2/)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n  }\n\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._parent\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static') // TODO: v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [undefined, defaultBsPopperConfig])\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element))\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n      return\n    }\n\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN)\n\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      const composedPath = event.composedPath()\n      const isMenuTarget = composedPath.includes(context._menu)\n      if (\n        composedPath.includes(context._element) ||\n        (context._config.autoClose === 'inside' && !isMenuTarget) ||\n        (context._config.autoClose === 'outside' && isMenuTarget)\n      ) {\n        continue\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue\n      }\n\n      const relatedTarget = { relatedTarget: context._element }\n\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName)\n    const isEscapeEvent = event.key === ESCAPE_KEY\n    const isUpOrDownEvent = [ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key)\n\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return\n    }\n\n    if (isInput && !isEscapeEvent) {\n      return\n    }\n\n    event.preventDefault()\n\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE) ?\n      this :\n      (SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.next(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.findOne(SELECTOR_DATA_TOGGLE, event.delegateTarget.parentNode))\n\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (isUpOrDownEvent) {\n      event.stopPropagation()\n      instance.show()\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (instance._isShown()) { // else is escape and we check if it is shown\n      event.stopPropagation()\n      instance.hide()\n      getToggleButton.focus()\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "ESCAPE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_SHOW", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPEND", "CLASS_NAME_DROPSTART", "CLASS_NAME_DROPUP_CENTER", "CLASS_NAME_DROPDOWN_CENTER", "SELECTOR_DATA_TOGGLE", "SELECTOR_DATA_TOGGLE_SHOWN", "SELECTOR_MENU", "SELECTOR_NAVBAR", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "isRTL", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "PLACEMENT_TOPCENTER", "PLACEMENT_BOTTOMCENTER", "<PERSON><PERSON><PERSON>", "DefaultType", "Dropdown", "BaseComponent", "element", "config", "SelectorEngine", "isDisabled", "relatedTarget", "EventHandler", "noop", "Manipulator", "isElement", "<PERSON><PERSON>", "referenceElement", "getElement", "popperConfig", "Popper.createPopper", "parentDropdown", "isEnd", "offset", "value", "popperData", "defaultBsPopperConfig", "__spreadValues", "execute", "key", "target", "items", "isVisible", "getNextActiveElement", "data", "event", "openToggles", "toggle", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "isInput", "isEscapeEvent", "isUpOrDownEvent", "getToggleButton", "instance", "defineJQueryPlugin"], "mappings": "siBA4BA,MAAMA,EAAO,WACPC,EAAW,cACXC,EAAY,IAAID,CAAQ,GACxBE,EAAe,YAEfC,EAAa,SACbC,EAAU,MACVC,EAAe,UACfC,EAAiB,YACjBC,EAAqB,EAErBC,EAAa,OAAOP,CAAS,GAC7BQ,EAAe,SAASR,CAAS,GACjCS,EAAa,OAAOT,CAAS,GAC7BU,EAAc,QAAQV,CAAS,GAC/BW,EAAuB,QAAQX,CAAS,GAAGC,CAAY,GACvDW,EAAyB,UAAUZ,CAAS,GAAGC,CAAY,GAC3DY,EAAuB,QAAQb,CAAS,GAAGC,CAAY,GAEvDa,EAAkB,OAClBC,EAAoB,SACpBC,EAAqB,UACrBC,EAAuB,YACvBC,EAA2B,gBAC3BC,EAA6B,kBAE7BC,EAAuB,4DACvBC,GAA6B,GAAGD,CAAoB,IAAIN,CAAe,GACvEQ,EAAgB,iBAChBC,GAAkB,UAClBC,GAAsB,cACtBC,GAAyB,8DAEzBC,GAAgBC,EAAO,EAAG,UAAY,YACtCC,GAAmBD,EAAO,EAAG,YAAc,UAC3CE,GAAmBF,EAAO,EAAG,aAAe,eAC5CG,GAAsBH,EAAO,EAAG,eAAiB,aACjDI,GAAkBJ,EAAO,EAAG,aAAe,cAC3CK,GAAiBL,EAAO,EAAG,cAAgB,aAC3CM,GAAsB,MACtBC,GAAyB,SAEzBC,GAAU,CACd,UAAW,GACX,SAAU,kBACV,QAAS,UACT,OAAQ,CAAC,EAAG,CAAC,EACb,aAAc,KACd,UAAW,QACb,EAEMC,GAAc,CAClB,UAAW,mBACX,SAAU,mBACV,QAAS,SACT,OAAQ,0BACR,aAAc,yBACd,UAAW,yBACb,EAMA,MAAMC,UAAiBC,CAAc,CACnC,YAAYC,EAASC,EAAQ,CAC3B,MAAMD,EAASC,CAAM,EAErB,KAAK,QAAU,KACf,KAAK,QAAU,KAAK,SAAS,WAE7B,KAAK,MAAQC,EAAe,KAAK,KAAK,SAAUnB,CAAa,EAAE,CAAC,GAC9DmB,EAAe,KAAK,KAAK,SAAUnB,CAAa,EAAE,CAAC,GACnDmB,EAAe,QAAQnB,EAAe,KAAK,OAAO,EACpD,KAAK,UAAY,KAAK,cAAa,CACvC,CAGE,WAAW,SAAU,CACnB,OAAOa,EACX,CAEE,WAAW,aAAc,CACvB,OAAOC,EACX,CAEE,WAAW,MAAO,CAChB,OAAOtC,CACX,CAGE,QAAS,CACP,OAAO,KAAK,SAAU,EAAG,KAAK,KAAI,EAAK,KAAK,KAAI,CACpD,CAEE,MAAO,CACL,GAAI4C,EAAW,KAAK,QAAQ,GAAK,KAAK,SAAQ,EAC5C,OAGF,MAAMC,EAAgB,CACpB,cAAe,KAAK,QAC1B,EAII,GAAI,CAFcC,EAAa,QAAQ,KAAK,SAAUnC,EAAYkC,CAAa,EAEjE,iBAUd,IANA,KAAK,cAAa,EAMd,iBAAkB,SAAS,iBAAmB,CAAC,KAAK,QAAQ,QAAQnB,EAAmB,EACzF,UAAWe,IAAW,GAAG,OAAO,GAAG,SAAS,KAAK,QAAQ,EACvDK,EAAa,GAAGL,EAAS,YAAaM,CAAI,EAI9C,KAAK,SAAS,MAAK,EACnB,KAAK,SAAS,aAAa,gBAAiB,EAAI,EAEhD,KAAK,MAAM,UAAU,IAAI/B,CAAe,EACxC,KAAK,SAAS,UAAU,IAAIA,CAAe,EAC3C8B,EAAa,QAAQ,KAAK,SAAUlC,EAAaiC,CAAa,EAClE,CAEE,MAAO,CACL,GAAID,EAAW,KAAK,QAAQ,GAAK,CAAC,KAAK,WACrC,OAGF,MAAMC,EAAgB,CACpB,cAAe,KAAK,QAC1B,EAEI,KAAK,cAAcA,CAAa,CACpC,CAEE,SAAU,CACJ,KAAK,SACP,KAAK,QAAQ,QAAO,EAGtB,MAAM,QAAO,CACjB,CAEE,QAAS,CACP,KAAK,UAAY,KAAK,cAAa,EAC/B,KAAK,SACP,KAAK,QAAQ,OAAM,CAEzB,CAGE,cAAcA,EAAe,CAE3B,GAAI,CADcC,EAAa,QAAQ,KAAK,SAAUrC,EAAYoC,CAAa,EACjE,iBAMd,IAAI,iBAAkB,SAAS,gBAC7B,UAAWJ,IAAW,GAAG,OAAO,GAAG,SAAS,KAAK,QAAQ,EACvDK,EAAa,IAAIL,EAAS,YAAaM,CAAI,EAI3C,KAAK,SACP,KAAK,QAAQ,QAAO,EAGtB,KAAK,MAAM,UAAU,OAAO/B,CAAe,EAC3C,KAAK,SAAS,UAAU,OAAOA,CAAe,EAC9C,KAAK,SAAS,aAAa,gBAAiB,OAAO,EACnDgC,EAAY,oBAAoB,KAAK,MAAO,QAAQ,EACpDF,EAAa,QAAQ,KAAK,SAAUpC,EAAcmC,CAAa,EAG/D,KAAK,SAAS,MAAK,EACvB,CAEE,WAAWH,EAAQ,CAGjB,GAFAA,EAAS,MAAM,WAAWA,CAAM,EAE5B,OAAOA,EAAO,WAAc,UAAY,CAACO,EAAUP,EAAO,SAAS,GACrE,OAAOA,EAAO,UAAU,uBAA0B,WAGlD,MAAM,IAAI,UAAU,GAAG1C,EAAK,YAAW,CAAE,gGAAgG,EAG3I,OAAO0C,CACX,CAEE,eAAgB,CACd,GAAI,OAAOQ,GAAW,YACpB,MAAM,IAAI,UAAU,uEAAwE,EAG9F,IAAIC,EAAmB,KAAK,SAExB,KAAK,QAAQ,YAAc,SAC7BA,EAAmB,KAAK,QACfF,EAAU,KAAK,QAAQ,SAAS,EACzCE,EAAmBC,EAAW,KAAK,QAAQ,SAAS,EAC3C,OAAO,KAAK,QAAQ,WAAc,WAC3CD,EAAmB,KAAK,QAAQ,WAGlC,MAAME,EAAe,KAAK,iBAAgB,EAC1C,KAAK,QAAUC,EAAoBH,EAAkB,KAAK,MAAOE,CAAY,CACjF,CAEE,UAAW,CACT,OAAO,KAAK,MAAM,UAAU,SAASrC,CAAe,CACxD,CAEE,eAAgB,CACd,MAAMuC,EAAiB,KAAK,QAE5B,GAAIA,EAAe,UAAU,SAASrC,CAAkB,EACtD,OAAOe,GAGT,GAAIsB,EAAe,UAAU,SAASpC,CAAoB,EACxD,OAAOe,GAGT,GAAIqB,EAAe,UAAU,SAASnC,CAAwB,EAC5D,OAAOe,GAGT,GAAIoB,EAAe,UAAU,SAASlC,CAA0B,EAC9D,OAAOe,GAIT,MAAMoB,EAAQ,iBAAiB,KAAK,KAAK,EAAE,iBAAiB,eAAe,EAAE,SAAW,MAExF,OAAID,EAAe,UAAU,SAAStC,CAAiB,EAC9CuC,EAAQ1B,GAAmBF,GAG7B4B,EAAQxB,GAAsBD,EACzC,CAEE,eAAgB,CACd,OAAO,KAAK,SAAS,QAAQN,EAAe,IAAM,IACtD,CAEE,YAAa,CACX,KAAM,CAAE,OAAAgC,CAAQ,EAAG,KAAK,QAExB,OAAI,OAAOA,GAAW,SACbA,EAAO,MAAM,GAAG,EAAE,IAAIC,GAAS,OAAO,SAASA,EAAO,EAAE,CAAC,EAG9D,OAAOD,GAAW,WACbE,GAAcF,EAAOE,EAAY,KAAK,QAAQ,EAGhDF,CACX,CAEE,kBAAmB,CACjB,MAAMG,EAAwB,CAC5B,UAAW,KAAK,cAAe,EAC/B,UAAW,CAAC,CACV,KAAM,kBACN,QAAS,CACP,SAAU,KAAK,QAAQ,QACjC,CACO,EACD,CACE,KAAM,SACN,QAAS,CACP,OAAQ,KAAK,WAAU,CACjC,CACO,CAAA,CACP,EAGI,OAAI,KAAK,WAAa,KAAK,QAAQ,UAAY,YAC7CZ,EAAY,iBAAiB,KAAK,MAAO,SAAU,QAAQ,EAC3DY,EAAsB,UAAY,CAAC,CACjC,KAAM,cACN,QAAS,EACV,CAAA,GAGIC,IAAA,GACFD,GACAE,EAAQ,KAAK,QAAQ,aAAc,CAAC,OAAWF,CAAqB,CAAC,EAE9E,CAEE,gBAAgB,CAAE,IAAAG,EAAK,OAAAC,GAAU,CAC/B,MAAMC,EAAQtB,EAAe,KAAKhB,GAAwB,KAAK,KAAK,EAAE,OAAOc,GAAWyB,EAAUzB,CAAO,CAAC,EAErGwB,EAAM,QAMXE,EAAqBF,EAAOD,EAAQD,IAAQxD,EAAgB,CAAC0D,EAAM,SAASD,CAAM,CAAC,EAAE,MAAK,CAC9F,CAGE,OAAO,gBAAgBtB,EAAQ,CAC7B,OAAO,KAAK,KAAK,UAAY,CAC3B,MAAM0B,EAAO7B,EAAS,oBAAoB,KAAMG,CAAM,EAEtD,GAAI,OAAOA,GAAW,SAItB,IAAI,OAAO0B,EAAK1B,CAAM,GAAM,YAC1B,MAAM,IAAI,UAAU,oBAAoBA,CAAM,GAAG,EAGnD0B,EAAK1B,CAAM,EAAC,EACb,CAAA,CACL,CAEE,OAAO,WAAW2B,EAAO,CACvB,GAAIA,EAAM,SAAW7D,GAAuB6D,EAAM,OAAS,SAAWA,EAAM,MAAQhE,EAClF,OAGF,MAAMiE,EAAc3B,EAAe,KAAKpB,EAA0B,EAElE,UAAWgD,KAAUD,EAAa,CAChC,MAAME,EAAUjC,EAAS,YAAYgC,CAAM,EAC3C,GAAI,CAACC,GAAWA,EAAQ,QAAQ,YAAc,GAC5C,SAGF,MAAMC,EAAeJ,EAAM,aAAY,EACjCK,EAAeD,EAAa,SAASD,EAAQ,KAAK,EAUxD,GAREC,EAAa,SAASD,EAAQ,QAAQ,GACrCA,EAAQ,QAAQ,YAAc,UAAY,CAACE,GAC3CF,EAAQ,QAAQ,YAAc,WAAaE,GAM1CF,EAAQ,MAAM,SAASH,EAAM,MAAM,IAAOA,EAAM,OAAS,SAAWA,EAAM,MAAQhE,GAAY,qCAAqC,KAAKgE,EAAM,OAAO,OAAO,GAC9J,SAGF,MAAMxB,EAAgB,CAAE,cAAe2B,EAAQ,QAAQ,EAEnDH,EAAM,OAAS,UACjBxB,EAAc,WAAawB,GAG7BG,EAAQ,cAAc3B,CAAa,CACzC,CACA,CAEE,OAAO,sBAAsBwB,EAAO,CAIlC,MAAMM,EAAU,kBAAkB,KAAKN,EAAM,OAAO,OAAO,EACrDO,EAAgBP,EAAM,MAAQjE,EAC9ByE,EAAkB,CAACvE,EAAcC,CAAc,EAAE,SAAS8D,EAAM,GAAG,EAMzE,GAJI,CAACQ,GAAmB,CAACD,GAIrBD,GAAW,CAACC,EACd,OAGFP,EAAM,eAAc,EAGpB,MAAMS,EAAkB,KAAK,QAAQxD,CAAoB,EACvD,KACCqB,EAAe,KAAK,KAAMrB,CAAoB,EAAE,CAAC,GAChDqB,EAAe,KAAK,KAAMrB,CAAoB,EAAE,CAAC,GACjDqB,EAAe,QAAQrB,EAAsB+C,EAAM,eAAe,UAAU,EAE1EU,EAAWxC,EAAS,oBAAoBuC,CAAe,EAE7D,GAAID,EAAiB,CACnBR,EAAM,gBAAe,EACrBU,EAAS,KAAI,EACbA,EAAS,gBAAgBV,CAAK,EAC9B,MACN,CAEQU,EAAS,aACXV,EAAM,gBAAe,EACrBU,EAAS,KAAI,EACbD,EAAgB,MAAK,EAE3B,CACA,CAMAhC,EAAa,GAAG,SAAUhC,EAAwBQ,EAAsBiB,EAAS,qBAAqB,EACtGO,EAAa,GAAG,SAAUhC,EAAwBU,EAAee,EAAS,qBAAqB,EAC/FO,EAAa,GAAG,SAAUjC,EAAsB0B,EAAS,UAAU,EACnEO,EAAa,GAAG,SAAU/B,EAAsBwB,EAAS,UAAU,EACnEO,EAAa,GAAG,SAAUjC,EAAsBS,EAAsB,SAAU+C,EAAO,CACrFA,EAAM,eAAc,EACpB9B,EAAS,oBAAoB,IAAI,EAAE,OAAM,CAC3C,CAAC,EAMDyC,EAAmBzC,CAAQ", "x_google_ignoreList": [0]}