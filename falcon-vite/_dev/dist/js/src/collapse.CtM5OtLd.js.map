{"version": 3, "file": "collapse.CtM5OtLd.js", "sources": ["../../../node_modules/.pnpm/bootstrap@5.3.6_@popperjs+core@2.11.8/node_modules/bootstrap/js/src/collapse.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  getElement,\n  reflow\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\nconst Default = {\n  parent: null,\n  toggle: true\n}\n\nconst DefaultType = {\n  parent: '(null|element)',\n  toggle: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isTransitioning = false\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (const elem of toggleList) {\n      const selector = SelectorEngine.getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElement => foundElement === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let activeChildren = []\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES)\n        .filter(element => element !== this._element)\n        .map(element => Collapse.getOrCreateInstance(element, { toggle: false }))\n    }\n\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide()\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    for (const trigger of this._triggerArray) {\n      const element = SelectorEngine.getElementFromSelector(trigger)\n\n      if (element && !this._isShown(element)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  // Private\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE)\n\n    for (const element of children) {\n      const selected = SelectorEngine.getElementFromSelector(element)\n\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected))\n      }\n    }\n  }\n\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element))\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen)\n      element.setAttribute('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {}\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  for (const element of SelectorEngine.getMultipleElementsFromSelector(this)) {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_CLICK_DATA_API", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "CLASS_NAME_DEEPER_CHILDREN", "CLASS_NAME_HORIZONTAL", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON><PERSON>", "DefaultType", "Collapse", "BaseComponent", "element", "config", "toggleList", "SelectorEngine", "elem", "selector", "filterElement", "foundElement", "activeC<PERSON><PERSON>n", "EventHandler", "activeInstance", "dimension", "complete", "scrollSize", "reflow", "trigger", "getElement", "children", "selected", "trigger<PERSON><PERSON>y", "isOpen", "_config", "data", "event", "defineJQueryPlugin"], "mappings": "sGAoBA,MAAMA,EAAO,WACPC,EAAW,cACXC,EAAY,IAAID,CAAQ,GACxBE,EAAe,YAEfC,EAAa,OAAOF,CAAS,GAC7BG,EAAc,QAAQH,CAAS,GAC/BI,EAAa,OAAOJ,CAAS,GAC7BK,EAAe,SAASL,CAAS,GACjCM,EAAuB,QAAQN,CAAS,GAAGC,CAAY,GAEvDM,EAAkB,OAClBC,EAAsB,WACtBC,EAAwB,aACxBC,EAAuB,YACvBC,EAA6B,WAAWH,CAAmB,KAAKA,CAAmB,GACnFI,EAAwB,sBAExBC,EAAQ,QACRC,EAAS,SAETC,EAAmB,uCACnBC,EAAuB,8BAEvBC,EAAU,CACd,OAAQ,KACR,OAAQ,EACV,EAEMC,EAAc,CAClB,OAAQ,iBACR,OAAQ,SACV,EAMA,MAAMC,UAAiBC,CAAc,CACnC,YAAYC,EAASC,EAAQ,CAC3B,MAAMD,EAASC,CAAM,EAErB,KAAK,iBAAmB,GACxB,KAAK,cAAgB,CAAA,EAErB,MAAMC,EAAaC,EAAe,KAAKR,CAAoB,EAE3D,UAAWS,KAAQF,EAAY,CAC7B,MAAMG,EAAWF,EAAe,uBAAuBC,CAAI,EACrDE,EAAgBH,EAAe,KAAKE,CAAQ,EAC/C,OAAOE,GAAgBA,IAAiB,KAAK,QAAQ,EAEpDF,IAAa,MAAQC,EAAc,QACrC,KAAK,cAAc,KAAKF,CAAI,CAEpC,CAEI,KAAK,oBAAmB,EAEnB,KAAK,QAAQ,QAChB,KAAK,0BAA0B,KAAK,cAAe,KAAK,SAAU,CAAA,EAGhE,KAAK,QAAQ,QACf,KAAK,OAAM,CAEjB,CAGE,WAAW,SAAU,CACnB,OAAOR,CACX,CAEE,WAAW,aAAc,CACvB,OAAOC,CACX,CAEE,WAAW,MAAO,CAChB,OAAOpB,CACX,CAGE,QAAS,CACH,KAAK,WACP,KAAK,KAAI,EAET,KAAK,KAAI,CAEf,CAEE,MAAO,CACL,GAAI,KAAK,kBAAoB,KAAK,SAAQ,EACxC,OAGF,IAAI+B,EAAiB,CAAA,EAcrB,GAXI,KAAK,QAAQ,SACfA,EAAiB,KAAK,uBAAuBd,CAAgB,EAC1D,OAAOM,GAAWA,IAAY,KAAK,QAAQ,EAC3C,IAAIA,GAAWF,EAAS,oBAAoBE,EAAS,CAAE,OAAQ,GAAO,CAAC,GAGxEQ,EAAe,QAAUA,EAAe,CAAC,EAAE,kBAI5BC,EAAa,QAAQ,KAAK,SAAU5B,CAAU,EAClD,iBACb,OAGF,UAAW6B,KAAkBF,EAC3BE,EAAe,KAAI,EAGrB,MAAMC,EAAY,KAAK,cAAa,EAEpC,KAAK,SAAS,UAAU,OAAOxB,CAAmB,EAClD,KAAK,SAAS,UAAU,IAAIC,CAAqB,EAEjD,KAAK,SAAS,MAAMuB,CAAS,EAAI,EAEjC,KAAK,0BAA0B,KAAK,cAAe,EAAI,EACvD,KAAK,iBAAmB,GAExB,MAAMC,EAAW,IAAM,CACrB,KAAK,iBAAmB,GAExB,KAAK,SAAS,UAAU,OAAOxB,CAAqB,EACpD,KAAK,SAAS,UAAU,IAAID,EAAqBD,CAAe,EAEhE,KAAK,SAAS,MAAMyB,CAAS,EAAI,GAEjCF,EAAa,QAAQ,KAAK,SAAU3B,CAAW,CACrD,EAGU+B,EAAa,SADUF,EAAU,CAAC,EAAE,cAAgBA,EAAU,MAAM,CAAC,CAC3B,GAEhD,KAAK,eAAeC,EAAU,KAAK,SAAU,EAAI,EACjD,KAAK,SAAS,MAAMD,CAAS,EAAI,GAAG,KAAK,SAASE,CAAU,CAAC,IACjE,CAEE,MAAO,CAML,GALI,KAAK,kBAAoB,CAAC,KAAK,SAAQ,GAIxBJ,EAAa,QAAQ,KAAK,SAAU1B,CAAU,EAClD,iBACb,OAGF,MAAM4B,EAAY,KAAK,cAAa,EAEpC,KAAK,SAAS,MAAMA,CAAS,EAAI,GAAG,KAAK,SAAS,wBAAwBA,CAAS,CAAC,KAEpFG,EAAO,KAAK,QAAQ,EAEpB,KAAK,SAAS,UAAU,IAAI1B,CAAqB,EACjD,KAAK,SAAS,UAAU,OAAOD,EAAqBD,CAAe,EAEnE,UAAW6B,KAAW,KAAK,cAAe,CACxC,MAAMf,EAAUG,EAAe,uBAAuBY,CAAO,EAEzDf,GAAW,CAAC,KAAK,SAASA,CAAO,GACnC,KAAK,0BAA0B,CAACe,CAAO,EAAG,EAAK,CAEvD,CAEI,KAAK,iBAAmB,GAExB,MAAMH,EAAW,IAAM,CACrB,KAAK,iBAAmB,GACxB,KAAK,SAAS,UAAU,OAAOxB,CAAqB,EACpD,KAAK,SAAS,UAAU,IAAID,CAAmB,EAC/CsB,EAAa,QAAQ,KAAK,SAAUzB,CAAY,CACtD,EAEI,KAAK,SAAS,MAAM2B,CAAS,EAAI,GAEjC,KAAK,eAAeC,EAAU,KAAK,SAAU,EAAI,CACrD,CAGE,SAASZ,EAAU,KAAK,SAAU,CAChC,OAAOA,EAAQ,UAAU,SAASd,CAAe,CACrD,CAEE,kBAAkBe,EAAQ,CACxB,OAAAA,EAAO,OAAS,EAAQA,EAAO,OAC/BA,EAAO,OAASe,EAAWf,EAAO,MAAM,EACjCA,CACX,CAEE,eAAgB,CACd,OAAO,KAAK,SAAS,UAAU,SAASV,CAAqB,EAAIC,EAAQC,CAC7E,CAEE,qBAAsB,CACpB,GAAI,CAAC,KAAK,QAAQ,OAChB,OAGF,MAAMwB,EAAW,KAAK,uBAAuBtB,CAAoB,EAEjE,UAAWK,KAAWiB,EAAU,CAC9B,MAAMC,EAAWf,EAAe,uBAAuBH,CAAO,EAE1DkB,GACF,KAAK,0BAA0B,CAAClB,CAAO,EAAG,KAAK,SAASkB,CAAQ,CAAC,CAEzE,CACA,CAEE,uBAAuBb,EAAU,CAC/B,MAAMY,EAAWd,EAAe,KAAKb,EAA4B,KAAK,QAAQ,MAAM,EAEpF,OAAOa,EAAe,KAAKE,EAAU,KAAK,QAAQ,MAAM,EAAE,OAAOL,GAAW,CAACiB,EAAS,SAASjB,CAAO,CAAC,CAC3G,CAEE,0BAA0BmB,EAAcC,EAAQ,CAC9C,GAAKD,EAAa,OAIlB,UAAWnB,KAAWmB,EACpBnB,EAAQ,UAAU,OAAOX,EAAsB,CAAC+B,CAAM,EACtDpB,EAAQ,aAAa,gBAAiBoB,CAAM,CAElD,CAGE,OAAO,gBAAgBnB,EAAQ,CAC7B,MAAMoB,EAAU,CAAA,EAChB,OAAI,OAAOpB,GAAW,UAAY,YAAY,KAAKA,CAAM,IACvDoB,EAAQ,OAAS,IAGZ,KAAK,KAAK,UAAY,CAC3B,MAAMC,EAAOxB,EAAS,oBAAoB,KAAMuB,CAAO,EAEvD,GAAI,OAAOpB,GAAW,SAAU,CAC9B,GAAI,OAAOqB,EAAKrB,CAAM,GAAM,YAC1B,MAAM,IAAI,UAAU,oBAAoBA,CAAM,GAAG,EAGnDqB,EAAKrB,CAAM,EAAC,CACpB,CACK,CAAA,CACL,CACA,CAMAQ,EAAa,GAAG,SAAUxB,EAAsBU,EAAsB,SAAU4B,EAAO,EAEjFA,EAAM,OAAO,UAAY,KAAQA,EAAM,gBAAkBA,EAAM,eAAe,UAAY,MAC5FA,EAAM,eAAc,EAGtB,UAAWvB,KAAWG,EAAe,gCAAgC,IAAI,EACvEL,EAAS,oBAAoBE,EAAS,CAAE,OAAQ,EAAO,CAAA,EAAE,OAAM,CAEnE,CAAC,EAMDwB,EAAmB1B,CAAQ", "x_google_ignoreList": [0]}