{"version": 3, "file": "scrollspy.Cj27ihKs.js", "sources": ["../../../node_modules/.pnpm/bootstrap@5.3.6_@popperjs+core@2.11.8/node_modules/bootstrap/js/src/scrollspy.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin, getElement, isDisabled, isVisible\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_TARGET_LINKS = '[href]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_NAV_ITEMS} > ${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: null, // TODO: v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: '0px 0px -25%',\n  smoothScroll: false,\n  target: null,\n  threshold: [0.1, 0.5, 1]\n}\n\nconst DefaultType = {\n  offset: '(number|null)', // TODO v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: 'string',\n  smoothScroll: 'boolean',\n  target: 'element',\n  threshold: 'array'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    // this._element is the observablesContainer and config.target the menu links wrapper\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n    this._rootElement = getComputedStyle(this._element).overflowY === 'visible' ? null : this._element\n    this._activeTarget = null\n    this._observer = null\n    this._previousScrollData = {\n      visibleEntryTop: 0,\n      parentScrollTop: 0\n    }\n    this.refresh() // initialize\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  refresh() {\n    this._initializeTargetsAndObservables()\n    this._maybeEnableSmoothScroll()\n\n    if (this._observer) {\n      this._observer.disconnect()\n    } else {\n      this._observer = this._getNewObserver()\n    }\n\n    for (const section of this._observableSections.values()) {\n      this._observer.observe(section)\n    }\n  }\n\n  dispose() {\n    this._observer.disconnect()\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    // TODO: on v6 target should be given explicitly & remove the {target: 'ss-target'} case\n    config.target = getElement(config.target) || document.body\n\n    // TODO: v6 Only for backwards compatibility reasons. Use rootMargin only\n    config.rootMargin = config.offset ? `${config.offset}px 0px -30%` : config.rootMargin\n\n    if (typeof config.threshold === 'string') {\n      config.threshold = config.threshold.split(',').map(value => Number.parseFloat(value))\n    }\n\n    return config\n  }\n\n  _maybeEnableSmoothScroll() {\n    if (!this._config.smoothScroll) {\n      return\n    }\n\n    // unregister any previous listeners\n    EventHandler.off(this._config.target, EVENT_CLICK)\n\n    EventHandler.on(this._config.target, EVENT_CLICK, SELECTOR_TARGET_LINKS, event => {\n      const observableSection = this._observableSections.get(event.target.hash)\n      if (observableSection) {\n        event.preventDefault()\n        const root = this._rootElement || window\n        const height = observableSection.offsetTop - this._element.offsetTop\n        if (root.scrollTo) {\n          root.scrollTo({ top: height, behavior: 'smooth' })\n          return\n        }\n\n        // Chrome 60 doesn't support `scrollTo`\n        root.scrollTop = height\n      }\n    })\n  }\n\n  _getNewObserver() {\n    const options = {\n      root: this._rootElement,\n      threshold: this._config.threshold,\n      rootMargin: this._config.rootMargin\n    }\n\n    return new IntersectionObserver(entries => this._observerCallback(entries), options)\n  }\n\n  // The logic of selection\n  _observerCallback(entries) {\n    const targetElement = entry => this._targetLinks.get(`#${entry.target.id}`)\n    const activate = entry => {\n      this._previousScrollData.visibleEntryTop = entry.target.offsetTop\n      this._process(targetElement(entry))\n    }\n\n    const parentScrollTop = (this._rootElement || document.documentElement).scrollTop\n    const userScrollsDown = parentScrollTop >= this._previousScrollData.parentScrollTop\n    this._previousScrollData.parentScrollTop = parentScrollTop\n\n    for (const entry of entries) {\n      if (!entry.isIntersecting) {\n        this._activeTarget = null\n        this._clearActiveClass(targetElement(entry))\n\n        continue\n      }\n\n      const entryIsLowerThanPrevious = entry.target.offsetTop >= this._previousScrollData.visibleEntryTop\n      // if we are scrolling down, pick the bigger offsetTop\n      if (userScrollsDown && entryIsLowerThanPrevious) {\n        activate(entry)\n        // if parent isn't scrolled, let's keep the first visible item, breaking the iteration\n        if (!parentScrollTop) {\n          return\n        }\n\n        continue\n      }\n\n      // if we are scrolling up, pick the smallest offsetTop\n      if (!userScrollsDown && !entryIsLowerThanPrevious) {\n        activate(entry)\n      }\n    }\n  }\n\n  _initializeTargetsAndObservables() {\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n\n    const targetLinks = SelectorEngine.find(SELECTOR_TARGET_LINKS, this._config.target)\n\n    for (const anchor of targetLinks) {\n      // ensure that the anchor has an id and is not disabled\n      if (!anchor.hash || isDisabled(anchor)) {\n        continue\n      }\n\n      const observableSection = SelectorEngine.findOne(decodeURI(anchor.hash), this._element)\n\n      // ensure that the observableSection exists & is visible\n      if (isVisible(observableSection)) {\n        this._targetLinks.set(decodeURI(anchor.hash), anchor)\n        this._observableSections.set(anchor.hash, observableSection)\n      }\n    }\n  }\n\n  _process(target) {\n    if (this._activeTarget === target) {\n      return\n    }\n\n    this._clearActiveClass(this._config.target)\n    this._activeTarget = target\n    target.classList.add(CLASS_NAME_ACTIVE)\n    this._activateParents(target)\n\n    EventHandler.trigger(this._element, EVENT_ACTIVATE, { relatedTarget: target })\n  }\n\n  _activateParents(target) {\n    // Activate dropdown parents\n    if (target.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, target.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n      return\n    }\n\n    for (const listGroup of SelectorEngine.parents(target, SELECTOR_NAV_LIST_GROUP)) {\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      for (const item of SelectorEngine.prev(listGroup, SELECTOR_LINK_ITEMS)) {\n        item.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _clearActiveClass(parent) {\n    parent.classList.remove(CLASS_NAME_ACTIVE)\n\n    const activeNodes = SelectorEngine.find(`${SELECTOR_TARGET_LINKS}.${CLASS_NAME_ACTIVE}`, parent)\n    for (const node of activeNodes) {\n      node.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const spy of SelectorEngine.find(SELECTOR_DATA_SPY)) {\n    ScrollSpy.getOrCreateInstance(spy)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "EVENT_ACTIVATE", "EVENT_CLICK", "EVENT_LOAD_DATA_API", "CLASS_NAME_DROPDOWN_ITEM", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_SPY", "SELECTOR_TARGET_LINKS", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_LINK_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "<PERSON><PERSON><PERSON>", "DefaultType", "ScrollSpy", "BaseComponent", "element", "config", "section", "getElement", "value", "EventHandler", "event", "observableSection", "root", "height", "options", "entries", "targetElement", "entry", "activate", "parentScrollTop", "userScrollsDown", "entryIsLowerThanPrevious", "targetLinks", "SelectorEngine", "anchor", "isDisabled", "isVisible", "target", "listGroup", "item", "parent", "activeNodes", "node", "data", "spy", "defineJQueryPlugin"], "mappings": "6GAkBA,MAAMA,EAAO,YACPC,EAAW,eACXC,EAAY,IAAID,CAAQ,GACxBE,EAAe,YAEfC,EAAiB,WAAWF,CAAS,GACrCG,EAAc,QAAQH,CAAS,GAC/BI,EAAsB,OAAOJ,CAAS,GAAGC,CAAY,GAErDI,EAA2B,gBAC3BC,EAAoB,SAEpBC,EAAoB,yBACpBC,EAAwB,SACxBC,EAA0B,oBAC1BC,EAAqB,YACrBC,EAAqB,YACrBC,EAAsB,mBACtBC,EAAsB,GAAGH,CAAkB,KAAKC,CAAkB,MAAMD,CAAkB,KAAKE,CAAmB,GAClHE,EAAoB,YACpBC,EAA2B,mBAE3BC,EAAU,CACd,OAAQ,KACR,WAAY,eACZ,aAAc,GACd,OAAQ,KACR,UAAW,CAAC,GAAK,GAAK,CAAC,CACzB,EAEMC,EAAc,CAClB,OAAQ,gBACR,WAAY,SACZ,aAAc,UACd,OAAQ,UACR,UAAW,OACb,EAMA,MAAMC,UAAkBC,CAAc,CACpC,YAAYC,EAASC,EAAQ,CAC3B,MAAMD,EAASC,CAAM,EAGrB,KAAK,aAAe,IAAI,IACxB,KAAK,oBAAsB,IAAI,IAC/B,KAAK,aAAe,iBAAiB,KAAK,QAAQ,EAAE,YAAc,UAAY,KAAO,KAAK,SAC1F,KAAK,cAAgB,KACrB,KAAK,UAAY,KACjB,KAAK,oBAAsB,CACzB,gBAAiB,EACjB,gBAAiB,CACvB,EACI,KAAK,QAAS,CAClB,CAGE,WAAW,SAAU,CACnB,OAAOL,CACX,CAEE,WAAW,aAAc,CACvB,OAAOC,CACX,CAEE,WAAW,MAAO,CAChB,OAAOnB,CACX,CAGE,SAAU,CACR,KAAK,iCAAgC,EACrC,KAAK,yBAAwB,EAEzB,KAAK,UACP,KAAK,UAAU,WAAU,EAEzB,KAAK,UAAY,KAAK,gBAAe,EAGvC,UAAWwB,KAAW,KAAK,oBAAoB,OAAM,EACnD,KAAK,UAAU,QAAQA,CAAO,CAEpC,CAEE,SAAU,CACR,KAAK,UAAU,WAAU,EACzB,MAAM,QAAO,CACjB,CAGE,kBAAkBD,EAAQ,CAExB,OAAAA,EAAO,OAASE,EAAWF,EAAO,MAAM,GAAK,SAAS,KAGtDA,EAAO,WAAaA,EAAO,OAAS,GAAGA,EAAO,MAAM,cAAgBA,EAAO,WAEvE,OAAOA,EAAO,WAAc,WAC9BA,EAAO,UAAYA,EAAO,UAAU,MAAM,GAAG,EAAE,IAAIG,GAAS,OAAO,WAAWA,CAAK,CAAC,GAG/EH,CACX,CAEE,0BAA2B,CACpB,KAAK,QAAQ,eAKlBI,EAAa,IAAI,KAAK,QAAQ,OAAQtB,CAAW,EAEjDsB,EAAa,GAAG,KAAK,QAAQ,OAAQtB,EAAaK,EAAuBkB,GAAS,CAChF,MAAMC,EAAoB,KAAK,oBAAoB,IAAID,EAAM,OAAO,IAAI,EACxE,GAAIC,EAAmB,CACrBD,EAAM,eAAc,EACpB,MAAME,EAAO,KAAK,cAAgB,OAC5BC,EAASF,EAAkB,UAAY,KAAK,SAAS,UAC3D,GAAIC,EAAK,SAAU,CACjBA,EAAK,SAAS,CAAE,IAAKC,EAAQ,SAAU,QAAU,CAAA,EACjD,MACV,CAGQD,EAAK,UAAYC,CACzB,CACK,CAAA,EACL,CAEE,iBAAkB,CAChB,MAAMC,EAAU,CACd,KAAM,KAAK,aACX,UAAW,KAAK,QAAQ,UACxB,WAAY,KAAK,QAAQ,UAC/B,EAEI,OAAO,IAAI,qBAAqBC,GAAW,KAAK,kBAAkBA,CAAO,EAAGD,CAAO,CACvF,CAGE,kBAAkBC,EAAS,CACzB,MAAMC,EAAgBC,GAAS,KAAK,aAAa,IAAI,IAAIA,EAAM,OAAO,EAAE,EAAE,EACpEC,EAAWD,GAAS,CACxB,KAAK,oBAAoB,gBAAkBA,EAAM,OAAO,UACxD,KAAK,SAASD,EAAcC,CAAK,CAAC,CACxC,EAEUE,GAAmB,KAAK,cAAgB,SAAS,iBAAiB,UAClEC,EAAkBD,GAAmB,KAAK,oBAAoB,gBACpE,KAAK,oBAAoB,gBAAkBA,EAE3C,UAAWF,KAASF,EAAS,CAC3B,GAAI,CAACE,EAAM,eAAgB,CACzB,KAAK,cAAgB,KACrB,KAAK,kBAAkBD,EAAcC,CAAK,CAAC,EAE3C,QACR,CAEM,MAAMI,EAA2BJ,EAAM,OAAO,WAAa,KAAK,oBAAoB,gBAEpF,GAAIG,GAAmBC,EAA0B,CAG/C,GAFAH,EAASD,CAAK,EAEV,CAACE,EACH,OAGF,QACR,CAGU,CAACC,GAAmB,CAACC,GACvBH,EAASD,CAAK,CAEtB,CACA,CAEE,kCAAmC,CACjC,KAAK,aAAe,IAAI,IACxB,KAAK,oBAAsB,IAAI,IAE/B,MAAMK,EAAcC,EAAe,KAAK/B,EAAuB,KAAK,QAAQ,MAAM,EAElF,UAAWgC,KAAUF,EAAa,CAEhC,GAAI,CAACE,EAAO,MAAQC,EAAWD,CAAM,EACnC,SAGF,MAAMb,EAAoBY,EAAe,QAAQ,UAAUC,EAAO,IAAI,EAAG,KAAK,QAAQ,EAGlFE,EAAUf,CAAiB,IAC7B,KAAK,aAAa,IAAI,UAAUa,EAAO,IAAI,EAAGA,CAAM,EACpD,KAAK,oBAAoB,IAAIA,EAAO,KAAMb,CAAiB,EAEnE,CACA,CAEE,SAASgB,EAAQ,CACX,KAAK,gBAAkBA,IAI3B,KAAK,kBAAkB,KAAK,QAAQ,MAAM,EAC1C,KAAK,cAAgBA,EACrBA,EAAO,UAAU,IAAIrC,CAAiB,EACtC,KAAK,iBAAiBqC,CAAM,EAE5BlB,EAAa,QAAQ,KAAK,SAAUvB,EAAgB,CAAE,cAAeyC,CAAQ,CAAA,EACjF,CAEE,iBAAiBA,EAAQ,CAEvB,GAAIA,EAAO,UAAU,SAAStC,CAAwB,EAAG,CACvDkC,EAAe,QAAQxB,EAA0B4B,EAAO,QAAQ7B,CAAiB,CAAC,EAC/E,UAAU,IAAIR,CAAiB,EAClC,MACN,CAEI,UAAWsC,KAAaL,EAAe,QAAQI,EAAQlC,CAAuB,EAG5E,UAAWoC,KAAQN,EAAe,KAAKK,EAAW/B,CAAmB,EACnEgC,EAAK,UAAU,IAAIvC,CAAiB,CAG5C,CAEE,kBAAkBwC,EAAQ,CACxBA,EAAO,UAAU,OAAOxC,CAAiB,EAEzC,MAAMyC,EAAcR,EAAe,KAAK,GAAG/B,CAAqB,IAAIF,CAAiB,GAAIwC,CAAM,EAC/F,UAAWE,KAAQD,EACjBC,EAAK,UAAU,OAAO1C,CAAiB,CAE7C,CAGE,OAAO,gBAAgBe,EAAQ,CAC7B,OAAO,KAAK,KAAK,UAAY,CAC3B,MAAM4B,EAAO/B,EAAU,oBAAoB,KAAMG,CAAM,EAEvD,GAAI,OAAOA,GAAW,SAItB,IAAI4B,EAAK5B,CAAM,IAAM,QAAaA,EAAO,WAAW,GAAG,GAAKA,IAAW,cACrE,MAAM,IAAI,UAAU,oBAAoBA,CAAM,GAAG,EAGnD4B,EAAK5B,CAAM,EAAC,EACb,CAAA,CACL,CACA,CAMAI,EAAa,GAAG,OAAQrB,EAAqB,IAAM,CACjD,UAAW8C,KAAOX,EAAe,KAAKhC,CAAiB,EACrDW,EAAU,oBAAoBgC,CAAG,CAErC,CAAC,EAMDC,EAAmBjC,CAAS", "x_google_ignoreList": [0]}