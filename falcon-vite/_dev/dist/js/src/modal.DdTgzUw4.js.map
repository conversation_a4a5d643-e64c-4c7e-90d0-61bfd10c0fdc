{"version": 3, "file": "modal.DdTgzUw4.js", "sources": ["../../../node_modules/.pnpm/bootstrap@5.3.6_@popperjs+core@2.11.8/node_modules/bootstrap/js/src/util/backdrop.js", "../../../node_modules/.pnpm/bootstrap@5.3.6_@popperjs+core@2.11.8/node_modules/bootstrap/js/src/util/focustrap.js", "../../../node_modules/.pnpm/bootstrap@5.3.6_@popperjs+core@2.11.8/node_modules/bootstrap/js/src/util/scrollbar.js", "../../../node_modules/.pnpm/bootstrap@5.3.6_@popperjs+core@2.11.8/node_modules/bootstrap/js/src/modal.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport Config from './config.js'\nimport {\n  execute, executeAfterTransition, getElement, reflow\n} from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nconst Default = {\n  className: 'modal-backdrop',\n  clickCallback: null,\n  isAnimated: false,\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  rootElement: 'body' // give the choice to place backdrop under different elements\n}\n\nconst DefaultType = {\n  className: 'string',\n  clickCallback: '(function|null)',\n  isAnimated: 'boolean',\n  isVisible: 'boolean',\n  rootElement: '(element|string)'\n}\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    const element = this._getElement()\n    if (this._config.isAnimated) {\n      reflow(element)\n    }\n\n    element.classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    const element = this._getElement()\n    this._config.rootElement.append(element)\n\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nconst Default = {\n  autofocus: true,\n  trapElement: null // The element to trap focus inside of\n}\n\nconst DefaultType = {\n  autofocus: 'boolean',\n  trapElement: 'element'\n}\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return\n    }\n\n    if (this._config.autofocus) {\n      this._config.trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const { trapElement } = this._config\n\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Manipulator from '../dom/manipulator.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport { isElement } from './index.js'\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\nconst PROPERTY_PADDING = 'padding-right'\nconst PROPERTY_MARGIN = 'margin-right'\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN)\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProperty)\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty)\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty)\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty)\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty)\n        return\n      }\n\n      Manipulator.removeDataAttribute(element, styleProperty)\n      element.style.setProperty(styleProperty, value)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n      return\n    }\n\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel)\n    }\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport Backdrop from './util/backdrop.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport FocusTrap from './util/focustrap.js'\nimport {\n  defineJQueryPlugin, isRTL, isVisible, reflow\n} from './util/index.js'\nimport ScrollBarHelper from './util/scrollbar.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\nconst Default = {\n  backdrop: true,\n  focus: true,\n  keyboard: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  focus: 'boolean',\n  keyboard: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._isTransitioning = true\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._backdrop.show(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    this._isTransitioning = true\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    this._queueCallback(() => this._hideModal(), this._element, this._isAnimated())\n  }\n\n  dispose() {\n    EventHandler.off(window, EVENT_KEY)\n    EventHandler.off(this._dialog, EVENT_KEY)\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value,\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _showElement(relatedTarget) {\n    // try to append dynamic modal\n    if (!document.body.contains(this._element)) {\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, this._isAnimated())\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        this.hide()\n        return\n      }\n\n      this._triggerBackdropTransition()\n    })\n\n    EventHandler.on(window, EVENT_RESIZE, () => {\n      if (this._isShown && !this._isTransitioning) {\n        this._adjustDialog()\n      }\n    })\n\n    EventHandler.on(this._element, EVENT_MOUSEDOWN_DISMISS, event => {\n      // a bad trick to segregate clicks that may start inside dialog but end outside, and avoid listen to scrollbar clicks\n      EventHandler.one(this._element, EVENT_CLICK_DISMISS, event2 => {\n        if (this._element !== event.target || this._element !== event2.target) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n          return\n        }\n\n        if (this._config.backdrop) {\n          this.hide()\n        }\n      })\n    })\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const initialOverflowY = this._element.style.overflowY\n    // return if the following background transition hasn't yet completed\n    if (initialOverflowY === 'hidden' || this._element.classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      this._queueCallback(() => {\n        this._element.style.overflowY = initialOverflowY\n      }, this._dialog)\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  /**\n   * The following methods are used to handle overflowing modals\n   */\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if (isBodyOverflowing && !isModalOverflowing) {\n      const property = isRTL() ? 'paddingLeft' : 'paddingRight'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n\n    if (!isBodyOverflowing && isModalOverflowing) {\n      const property = isRTL() ? 'paddingRight' : 'paddingLeft'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking modal toggler while another one is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen) {\n    Modal.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n"], "names": ["NAME", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "EVENT_MOUSEDOWN", "<PERSON><PERSON><PERSON>", "DefaultType", "Backdrop", "Config", "config", "callback", "execute", "element", "reflow", "EventHandler", "backdrop", "getElement", "executeAfterTransition", "DATA_KEY", "EVENT_KEY", "EVENT_FOCUSIN", "EVENT_KEYDOWN_TAB", "TAB_KEY", "TAB_NAV_FORWARD", "TAB_NAV_BACKWARD", "FocusTrap", "event", "trapElement", "elements", "SelectorEngine", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "PROPERTY_PADDING", "PROPERTY_MARGIN", "ScrollBarHelper", "documentWidth", "width", "calculatedValue", "selector", "styleProperty", "scrollbarWidth", "manipulationCallBack", "actualValue", "Manipulator", "value", "callBack", "isElement", "sel", "DATA_API_KEY", "ESCAPE_KEY", "EVENT_HIDE", "EVENT_HIDE_PREVENTED", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_CLICK_DATA_API", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "OPEN_SELECTOR", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_TOGGLE", "Modal", "BaseComponent", "relatedTarget", "modalBody", "transitionComplete", "event2", "isModalOverflowing", "initialOverflowY", "isBodyOverflowing", "property", "isRTL", "data", "target", "showEvent", "isVisible", "alreadyOpen", "enableDismissTrigger", "defineJQueryPlugin"], "mappings": "kOAiBA,MAAMA,EAAO,WACPC,EAAkB,OAClBC,EAAkB,OAClBC,EAAkB,gBAAgBH,CAAI,GAEtCI,EAAU,CACd,UAAW,iBACX,cAAe,KACf,WAAY,GACZ,UAAW,GACX,YAAa,MACf,EAEMC,EAAc,CAClB,UAAW,SACX,cAAe,kBACf,WAAY,UACZ,UAAW,UACX,YAAa,kBACf,EAMA,MAAMC,UAAiBC,CAAO,CAC5B,YAAYC,EAAQ,CAClB,MAAK,EACL,KAAK,QAAU,KAAK,WAAWA,CAAM,EACrC,KAAK,YAAc,GACnB,KAAK,SAAW,IACpB,CAGE,WAAW,SAAU,CACnB,OAAOJ,CACX,CAEE,WAAW,aAAc,CACvB,OAAOC,CACX,CAEE,WAAW,MAAO,CAChB,OAAOL,CACX,CAGE,KAAKS,EAAU,CACb,GAAI,CAAC,KAAK,QAAQ,UAAW,CAC3BC,EAAQD,CAAQ,EAChB,MACN,CAEI,KAAK,QAAO,EAEZ,MAAME,EAAU,KAAK,YAAW,EAC5B,KAAK,QAAQ,YACfC,EAAOD,CAAO,EAGhBA,EAAQ,UAAU,IAAIT,CAAe,EAErC,KAAK,kBAAkB,IAAM,CAC3BQ,EAAQD,CAAQ,CACjB,CAAA,CACL,CAEE,KAAKA,EAAU,CACb,GAAI,CAAC,KAAK,QAAQ,UAAW,CAC3BC,EAAQD,CAAQ,EAChB,MACN,CAEI,KAAK,YAAW,EAAG,UAAU,OAAOP,CAAe,EAEnD,KAAK,kBAAkB,IAAM,CAC3B,KAAK,QAAO,EACZQ,EAAQD,CAAQ,CACjB,CAAA,CACL,CAEE,SAAU,CACH,KAAK,cAIVI,EAAa,IAAI,KAAK,SAAUV,CAAe,EAE/C,KAAK,SAAS,OAAM,EACpB,KAAK,YAAc,GACvB,CAGE,aAAc,CACZ,GAAI,CAAC,KAAK,SAAU,CAClB,MAAMW,EAAW,SAAS,cAAc,KAAK,EAC7CA,EAAS,UAAY,KAAK,QAAQ,UAC9B,KAAK,QAAQ,YACfA,EAAS,UAAU,IAAIb,CAAe,EAGxC,KAAK,SAAWa,CACtB,CAEI,OAAO,KAAK,QAChB,CAEE,kBAAkBN,EAAQ,CAExB,OAAAA,EAAO,YAAcO,EAAWP,EAAO,WAAW,EAC3CA,CACX,CAEE,SAAU,CACR,GAAI,KAAK,YACP,OAGF,MAAMG,EAAU,KAAK,YAAW,EAChC,KAAK,QAAQ,YAAY,OAAOA,CAAO,EAEvCE,EAAa,GAAGF,EAASR,EAAiB,IAAM,CAC9CO,EAAQ,KAAK,QAAQ,aAAa,CACnC,CAAA,EAED,KAAK,YAAc,EACvB,CAEE,kBAAkBD,EAAU,CAC1BO,EAAuBP,EAAU,KAAK,YAAW,EAAI,KAAK,QAAQ,UAAU,CAChF,CACA,CCrIA,MAAMT,EAAO,YACPiB,EAAW,eACXC,EAAY,IAAID,CAAQ,GACxBE,EAAgB,UAAUD,CAAS,GACnCE,EAAoB,cAAcF,CAAS,GAE3CG,EAAU,MACVC,EAAkB,UAClBC,EAAmB,WAEnBnB,EAAU,CACd,UAAW,GACX,YAAa,IACf,EAEMC,EAAc,CAClB,UAAW,UACX,YAAa,SACf,EAMA,MAAMmB,UAAkBjB,CAAO,CAC7B,YAAYC,EAAQ,CAClB,MAAK,EACL,KAAK,QAAU,KAAK,WAAWA,CAAM,EACrC,KAAK,UAAY,GACjB,KAAK,qBAAuB,IAChC,CAGE,WAAW,SAAU,CACnB,OAAOJ,CACX,CAEE,WAAW,aAAc,CACvB,OAAOC,CACX,CAEE,WAAW,MAAO,CAChB,OAAOL,CACX,CAGE,UAAW,CACL,KAAK,YAIL,KAAK,QAAQ,WACf,KAAK,QAAQ,YAAY,MAAK,EAGhCa,EAAa,IAAI,SAAUK,CAAS,EACpCL,EAAa,GAAG,SAAUM,EAAeM,GAAS,KAAK,eAAeA,CAAK,CAAC,EAC5EZ,EAAa,GAAG,SAAUO,EAAmBK,GAAS,KAAK,eAAeA,CAAK,CAAC,EAEhF,KAAK,UAAY,GACrB,CAEE,YAAa,CACN,KAAK,YAIV,KAAK,UAAY,GACjBZ,EAAa,IAAI,SAAUK,CAAS,EACxC,CAGE,eAAeO,EAAO,CACpB,KAAM,CAAE,YAAAC,CAAa,EAAG,KAAK,QAE7B,GAAID,EAAM,SAAW,UAAYA,EAAM,SAAWC,GAAeA,EAAY,SAASD,EAAM,MAAM,EAChG,OAGF,MAAME,EAAWC,EAAe,kBAAkBF,CAAW,EAEzDC,EAAS,SAAW,EACtBD,EAAY,MAAK,EACR,KAAK,uBAAyBH,EACvCI,EAASA,EAAS,OAAS,CAAC,EAAE,MAAK,EAEnCA,EAAS,CAAC,EAAE,MAAK,CAEvB,CAEE,eAAeF,EAAO,CAChBA,EAAM,MAAQJ,IAIlB,KAAK,qBAAuBI,EAAM,SAAWF,EAAmBD,EACpE,CACA,CCjGA,MAAMO,EAAyB,oDACzBC,EAA0B,cAC1BC,EAAmB,gBACnBC,EAAkB,eAMxB,MAAMC,CAAgB,CACpB,aAAc,CACZ,KAAK,SAAW,SAAS,IAC7B,CAGE,UAAW,CAET,MAAMC,EAAgB,SAAS,gBAAgB,YAC/C,OAAO,KAAK,IAAI,OAAO,WAAaA,CAAa,CACrD,CAEE,MAAO,CACL,MAAMC,EAAQ,KAAK,SAAQ,EAC3B,KAAK,iBAAgB,EAErB,KAAK,sBAAsB,KAAK,SAAUJ,EAAkBK,GAAmBA,EAAkBD,CAAK,EAEtG,KAAK,sBAAsBN,EAAwBE,EAAkBK,GAAmBA,EAAkBD,CAAK,EAC/G,KAAK,sBAAsBL,EAAyBE,EAAiBI,GAAmBA,EAAkBD,CAAK,CACnH,CAEE,OAAQ,CACN,KAAK,wBAAwB,KAAK,SAAU,UAAU,EACtD,KAAK,wBAAwB,KAAK,SAAUJ,CAAgB,EAC5D,KAAK,wBAAwBF,EAAwBE,CAAgB,EACrE,KAAK,wBAAwBD,EAAyBE,CAAe,CACzE,CAEE,eAAgB,CACd,OAAO,KAAK,WAAa,CAC7B,CAGE,kBAAmB,CACjB,KAAK,sBAAsB,KAAK,SAAU,UAAU,EACpD,KAAK,SAAS,MAAM,SAAW,QACnC,CAEE,sBAAsBK,EAAUC,EAAe7B,EAAU,CACvD,MAAM8B,EAAiB,KAAK,SAAQ,EAC9BC,EAAuB7B,GAAW,CACtC,GAAIA,IAAY,KAAK,UAAY,OAAO,WAAaA,EAAQ,YAAc4B,EACzE,OAGF,KAAK,sBAAsB5B,EAAS2B,CAAa,EACjD,MAAMF,EAAkB,OAAO,iBAAiBzB,CAAO,EAAE,iBAAiB2B,CAAa,EACvF3B,EAAQ,MAAM,YAAY2B,EAAe,GAAG7B,EAAS,OAAO,WAAW2B,CAAe,CAAC,CAAC,IAAI,CAClG,EAEI,KAAK,2BAA2BC,EAAUG,CAAoB,CAClE,CAEE,sBAAsB7B,EAAS2B,EAAe,CAC5C,MAAMG,EAAc9B,EAAQ,MAAM,iBAAiB2B,CAAa,EAC5DG,GACFC,EAAY,iBAAiB/B,EAAS2B,EAAeG,CAAW,CAEtE,CAEE,wBAAwBJ,EAAUC,EAAe,CAC/C,MAAME,EAAuB7B,GAAW,CACtC,MAAMgC,EAAQD,EAAY,iBAAiB/B,EAAS2B,CAAa,EAEjE,GAAIK,IAAU,KAAM,CAClBhC,EAAQ,MAAM,eAAe2B,CAAa,EAC1C,MACR,CAEMI,EAAY,oBAAoB/B,EAAS2B,CAAa,EACtD3B,EAAQ,MAAM,YAAY2B,EAAeK,CAAK,CACpD,EAEI,KAAK,2BAA2BN,EAAUG,CAAoB,CAClE,CAEE,2BAA2BH,EAAUO,EAAU,CAC7C,GAAIC,EAAUR,CAAQ,EAAG,CACvBO,EAASP,CAAQ,EACjB,MACN,CAEI,UAAWS,KAAOlB,EAAe,KAAKS,EAAU,KAAK,QAAQ,EAC3DO,EAASE,CAAG,CAElB,CACA,CCzFA,MAAM9C,EAAO,QACPiB,EAAW,WACXC,EAAY,IAAID,CAAQ,GACxB8B,GAAe,YACfC,GAAa,SAEbC,GAAa,OAAO/B,CAAS,GAC7BgC,GAAuB,gBAAgBhC,CAAS,GAChDiC,EAAe,SAASjC,CAAS,GACjCkC,EAAa,OAAOlC,CAAS,GAC7BmC,GAAc,QAAQnC,CAAS,GAC/BoC,GAAe,SAASpC,CAAS,GACjCqC,GAAsB,gBAAgBrC,CAAS,GAC/CsC,GAA0B,oBAAoBtC,CAAS,GACvDuC,GAAwB,kBAAkBvC,CAAS,GACnDwC,GAAuB,QAAQxC,CAAS,GAAG6B,EAAY,GAEvDY,EAAkB,aAClB1D,GAAkB,OAClBC,EAAkB,OAClB0D,EAAoB,eAEpBC,GAAgB,cAChBC,GAAkB,gBAClBC,GAAsB,cACtBC,GAAuB,2BAEvB5D,GAAU,CACd,SAAU,GACV,MAAO,GACP,SAAU,EACZ,EAEMC,GAAc,CAClB,SAAU,mBACV,MAAO,UACP,SAAU,SACZ,EAMA,MAAM4D,UAAcC,CAAc,CAChC,YAAYvD,EAASH,EAAQ,CAC3B,MAAMG,EAASH,CAAM,EAErB,KAAK,QAAUoB,EAAe,QAAQkC,GAAiB,KAAK,QAAQ,EACpE,KAAK,UAAY,KAAK,oBAAmB,EACzC,KAAK,WAAa,KAAK,qBAAoB,EAC3C,KAAK,SAAW,GAChB,KAAK,iBAAmB,GACxB,KAAK,WAAa,IAAI7B,EAEtB,KAAK,mBAAkB,CAC3B,CAGE,WAAW,SAAU,CACnB,OAAO7B,EACX,CAEE,WAAW,aAAc,CACvB,OAAOC,EACX,CAEE,WAAW,MAAO,CAChB,OAAOL,CACX,CAGE,OAAOmE,EAAe,CACpB,OAAO,KAAK,SAAW,KAAK,KAAI,EAAK,KAAK,KAAKA,CAAa,CAChE,CAEE,KAAKA,EAAe,CACd,KAAK,UAAY,KAAK,kBAIRtD,EAAa,QAAQ,KAAK,SAAUuC,EAAY,CAChE,cAAAe,CACD,CAAA,EAEa,mBAId,KAAK,SAAW,GAChB,KAAK,iBAAmB,GAExB,KAAK,WAAW,KAAI,EAEpB,SAAS,KAAK,UAAU,IAAIR,CAAe,EAE3C,KAAK,cAAa,EAElB,KAAK,UAAU,KAAK,IAAM,KAAK,aAAaQ,CAAa,CAAC,EAC9D,CAEE,MAAO,CACD,CAAC,KAAK,UAAY,KAAK,kBAITtD,EAAa,QAAQ,KAAK,SAAUoC,EAAU,EAElD,mBAId,KAAK,SAAW,GAChB,KAAK,iBAAmB,GACxB,KAAK,WAAW,WAAU,EAE1B,KAAK,SAAS,UAAU,OAAO/C,CAAe,EAE9C,KAAK,eAAe,IAAM,KAAK,WAAY,EAAE,KAAK,SAAU,KAAK,YAAa,CAAA,EAClF,CAEE,SAAU,CACRW,EAAa,IAAI,OAAQK,CAAS,EAClCL,EAAa,IAAI,KAAK,QAASK,CAAS,EAExC,KAAK,UAAU,QAAO,EACtB,KAAK,WAAW,WAAU,EAE1B,MAAM,QAAO,CACjB,CAEE,cAAe,CACb,KAAK,cAAa,CACtB,CAGE,qBAAsB,CACpB,OAAO,IAAIZ,EAAS,CAClB,UAAW,EAAQ,KAAK,QAAQ,SAChC,WAAY,KAAK,YAAW,CAC7B,CAAA,CACL,CAEE,sBAAuB,CACrB,OAAO,IAAIkB,EAAU,CACnB,YAAa,KAAK,QACnB,CAAA,CACL,CAEE,aAAa2C,EAAe,CAErB,SAAS,KAAK,SAAS,KAAK,QAAQ,GACvC,SAAS,KAAK,OAAO,KAAK,QAAQ,EAGpC,KAAK,SAAS,MAAM,QAAU,QAC9B,KAAK,SAAS,gBAAgB,aAAa,EAC3C,KAAK,SAAS,aAAa,aAAc,EAAI,EAC7C,KAAK,SAAS,aAAa,OAAQ,QAAQ,EAC3C,KAAK,SAAS,UAAY,EAE1B,MAAMC,EAAYxC,EAAe,QAAQmC,GAAqB,KAAK,OAAO,EACtEK,IACFA,EAAU,UAAY,GAGxBxD,EAAO,KAAK,QAAQ,EAEpB,KAAK,SAAS,UAAU,IAAIV,CAAe,EAE3C,MAAMmE,EAAqB,IAAM,CAC3B,KAAK,QAAQ,OACf,KAAK,WAAW,SAAQ,EAG1B,KAAK,iBAAmB,GACxBxD,EAAa,QAAQ,KAAK,SAAUwC,GAAa,CAC/C,cAAAc,CACD,CAAA,CACP,EAEI,KAAK,eAAeE,EAAoB,KAAK,QAAS,KAAK,YAAa,CAAA,CAC5E,CAEE,oBAAqB,CACnBxD,EAAa,GAAG,KAAK,SAAU4C,GAAuBhC,GAAS,CAC7D,GAAIA,EAAM,MAAQuB,GAIlB,IAAI,KAAK,QAAQ,SAAU,CACzB,KAAK,KAAI,EACT,MACR,CAEM,KAAK,2BAA0B,EAChC,CAAA,EAEDnC,EAAa,GAAG,OAAQyC,GAAc,IAAM,CACtC,KAAK,UAAY,CAAC,KAAK,kBACzB,KAAK,cAAa,CAErB,CAAA,EAEDzC,EAAa,GAAG,KAAK,SAAU2C,GAAyB/B,GAAS,CAE/DZ,EAAa,IAAI,KAAK,SAAU0C,GAAqBe,GAAU,CAC7D,GAAI,OAAK,WAAa7C,EAAM,QAAU,KAAK,WAAa6C,EAAO,QAI/D,IAAI,KAAK,QAAQ,WAAa,SAAU,CACtC,KAAK,2BAA0B,EAC/B,MACV,CAEY,KAAK,QAAQ,UACf,KAAK,KAAI,EAEZ,CAAA,CACF,CAAA,CACL,CAEE,YAAa,CACX,KAAK,SAAS,MAAM,QAAU,OAC9B,KAAK,SAAS,aAAa,cAAe,EAAI,EAC9C,KAAK,SAAS,gBAAgB,YAAY,EAC1C,KAAK,SAAS,gBAAgB,MAAM,EACpC,KAAK,iBAAmB,GAExB,KAAK,UAAU,KAAK,IAAM,CACxB,SAAS,KAAK,UAAU,OAAOX,CAAe,EAC9C,KAAK,kBAAiB,EACtB,KAAK,WAAW,MAAK,EACrB9C,EAAa,QAAQ,KAAK,SAAUsC,CAAY,CACjD,CAAA,CACL,CAEE,aAAc,CACZ,OAAO,KAAK,SAAS,UAAU,SAASlD,EAAe,CAC3D,CAEE,4BAA6B,CAE3B,GADkBY,EAAa,QAAQ,KAAK,SAAUqC,EAAoB,EAC5D,iBACZ,OAGF,MAAMqB,EAAqB,KAAK,SAAS,aAAe,SAAS,gBAAgB,aAC3EC,EAAmB,KAAK,SAAS,MAAM,UAEzCA,IAAqB,UAAY,KAAK,SAAS,UAAU,SAASZ,CAAiB,IAIlFW,IACH,KAAK,SAAS,MAAM,UAAY,UAGlC,KAAK,SAAS,UAAU,IAAIX,CAAiB,EAC7C,KAAK,eAAe,IAAM,CACxB,KAAK,SAAS,UAAU,OAAOA,CAAiB,EAChD,KAAK,eAAe,IAAM,CACxB,KAAK,SAAS,MAAM,UAAYY,CACjC,EAAE,KAAK,OAAO,CAChB,EAAE,KAAK,OAAO,EAEf,KAAK,SAAS,MAAK,EACvB,CAME,eAAgB,CACd,MAAMD,EAAqB,KAAK,SAAS,aAAe,SAAS,gBAAgB,aAC3EhC,EAAiB,KAAK,WAAW,SAAQ,EACzCkC,EAAoBlC,EAAiB,EAE3C,GAAIkC,GAAqB,CAACF,EAAoB,CAC5C,MAAMG,EAAWC,EAAO,EAAG,cAAgB,eAC3C,KAAK,SAAS,MAAMD,CAAQ,EAAI,GAAGnC,CAAc,IACvD,CAEI,GAAI,CAACkC,GAAqBF,EAAoB,CAC5C,MAAMG,EAAWC,EAAO,EAAG,eAAiB,cAC5C,KAAK,SAAS,MAAMD,CAAQ,EAAI,GAAGnC,CAAc,IACvD,CACA,CAEE,mBAAoB,CAClB,KAAK,SAAS,MAAM,YAAc,GAClC,KAAK,SAAS,MAAM,aAAe,EACvC,CAGE,OAAO,gBAAgB/B,EAAQ2D,EAAe,CAC5C,OAAO,KAAK,KAAK,UAAY,CAC3B,MAAMS,EAAOX,EAAM,oBAAoB,KAAMzD,CAAM,EAEnD,GAAI,OAAOA,GAAW,SAItB,IAAI,OAAOoE,EAAKpE,CAAM,GAAM,YAC1B,MAAM,IAAI,UAAU,oBAAoBA,CAAM,GAAG,EAGnDoE,EAAKpE,CAAM,EAAE2D,CAAa,EAC3B,CAAA,CACL,CACA,CAMAtD,EAAa,GAAG,SAAU6C,GAAsBM,GAAsB,SAAUvC,EAAO,CACrF,MAAMoD,EAASjD,EAAe,uBAAuB,IAAI,EAErD,CAAC,IAAK,MAAM,EAAE,SAAS,KAAK,OAAO,GACrCH,EAAM,eAAc,EAGtBZ,EAAa,IAAIgE,EAAQzB,EAAY0B,GAAa,CAC5CA,EAAU,kBAKdjE,EAAa,IAAIgE,EAAQ1B,EAAc,IAAM,CACvC4B,EAAU,IAAI,GAChB,KAAK,MAAK,CAEb,CAAA,CACF,CAAA,EAGD,MAAMC,EAAcpD,EAAe,QAAQiC,EAAa,EACpDmB,GACFf,EAAM,YAAYe,CAAW,EAAE,KAAI,EAGxBf,EAAM,oBAAoBY,CAAM,EAExC,OAAO,IAAI,CAClB,CAAC,EAEDI,EAAqBhB,CAAK,EAM1BiB,EAAmBjB,CAAK", "x_google_ignoreList": [0, 1, 2, 3]}