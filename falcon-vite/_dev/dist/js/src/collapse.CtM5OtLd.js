import{B as E,S as i,E as a,r as u,g as p,d as A}from"../selector-engine/selector-engine.DUbyXJpR.js";const C="collapse",S="bs.collapse",h=`.${S}`,L=".data-api",T=`show${h}`,N=`shown${h}`,D=`hide${h}`,w=`hidden${h}`,y=`click${h}${L}`,m="show",o="collapse",g="collapsing",I="collapsed",v=`:scope .${o} .${o}`,O="collapse-horizontal",$="width",H="height",M=".collapse.show, .collapse.collapsing",f='[data-bs-toggle="collapse"]',P={parent:null,toggle:!0},F={parent:"(null|element)",toggle:"boolean"};class c extends E{constructor(e,s){super(e,s),this._isTransitioning=!1,this._triggerArray=[];const t=i.find(f);for(const n of t){const r=i.getSelectorFromElement(n),d=i.find(r).filter(l=>l===this._element);r!==null&&d.length&&this._triggerArray.push(n)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return P}static get DefaultType(){return F}static get NAME(){return C}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(M).filter(l=>l!==this._element).map(l=>c.getOrCreateInstance(l,{toggle:!1}))),e.length&&e[0]._isTransitioning||a.trigger(this._element,T).defaultPrevented)return;for(const l of e)l.hide();const t=this._getDimension();this._element.classList.remove(o),this._element.classList.add(g),this._element.style[t]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const n=()=>{this._isTransitioning=!1,this._element.classList.remove(g),this._element.classList.add(o,m),this._element.style[t]="",a.trigger(this._element,N)},d=`scroll${t[0].toUpperCase()+t.slice(1)}`;this._queueCallback(n,this._element,!0),this._element.style[t]=`${this._element[d]}px`}hide(){if(this._isTransitioning||!this._isShown()||a.trigger(this._element,D).defaultPrevented)return;const s=this._getDimension();this._element.style[s]=`${this._element.getBoundingClientRect()[s]}px`,u(this._element),this._element.classList.add(g),this._element.classList.remove(o,m);for(const n of this._triggerArray){const r=i.getElementFromSelector(n);r&&!this._isShown(r)&&this._addAriaAndCollapsedClass([n],!1)}this._isTransitioning=!0;const t=()=>{this._isTransitioning=!1,this._element.classList.remove(g),this._element.classList.add(o),a.trigger(this._element,w)};this._element.style[s]="",this._queueCallback(t,this._element,!0)}_isShown(e=this._element){return e.classList.contains(m)}_configAfterMerge(e){return e.toggle=!!e.toggle,e.parent=p(e.parent),e}_getDimension(){return this._element.classList.contains(O)?$:H}_initializeChildren(){if(!this._config.parent)return;const e=this._getFirstLevelChildren(f);for(const s of e){const t=i.getElementFromSelector(s);t&&this._addAriaAndCollapsedClass([s],this._isShown(t))}}_getFirstLevelChildren(e){const s=i.find(v,this._config.parent);return i.find(e,this._config.parent).filter(t=>!s.includes(t))}_addAriaAndCollapsedClass(e,s){if(e.length)for(const t of e)t.classList.toggle(I,!s),t.setAttribute("aria-expanded",s)}static jQueryInterface(e){const s={};return typeof e=="string"&&/show|hide/.test(e)&&(s.toggle=!1),this.each(function(){const t=c.getOrCreateInstance(this,s);if(typeof e=="string"){if(typeof t[e]=="undefined")throw new TypeError(`No method named "${e}"`);t[e]()}})}}a.on(document,y,f,function(_){(_.target.tagName==="A"||_.delegateTarget&&_.delegateTarget.tagName==="A")&&_.preventDefault();for(const e of i.getMultipleElementsFromSelector(this))c.getOrCreateInstance(e,{toggle:!1}).toggle()});A(c);export{c as default};
//# sourceMappingURL=collapse.CtM5OtLd.js.map
