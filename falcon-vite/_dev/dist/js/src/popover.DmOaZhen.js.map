{"version": 3, "file": "popover.DmOaZhen.js", "sources": ["../../../node_modules/.pnpm/bootstrap@5.3.6_@popperjs+core@2.11.8/node_modules/bootstrap/js/src/popover.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Tooltip from './tooltip.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  content: '',\n  offset: [0, 8],\n  placement: 'right',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n    '<div class=\"popover-arrow\"></div>' +\n    '<h3 class=\"popover-header\"></h3>' +\n    '<div class=\"popover-body\"></div>' +\n    '</div>',\n  trigger: 'click'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(null|string|element|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Overrides\n  _isWithContent() {\n    return this._getTitle() || this._getContent()\n  }\n\n  // Private\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TITLE]: this._getTitle(),\n      [SELECTOR_CONTENT]: this._getContent()\n    }\n  }\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n"], "names": ["NAME", "SELECTOR_TITLE", "SELECTOR_CONTENT", "<PERSON><PERSON><PERSON>", "__spreadProps", "__spreadValues", "<PERSON><PERSON><PERSON>", "DefaultType", "Popover", "config", "data", "defineJQueryPlugin"], "mappings": "wjBAcA,MAAMA,EAAO,UAEPC,EAAiB,kBACjBC,EAAmB,gBAEnBC,EAAUC,EAAAC,EAAA,GACXC,EAAQ,SADG,CAEd,QAAS,GACT,OAAQ,CAAC,EAAG,CAAC,EACb,UAAW,QACX,SAAU,8IAKV,QAAS,OACX,GAEMC,EAAcH,EAAAC,EAAA,GACfC,EAAQ,aADO,CAElB,QAAS,gCACX,GAMA,MAAME,UAAgBF,CAAQ,CAE5B,WAAW,SAAU,CACnB,OAAOH,CACX,CAEE,WAAW,aAAc,CACvB,OAAOI,CACX,CAEE,WAAW,MAAO,CAChB,OAAOP,CACX,CAGE,gBAAiB,CACf,OAAO,KAAK,aAAe,KAAK,YAAW,CAC/C,CAGE,wBAAyB,CACvB,MAAO,CACL,CAACC,CAAc,EAAG,KAAK,UAAW,EAClC,CAACC,CAAgB,EAAG,KAAK,YAAW,CAC1C,CACA,CAEE,aAAc,CACZ,OAAO,KAAK,yBAAyB,KAAK,QAAQ,OAAO,CAC7D,CAGE,OAAO,gBAAgBO,EAAQ,CAC7B,OAAO,KAAK,KAAK,UAAY,CAC3B,MAAMC,EAAOF,EAAQ,oBAAoB,KAAMC,CAAM,EAErD,GAAI,OAAOA,GAAW,SAItB,IAAI,OAAOC,EAAKD,CAAM,GAAM,YAC1B,MAAM,IAAI,UAAU,oBAAoBA,CAAM,GAAG,EAGnDC,EAAKD,CAAM,EAAC,EACb,CAAA,CACL,CACA,CAMAE,EAAmBH,CAAO", "x_google_ignoreList": [0]}