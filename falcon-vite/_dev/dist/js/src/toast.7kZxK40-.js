import{B as c,E as s,r as l,d as _}from"../selector-engine/selector-engine.DUbyXJpR.js";import{e as u}from"../component-functions/component-functions.MEtW7SSs.js";const m="toast",d="bs.toast",i=`.${d}`,E=`mouseover${i}`,f=`mouseout${i}`,T=`focusin${i}`,S=`focusout${i}`,N=`hide${i}`,g=`hidden${i}`,y=`show${i}`,p=`shown${i}`,I="fade",h="hide",n="show",o="showing",b={animation:"boolean",autohide:"boolean",delay:"number"},L={animation:!0,autohide:!0,delay:5e3};class a extends c{constructor(e,t){super(e,t),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return L}static get DefaultType(){return b}static get NAME(){return m}show(){if(s.trigger(this._element,y).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add(I);const t=()=>{this._element.classList.remove(o),s.trigger(this._element,p),this._maybeScheduleHide()};this._element.classList.remove(h),l(this._element),this._element.classList.add(n,o),this._queueCallback(t,this._element,this._config.animation)}hide(){if(!this.isShown()||s.trigger(this._element,N).defaultPrevented)return;const t=()=>{this._element.classList.add(h),this._element.classList.remove(o,n),s.trigger(this._element,g)};this._element.classList.add(o),this._queueCallback(t,this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(n),super.dispose()}isShown(){return this._element.classList.contains(n)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(e,t){switch(e.type){case"mouseover":case"mouseout":{this._hasMouseInteraction=t;break}case"focusin":case"focusout":{this._hasKeyboardInteraction=t;break}}if(t){this._clearTimeout();return}const r=e.relatedTarget;this._element===r||this._element.contains(r)||this._maybeScheduleHide()}_setListeners(){s.on(this._element,E,e=>this._onInteraction(e,!0)),s.on(this._element,f,e=>this._onInteraction(e,!1)),s.on(this._element,T,e=>this._onInteraction(e,!0)),s.on(this._element,S,e=>this._onInteraction(e,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each(function(){const t=a.getOrCreateInstance(this,e);if(typeof e=="string"){if(typeof t[e]=="undefined")throw new TypeError(`No method named "${e}"`);t[e](this)}})}}u(a);_(a);export{a as default};
//# sourceMappingURL=toast.7kZxK40-.js.map
