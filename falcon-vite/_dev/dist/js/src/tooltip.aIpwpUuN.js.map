{"version": 3, "file": "tooltip.aIpwpUuN.js", "sources": ["../../../node_modules/.pnpm/bootstrap@5.3.6_@popperjs+core@2.11.8/node_modules/bootstrap/js/src/util/sanitizer.js", "../../../node_modules/.pnpm/bootstrap@5.3.6_@popperjs+core@2.11.8/node_modules/bootstrap/js/src/util/template-factory.js", "../../../node_modules/.pnpm/bootstrap@5.3.6_@popperjs+core@2.11.8/node_modules/bootstrap/js/src/tooltip.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n// js-docs-start allow-list\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  dd: [],\n  div: [],\n  dl: [],\n  dt: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n// js-docs-end allow-list\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\n/**\n * A pattern that recognizes URLs that are safe wrt. XSS in URL navigation\n * contexts.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/15.2.8/packages/core/src/sanitization/url_sanitizer.ts#L38\n */\n// eslint-disable-next-line unicorn/better-regex\nconst SAFE_URL_PATTERN = /^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n    .some(regex => regex.test(attributeName))\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    }\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\nimport { DefaultAllowlist, sanitizeHtml } from './sanitizer.js'\nimport { execute, getElement, isElement } from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'TemplateFactory'\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  content: {}, // { selector : text ,  selector2 : text2 , }\n  extraClass: '',\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  template: '<div></div>'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  content: 'object',\n  extraClass: '(string|function)',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  template: 'string'\n}\n\nconst DefaultContentType = {\n  entry: '(string|element|function|null)',\n  selector: '(string|element)'\n}\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content)\n      .map(config => this._resolvePossibleFunction(config))\n      .filter(Boolean)\n  }\n\n  hasContent() {\n    return this.getContent().length > 0\n  }\n\n  changeContent(content) {\n    this._checkContent(content)\n    this._config.content = { ...this._config.content, ...content }\n    return this\n  }\n\n  toHtml() {\n    const templateWrapper = document.createElement('div')\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template)\n\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector)\n    }\n\n    const template = templateWrapper.children[0]\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass)\n\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '))\n    }\n\n    return template\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config)\n    this._checkContent(config.content)\n  }\n\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({ selector, entry: content }, DefaultContentType)\n    }\n  }\n\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!templateElement) {\n      return\n    }\n\n    content = this._resolvePossibleFunction(content)\n\n    if (!content) {\n      templateElement.remove()\n      return\n    }\n\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement)\n      return\n    }\n\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content)\n      return\n    }\n\n    templateElement.textContent = content\n  }\n\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [undefined, this])\n  }\n\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = ''\n      templateElement.append(element)\n      return\n    }\n\n    templateElement.textContent = element.textContent\n  }\n}\n\nexport default TemplateFactory\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport {\n  defineJQueryPlugin, execute, findShadowRoot, getElement, getUID, isRTL, noop\n} from './util/index.js'\nimport { DefaultAllowlist } from './util/sanitizer.js'\nimport TemplateFactory from './util/template-factory.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst EVENT_HIDE = 'hide'\nconst EVENT_HIDDEN = 'hidden'\nconst EVENT_SHOW = 'show'\nconst EVENT_SHOWN = 'shown'\nconst EVENT_INSERTED = 'inserted'\nconst EVENT_CLICK = 'click'\nconst EVENT_FOCUSIN = 'focusin'\nconst EVENT_FOCUSOUT = 'focusout'\nconst EVENT_MOUSEENTER = 'mouseenter'\nconst EVENT_MOUSELEAVE = 'mouseleave'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  animation: true,\n  boundary: 'clippingParents',\n  container: false,\n  customClass: '',\n  delay: 0,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  html: false,\n  offset: [0, 6],\n  placement: 'top',\n  popperConfig: null,\n  sanitize: true,\n  sanitizeFn: null,\n  selector: false,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n            '<div class=\"tooltip-arrow\"></div>' +\n            '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  title: '',\n  trigger: 'hover focus'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  animation: 'boolean',\n  boundary: '(string|element)',\n  container: '(string|element|boolean)',\n  customClass: '(string|function)',\n  delay: '(number|object)',\n  fallbackPlacements: 'array',\n  html: 'boolean',\n  offset: '(array|string|function)',\n  placement: '(string|function)',\n  popperConfig: '(null|object|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  selector: '(string|boolean)',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string'\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org/docs/v2/)')\n    }\n\n    super(element, config)\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._isHovered = null\n    this._activeTrigger = {}\n    this._popper = null\n    this._templateFactory = null\n    this._newContent = null\n\n    // Protected\n    this.tip = null\n\n    this._setListeners()\n\n    if (!this._config.selector) {\n      this._fixTitle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle() {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (this._isShown()) {\n      this._leave()\n      return\n    }\n\n    this._enter()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._element.getAttribute('data-bs-original-title')) {\n      this._element.setAttribute('title', this._element.getAttribute('data-bs-original-title'))\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW))\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    // TODO: v6 remove this or make it optional\n    this._disposePopper()\n\n    const tip = this._getTipElement()\n\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'))\n\n    const { container } = this._config\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED))\n    }\n\n    this._popper = this._createPopper(tip)\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    const complete = () => {\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN))\n\n      if (this._isHovered === false) {\n        this._leave()\n      }\n\n      this._isHovered = false\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  hide() {\n    if (!this._isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE))\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const tip = this._getTipElement()\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n    this._isHovered = null // it is a trick to support manual triggering\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (!this._isHovered) {\n        this._disposePopper()\n      }\n\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN))\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  update() {\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle())\n  }\n\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._newContent || this._getContentForTemplate())\n    }\n\n    return this.tip\n  }\n\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml()\n\n    // TODO: remove this check in v6\n    if (!tip) {\n      return null\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n    // TODO: v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`)\n\n    const tipId = getUID(this.constructor.NAME).toString()\n\n    tip.setAttribute('id', tipId)\n\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    return tip\n  }\n\n  setContent(content) {\n    this._newContent = content\n    if (this._isShown()) {\n      this._disposePopper()\n      this.show()\n    }\n  }\n\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content)\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      })\n    }\n\n    return this._templateFactory\n  }\n\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    }\n  }\n\n  _getTitle() {\n    return this._resolvePossibleFunction(this._config.title) || this._element.getAttribute('data-bs-original-title')\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _isAnimated() {\n    return this._config.animation || (this.tip && this.tip.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _createPopper(tip) {\n    const placement = execute(this._config.placement, [this, tip, this._element])\n    const attachment = AttachmentMap[placement.toUpperCase()]\n    return Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this._element, this._element])\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'preSetPlacement',\n          enabled: true,\n          phase: 'beforeMain',\n          fn: data => {\n            // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n            // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n            this._getTipElement().setAttribute('data-popper-placement', data.state.placement)\n          }\n        }\n      ]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [undefined, defaultBsPopperConfig])\n    }\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK), this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context.toggle()\n        })\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSEENTER) :\n          this.constructor.eventName(EVENT_FOCUSIN)\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSELEAVE) :\n          this.constructor.eventName(EVENT_FOCUSOUT)\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true\n          context._enter()\n        })\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] =\n            context._element.contains(event.relatedTarget)\n\n          context._leave()\n        })\n      }\n    }\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n\n    if (!title) {\n      return\n    }\n\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent.trim()) {\n      this._element.setAttribute('aria-label', title)\n    }\n\n    this._element.setAttribute('data-bs-original-title', title) // DO NOT USE IT. Is only for backwards compatibility\n    this._element.removeAttribute('title')\n  }\n\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true\n      return\n    }\n\n    this._isHovered = true\n\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show()\n      }\n    }, this._config.delay.show)\n  }\n\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return\n    }\n\n    this._isHovered = false\n\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide()\n      }\n    }, this._config.delay.hide)\n  }\n\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout)\n    this._timeout = setTimeout(handler, timeout)\n  }\n\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true)\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute]\n      }\n    }\n\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const [key, value] of Object.entries(this._config)) {\n      if (this.constructor.Default[key] !== value) {\n        config[key] = value\n      }\n    }\n\n    config.selector = false\n    config.trigger = 'manual'\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    if (this.tip) {\n      this.tip.remove()\n      this.tip = null\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n"], "names": ["ARIA_ATTRIBUTE_PATTERN", "DefaultAllowlist", "uriAttributes", "SAFE_URL_PATTERN", "allowedAttribute", "attribute", "allowedAttributeList", "attributeName", "attributeRegex", "regex", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFunction", "createdDocument", "elements", "element", "elementName", "attributeList", "allowedAttributes", "NAME", "<PERSON><PERSON><PERSON>", "DefaultType", "DefaultContentType", "TemplateFactory", "Config", "config", "content", "__spreadValues", "templateWrapper", "selector", "text", "template", "extraClass", "arg", "templateElement", "SelectorEngine", "isElement", "getElement", "execute", "DISALLOWED_ATTRIBUTES", "CLASS_NAME_FADE", "CLASS_NAME_MODAL", "CLASS_NAME_SHOW", "SELECTOR_TOOLTIP_INNER", "SELECTOR_MODAL", "EVENT_MODAL_HIDE", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_INSERTED", "EVENT_CLICK", "EVENT_FOCUSIN", "EVENT_FOCUSOUT", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "AttachmentMap", "isRTL", "<PERSON><PERSON><PERSON>", "BaseComponent", "<PERSON><PERSON>", "EventHandler", "showEvent", "isInTheDom", "findShadowRoot", "tip", "container", "noop", "complete", "tipId", "getUID", "__spreadProps", "event", "placement", "attachment", "Popper.createPopper", "offset", "value", "popperData", "defaultBsPopperConfig", "data", "triggers", "trigger", "eventIn", "eventOut", "context", "title", "handler", "timeout", "dataAttributes", "Manipulator", "dataAttribute", "key", "defineJQueryPlugin"], "mappings": "0nBAQA,MAAMA,EAAyB,iBAElBC,EAAmB,CAE9B,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAAQD,CAAsB,EAClE,EAAG,CAAC,SAAU,OAAQ,QAAS,KAAK,EACpC,KAAM,CAAE,EACR,EAAG,CAAE,EACL,GAAI,CAAE,EACN,IAAK,CAAE,EACP,KAAM,CAAE,EACR,GAAI,CAAE,EACN,IAAK,CAAE,EACP,GAAI,CAAE,EACN,GAAI,CAAE,EACN,GAAI,CAAE,EACN,GAAI,CAAE,EACN,GAAI,CAAE,EACN,GAAI,CAAE,EACN,GAAI,CAAE,EACN,GAAI,CAAE,EACN,GAAI,CAAE,EACN,GAAI,CAAE,EACN,EAAG,CAAE,EACL,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,QAAQ,EACxD,GAAI,CAAE,EACN,GAAI,CAAE,EACN,EAAG,CAAE,EACL,IAAK,CAAE,EACP,EAAG,CAAE,EACL,MAAO,CAAE,EACT,KAAM,CAAE,EACR,IAAK,CAAE,EACP,IAAK,CAAE,EACP,OAAQ,CAAE,EACV,EAAG,CAAE,EACL,GAAI,CAAA,CACN,EAGME,EAAgB,IAAI,IAAI,CAC5B,aACA,OACA,OACA,WACA,WACA,SACA,MACA,YACF,CAAC,EASKC,EAAmB,0DAEnBC,EAAmB,CAACC,EAAWC,IAAyB,CAC5D,MAAMC,EAAgBF,EAAU,SAAS,YAAW,EAEpD,OAAIC,EAAqB,SAASC,CAAa,EACzCL,EAAc,IAAIK,CAAa,EAC1B,EAAQJ,EAAiB,KAAKE,EAAU,SAAS,EAGnD,GAIFC,EAAqB,OAAOE,GAAkBA,aAA0B,MAAM,EAClF,KAAKC,GAASA,EAAM,KAAKF,CAAa,CAAC,CAC5C,EAEO,SAASG,EAAaC,EAAYC,EAAWC,EAAkB,CACpE,GAAI,CAACF,EAAW,OACd,OAAOA,EAGT,GAAIE,GAAoB,OAAOA,GAAqB,WAClD,OAAOA,EAAiBF,CAAU,EAIpC,MAAMG,EADY,IAAI,OAAO,UAAS,EACJ,gBAAgBH,EAAY,WAAW,EACnEI,EAAW,CAAA,EAAG,OAAO,GAAGD,EAAgB,KAAK,iBAAiB,GAAG,CAAC,EAExE,UAAWE,KAAWD,EAAU,CAC9B,MAAME,EAAcD,EAAQ,SAAS,YAAW,EAEhD,GAAI,CAAC,OAAO,KAAKJ,CAAS,EAAE,SAASK,CAAW,EAAG,CACjDD,EAAQ,OAAM,EACd,QACN,CAEI,MAAME,EAAgB,CAAE,EAAC,OAAO,GAAGF,EAAQ,UAAU,EAC/CG,EAAoB,GAAG,OAAOP,EAAU,GAAG,GAAK,GAAIA,EAAUK,CAAW,GAAK,CAAE,CAAA,EAEtF,UAAWZ,KAAaa,EACjBd,EAAiBC,EAAWc,CAAiB,GAChDH,EAAQ,gBAAgBX,EAAU,QAAQ,CAGlD,CAEE,OAAOS,EAAgB,KAAK,SAC9B,CCpGA,MAAMM,EAAO,kBAEPC,EAAU,CACd,UAAWpB,EACX,QAAS,CAAE,EACX,WAAY,GACZ,KAAM,GACN,SAAU,GACV,WAAY,KACZ,SAAU,aACZ,EAEMqB,EAAc,CAClB,UAAW,SACX,QAAS,SACT,WAAY,oBACZ,KAAM,UACN,SAAU,UACV,WAAY,kBACZ,SAAU,QACZ,EAEMC,EAAqB,CACzB,MAAO,iCACP,SAAU,kBACZ,EAMA,MAAMC,UAAwBC,CAAO,CACnC,YAAYC,EAAQ,CAClB,MAAK,EACL,KAAK,QAAU,KAAK,WAAWA,CAAM,CACzC,CAGE,WAAW,SAAU,CACnB,OAAOL,CACX,CAEE,WAAW,aAAc,CACvB,OAAOC,CACX,CAEE,WAAW,MAAO,CAChB,OAAOF,CACX,CAGE,YAAa,CACX,OAAO,OAAO,OAAO,KAAK,QAAQ,OAAO,EACtC,IAAIM,GAAU,KAAK,yBAAyBA,CAAM,CAAC,EACnD,OAAO,OAAO,CACrB,CAEE,YAAa,CACX,OAAO,KAAK,WAAY,EAAC,OAAS,CACtC,CAEE,cAAcC,EAAS,CACrB,YAAK,cAAcA,CAAO,EAC1B,KAAK,QAAQ,QAAUC,IAAA,GAAK,KAAK,QAAQ,SAAYD,GAC9C,IACX,CAEE,QAAS,CACP,MAAME,EAAkB,SAAS,cAAc,KAAK,EACpDA,EAAgB,UAAY,KAAK,eAAe,KAAK,QAAQ,QAAQ,EAErE,SAAW,CAACC,EAAUC,CAAI,IAAK,OAAO,QAAQ,KAAK,QAAQ,OAAO,EAChE,KAAK,YAAYF,EAAiBE,EAAMD,CAAQ,EAGlD,MAAME,EAAWH,EAAgB,SAAS,CAAC,EACrCI,EAAa,KAAK,yBAAyB,KAAK,QAAQ,UAAU,EAExE,OAAIA,GACFD,EAAS,UAAU,IAAI,GAAGC,EAAW,MAAM,GAAG,CAAC,EAG1CD,CACX,CAGE,iBAAiBN,EAAQ,CACvB,MAAM,iBAAiBA,CAAM,EAC7B,KAAK,cAAcA,EAAO,OAAO,CACrC,CAEE,cAAcQ,EAAK,CACjB,SAAW,CAACJ,EAAUH,CAAO,IAAK,OAAO,QAAQO,CAAG,EAClD,MAAM,iBAAiB,CAAE,SAAAJ,EAAU,MAAOH,CAAS,EAAEJ,CAAkB,CAE7E,CAEE,YAAYS,EAAUL,EAASG,EAAU,CACvC,MAAMK,EAAkBC,EAAe,QAAQN,EAAUE,CAAQ,EAEjE,GAAKG,EAML,IAFAR,EAAU,KAAK,yBAAyBA,CAAO,EAE3C,CAACA,EAAS,CACZQ,EAAgB,OAAM,EACtB,MACN,CAEI,GAAIE,EAAUV,CAAO,EAAG,CACtB,KAAK,sBAAsBW,EAAWX,CAAO,EAAGQ,CAAe,EAC/D,MACN,CAEI,GAAI,KAAK,QAAQ,KAAM,CACrBA,EAAgB,UAAY,KAAK,eAAeR,CAAO,EACvD,MACN,CAEIQ,EAAgB,YAAcR,EAClC,CAEE,eAAeO,EAAK,CAClB,OAAO,KAAK,QAAQ,SAAWxB,EAAawB,EAAK,KAAK,QAAQ,UAAW,KAAK,QAAQ,UAAU,EAAIA,CACxG,CAEE,yBAAyBA,EAAK,CAC5B,OAAOK,EAAQL,EAAK,CAAC,OAAW,IAAI,CAAC,CACzC,CAEE,sBAAsBlB,EAASmB,EAAiB,CAC9C,GAAI,KAAK,QAAQ,KAAM,CACrBA,EAAgB,UAAY,GAC5BA,EAAgB,OAAOnB,CAAO,EAC9B,MACN,CAEImB,EAAgB,YAAcnB,EAAQ,WAC1C,CACA,CCxIA,MAAMI,EAAO,UACPoB,GAAwB,IAAI,IAAI,CAAC,WAAY,YAAa,YAAY,CAAC,EAEvEC,EAAkB,OAClBC,GAAmB,QACnBC,EAAkB,OAElBC,GAAyB,iBACzBC,EAAiB,IAAIH,EAAgB,GAErCI,EAAmB,gBAEnBC,EAAgB,QAChBC,EAAgB,QAChBC,GAAgB,QAChBC,GAAiB,SAEjBC,GAAa,OACbC,GAAe,SACfC,GAAa,OACbC,GAAc,QACdC,GAAiB,WACjBC,GAAc,QACdC,GAAgB,UAChBC,GAAiB,WACjBC,GAAmB,aACnBC,GAAmB,aAEnBC,GAAgB,CACpB,KAAM,OACN,IAAK,MACL,MAAOC,IAAU,OAAS,QAC1B,OAAQ,SACR,KAAMA,EAAO,EAAG,QAAU,MAC5B,EAEMzC,GAAU,CACd,UAAWpB,EACX,UAAW,GACX,SAAU,kBACV,UAAW,GACX,YAAa,GACb,MAAO,EACP,mBAAoB,CAAC,MAAO,QAAS,SAAU,MAAM,EACrD,KAAM,GACN,OAAQ,CAAC,EAAG,CAAC,EACb,UAAW,MACX,aAAc,KACd,SAAU,GACV,WAAY,KACZ,SAAU,GACV,SAAU,+GAIV,MAAO,GACP,QAAS,aACX,EAEMqB,GAAc,CAClB,UAAW,SACX,UAAW,UACX,SAAU,mBACV,UAAW,2BACX,YAAa,oBACb,MAAO,kBACP,mBAAoB,QACpB,KAAM,UACN,OAAQ,0BACR,UAAW,oBACX,aAAc,yBACd,SAAU,UACV,WAAY,kBACZ,SAAU,mBACV,SAAU,SACV,MAAO,4BACP,QAAS,QACX,EAMA,MAAMyC,UAAgBC,CAAc,CAClC,YAAYhD,EAASU,EAAQ,CAC3B,GAAI,OAAOuC,GAAW,YACpB,MAAM,IAAI,UAAU,sEAAuE,EAG7F,MAAMjD,EAASU,CAAM,EAGrB,KAAK,WAAa,GAClB,KAAK,SAAW,EAChB,KAAK,WAAa,KAClB,KAAK,eAAiB,CAAA,EACtB,KAAK,QAAU,KACf,KAAK,iBAAmB,KACxB,KAAK,YAAc,KAGnB,KAAK,IAAM,KAEX,KAAK,cAAa,EAEb,KAAK,QAAQ,UAChB,KAAK,UAAS,CAEpB,CAGE,WAAW,SAAU,CACnB,OAAOL,EACX,CAEE,WAAW,aAAc,CACvB,OAAOC,EACX,CAEE,WAAW,MAAO,CAChB,OAAOF,CACX,CAGE,QAAS,CACP,KAAK,WAAa,EACtB,CAEE,SAAU,CACR,KAAK,WAAa,EACtB,CAEE,eAAgB,CACd,KAAK,WAAa,CAAC,KAAK,UAC5B,CAEE,QAAS,CACP,GAAK,KAAK,WAIV,IAAI,KAAK,WAAY,CACnB,KAAK,OAAM,EACX,MACN,CAEI,KAAK,OAAM,EACf,CAEE,SAAU,CACR,aAAa,KAAK,QAAQ,EAE1B8C,EAAa,IAAI,KAAK,SAAS,QAAQrB,CAAc,EAAGC,EAAkB,KAAK,iBAAiB,EAE5F,KAAK,SAAS,aAAa,wBAAwB,GACrD,KAAK,SAAS,aAAa,QAAS,KAAK,SAAS,aAAa,wBAAwB,CAAC,EAG1F,KAAK,eAAc,EACnB,MAAM,QAAO,CACjB,CAEE,MAAO,CACL,GAAI,KAAK,SAAS,MAAM,UAAY,OAClC,MAAM,IAAI,MAAM,qCAAqC,EAGvD,GAAI,EAAE,KAAK,eAAgB,GAAI,KAAK,YAClC,OAGF,MAAMqB,EAAYD,EAAa,QAAQ,KAAK,SAAU,KAAK,YAAY,UAAUb,EAAU,CAAC,EAEtFe,GADaC,EAAe,KAAK,QAAQ,GACb,KAAK,SAAS,cAAc,iBAAiB,SAAS,KAAK,QAAQ,EAErG,GAAIF,EAAU,kBAAoB,CAACC,EACjC,OAIF,KAAK,eAAc,EAEnB,MAAME,EAAM,KAAK,eAAc,EAE/B,KAAK,SAAS,aAAa,mBAAoBA,EAAI,aAAa,IAAI,CAAC,EAErE,KAAM,CAAE,UAAAC,CAAW,EAAG,KAAK,QAe3B,GAbK,KAAK,SAAS,cAAc,gBAAgB,SAAS,KAAK,GAAG,IAChEA,EAAU,OAAOD,CAAG,EACpBJ,EAAa,QAAQ,KAAK,SAAU,KAAK,YAAY,UAAUX,EAAc,CAAC,GAGhF,KAAK,QAAU,KAAK,cAAce,CAAG,EAErCA,EAAI,UAAU,IAAI3B,CAAe,EAM7B,iBAAkB,SAAS,gBAC7B,UAAW3B,IAAW,GAAG,OAAO,GAAG,SAAS,KAAK,QAAQ,EACvDkD,EAAa,GAAGlD,EAAS,YAAawD,CAAI,EAI9C,MAAMC,EAAW,IAAM,CACrBP,EAAa,QAAQ,KAAK,SAAU,KAAK,YAAY,UAAUZ,EAAW,CAAC,EAEvE,KAAK,aAAe,IACtB,KAAK,OAAM,EAGb,KAAK,WAAa,EACxB,EAEI,KAAK,eAAemB,EAAU,KAAK,IAAK,KAAK,YAAa,CAAA,CAC9D,CAEE,MAAO,CAML,GALI,CAAC,KAAK,YAIQP,EAAa,QAAQ,KAAK,SAAU,KAAK,YAAY,UAAUf,EAAU,CAAC,EAC9E,iBACZ,OAQF,GALY,KAAK,eAAc,EAC3B,UAAU,OAAOR,CAAe,EAIhC,iBAAkB,SAAS,gBAC7B,UAAW3B,IAAW,GAAG,OAAO,GAAG,SAAS,KAAK,QAAQ,EACvDkD,EAAa,IAAIlD,EAAS,YAAawD,CAAI,EAI/C,KAAK,eAAevB,EAAa,EAAI,GACrC,KAAK,eAAeD,CAAa,EAAI,GACrC,KAAK,eAAeD,CAAa,EAAI,GACrC,KAAK,WAAa,KAElB,MAAM0B,EAAW,IAAM,CACjB,KAAK,yBAIJ,KAAK,YACR,KAAK,eAAc,EAGrB,KAAK,SAAS,gBAAgB,kBAAkB,EAChDP,EAAa,QAAQ,KAAK,SAAU,KAAK,YAAY,UAAUd,EAAY,CAAC,EAClF,EAEI,KAAK,eAAeqB,EAAU,KAAK,IAAK,KAAK,YAAa,CAAA,CAC9D,CAEE,QAAS,CACH,KAAK,SACP,KAAK,QAAQ,OAAM,CAEzB,CAGE,gBAAiB,CACf,MAAO,EAAQ,KAAK,UAAW,CACnC,CAEE,gBAAiB,CACf,OAAK,KAAK,MACR,KAAK,IAAM,KAAK,kBAAkB,KAAK,aAAe,KAAK,uBAAwB,CAAA,GAG9E,KAAK,GAChB,CAEE,kBAAkB9C,EAAS,CACzB,MAAM2C,EAAM,KAAK,oBAAoB3C,CAAO,EAAE,OAAM,EAGpD,GAAI,CAAC2C,EACH,OAAO,KAGTA,EAAI,UAAU,OAAO7B,EAAiBE,CAAe,EAErD2B,EAAI,UAAU,IAAI,MAAM,KAAK,YAAY,IAAI,OAAO,EAEpD,MAAMI,EAAQC,EAAO,KAAK,YAAY,IAAI,EAAE,SAAQ,EAEpD,OAAAL,EAAI,aAAa,KAAMI,CAAK,EAExB,KAAK,eACPJ,EAAI,UAAU,IAAI7B,CAAe,EAG5B6B,CACX,CAEE,WAAW3C,EAAS,CAClB,KAAK,YAAcA,EACf,KAAK,aACP,KAAK,eAAc,EACnB,KAAK,KAAI,EAEf,CAEE,oBAAoBA,EAAS,CAC3B,OAAI,KAAK,iBACP,KAAK,iBAAiB,cAAcA,CAAO,EAE3C,KAAK,iBAAmB,IAAIH,EAAgBoD,EAAAhD,EAAA,GACvC,KAAK,SADkC,CAI1C,QAAAD,EACA,WAAY,KAAK,yBAAyB,KAAK,QAAQ,WAAW,CACnE,EAAA,EAGI,KAAK,gBAChB,CAEE,wBAAyB,CACvB,MAAO,CACL,CAACiB,EAAsB,EAAG,KAAK,UAAS,CAC9C,CACA,CAEE,WAAY,CACV,OAAO,KAAK,yBAAyB,KAAK,QAAQ,KAAK,GAAK,KAAK,SAAS,aAAa,wBAAwB,CACnH,CAGE,6BAA6BiC,EAAO,CAClC,OAAO,KAAK,YAAY,oBAAoBA,EAAM,eAAgB,KAAK,mBAAoB,CAAA,CAC/F,CAEE,aAAc,CACZ,OAAO,KAAK,QAAQ,WAAc,KAAK,KAAO,KAAK,IAAI,UAAU,SAASpC,CAAe,CAC7F,CAEE,UAAW,CACT,OAAO,KAAK,KAAO,KAAK,IAAI,UAAU,SAASE,CAAe,CAClE,CAEE,cAAc2B,EAAK,CACjB,MAAMQ,EAAYvC,EAAQ,KAAK,QAAQ,UAAW,CAAC,KAAM+B,EAAK,KAAK,QAAQ,CAAC,EACtES,EAAalB,GAAciB,EAAU,YAAa,CAAA,EACxD,OAAOE,EAAoB,KAAK,SAAUV,EAAK,KAAK,iBAAiBS,CAAU,CAAC,CACpF,CAEE,YAAa,CACX,KAAM,CAAE,OAAAE,CAAQ,EAAG,KAAK,QAExB,OAAI,OAAOA,GAAW,SACbA,EAAO,MAAM,GAAG,EAAE,IAAIC,GAAS,OAAO,SAASA,EAAO,EAAE,CAAC,EAG9D,OAAOD,GAAW,WACbE,GAAcF,EAAOE,EAAY,KAAK,QAAQ,EAGhDF,CACX,CAEE,yBAAyB/C,EAAK,CAC5B,OAAOK,EAAQL,EAAK,CAAC,KAAK,SAAU,KAAK,QAAQ,CAAC,CACtD,CAEE,iBAAiB6C,EAAY,CAC3B,MAAMK,EAAwB,CAC5B,UAAWL,EACX,UAAW,CACT,CACE,KAAM,OACN,QAAS,CACP,mBAAoB,KAAK,QAAQ,kBAC7C,CACS,EACD,CACE,KAAM,SACN,QAAS,CACP,OAAQ,KAAK,WAAU,CACnC,CACS,EACD,CACE,KAAM,kBACN,QAAS,CACP,SAAU,KAAK,QAAQ,QACnC,CACS,EACD,CACE,KAAM,QACN,QAAS,CACP,QAAS,IAAI,KAAK,YAAY,IAAI,QAC9C,CACS,EACD,CACE,KAAM,kBACN,QAAS,GACT,MAAO,aACP,GAAIM,GAAQ,CAGV,KAAK,eAAc,EAAG,aAAa,wBAAyBA,EAAK,MAAM,SAAS,CAC5F,CACA,CACA,CACA,EAEI,OAAOzD,IAAA,GACFwD,GACA7C,EAAQ,KAAK,QAAQ,aAAc,CAAC,OAAW6C,CAAqB,CAAC,EAE9E,CAEE,eAAgB,CACd,MAAME,EAAW,KAAK,QAAQ,QAAQ,MAAM,GAAG,EAE/C,UAAWC,KAAWD,EACpB,GAAIC,IAAY,QACdrB,EAAa,GAAG,KAAK,SAAU,KAAK,YAAY,UAAUV,EAAW,EAAG,KAAK,QAAQ,SAAUqB,GAAS,CACtF,KAAK,6BAA6BA,CAAK,EAC/C,OAAM,CACf,CAAA,UACQU,IAAYrC,GAAgB,CACrC,MAAMsC,EAAUD,IAAYxC,EAC1B,KAAK,YAAY,UAAUY,EAAgB,EAC3C,KAAK,YAAY,UAAUF,EAAa,EACpCgC,EAAWF,IAAYxC,EAC3B,KAAK,YAAY,UAAUa,EAAgB,EAC3C,KAAK,YAAY,UAAUF,EAAc,EAE3CQ,EAAa,GAAG,KAAK,SAAUsB,EAAS,KAAK,QAAQ,SAAUX,GAAS,CACtE,MAAMa,EAAU,KAAK,6BAA6Bb,CAAK,EACvDa,EAAQ,eAAeb,EAAM,OAAS,UAAY7B,EAAgBD,CAAa,EAAI,GACnF2C,EAAQ,OAAM,CACf,CAAA,EACDxB,EAAa,GAAG,KAAK,SAAUuB,EAAU,KAAK,QAAQ,SAAUZ,GAAS,CACvE,MAAMa,EAAU,KAAK,6BAA6Bb,CAAK,EACvDa,EAAQ,eAAeb,EAAM,OAAS,WAAa7B,EAAgBD,CAAa,EAC9E2C,EAAQ,SAAS,SAASb,EAAM,aAAa,EAE/Ca,EAAQ,OAAM,CACf,CAAA,CACT,CAGI,KAAK,kBAAoB,IAAM,CACzB,KAAK,UACP,KAAK,KAAI,CAEjB,EAEIxB,EAAa,GAAG,KAAK,SAAS,QAAQrB,CAAc,EAAGC,EAAkB,KAAK,iBAAiB,CACnG,CAEE,WAAY,CACV,MAAM6C,EAAQ,KAAK,SAAS,aAAa,OAAO,EAE3CA,IAID,CAAC,KAAK,SAAS,aAAa,YAAY,GAAK,CAAC,KAAK,SAAS,YAAY,QAC1E,KAAK,SAAS,aAAa,aAAcA,CAAK,EAGhD,KAAK,SAAS,aAAa,yBAA0BA,CAAK,EAC1D,KAAK,SAAS,gBAAgB,OAAO,EACzC,CAEE,QAAS,CACP,GAAI,KAAK,YAAc,KAAK,WAAY,CACtC,KAAK,WAAa,GAClB,MACN,CAEI,KAAK,WAAa,GAElB,KAAK,YAAY,IAAM,CACjB,KAAK,YACP,KAAK,KAAI,CAEjB,EAAO,KAAK,QAAQ,MAAM,IAAI,CAC9B,CAEE,QAAS,CACH,KAAK,yBAIT,KAAK,WAAa,GAElB,KAAK,YAAY,IAAM,CAChB,KAAK,YACR,KAAK,KAAI,CAEjB,EAAO,KAAK,QAAQ,MAAM,IAAI,EAC9B,CAEE,YAAYC,EAASC,EAAS,CAC5B,aAAa,KAAK,QAAQ,EAC1B,KAAK,SAAW,WAAWD,EAASC,CAAO,CAC/C,CAEE,sBAAuB,CACrB,OAAO,OAAO,OAAO,KAAK,cAAc,EAAE,SAAS,EAAI,CAC3D,CAEE,WAAWnE,EAAQ,CACjB,MAAMoE,EAAiBC,EAAY,kBAAkB,KAAK,QAAQ,EAElE,UAAWC,KAAiB,OAAO,KAAKF,CAAc,EAChDtD,GAAsB,IAAIwD,CAAa,GACzC,OAAOF,EAAeE,CAAa,EAIvC,OAAAtE,EAASE,IAAA,GACJkE,GACC,OAAOpE,GAAW,UAAYA,EAASA,EAAS,CAAE,GAExDA,EAAS,KAAK,gBAAgBA,CAAM,EACpCA,EAAS,KAAK,kBAAkBA,CAAM,EACtC,KAAK,iBAAiBA,CAAM,EACrBA,CACX,CAEE,kBAAkBA,EAAQ,CACxB,OAAAA,EAAO,UAAYA,EAAO,YAAc,GAAQ,SAAS,KAAOY,EAAWZ,EAAO,SAAS,EAEvF,OAAOA,EAAO,OAAU,WAC1BA,EAAO,MAAQ,CACb,KAAMA,EAAO,MACb,KAAMA,EAAO,KACrB,GAGQ,OAAOA,EAAO,OAAU,WAC1BA,EAAO,MAAQA,EAAO,MAAM,SAAQ,GAGlC,OAAOA,EAAO,SAAY,WAC5BA,EAAO,QAAUA,EAAO,QAAQ,SAAQ,GAGnCA,CACX,CAEE,oBAAqB,CACnB,MAAMA,EAAS,CAAA,EAEf,SAAW,CAACuE,EAAKf,CAAK,IAAK,OAAO,QAAQ,KAAK,OAAO,EAChD,KAAK,YAAY,QAAQe,CAAG,IAAMf,IACpCxD,EAAOuE,CAAG,EAAIf,GAIlB,OAAAxD,EAAO,SAAW,GAClBA,EAAO,QAAU,SAKVA,CACX,CAEE,gBAAiB,CACX,KAAK,UACP,KAAK,QAAQ,QAAO,EACpB,KAAK,QAAU,MAGb,KAAK,MACP,KAAK,IAAI,OAAM,EACf,KAAK,IAAM,KAEjB,CAGE,OAAO,gBAAgBA,EAAQ,CAC7B,OAAO,KAAK,KAAK,UAAY,CAC3B,MAAM2D,EAAOtB,EAAQ,oBAAoB,KAAMrC,CAAM,EAErD,GAAI,OAAOA,GAAW,SAItB,IAAI,OAAO2D,EAAK3D,CAAM,GAAM,YAC1B,MAAM,IAAI,UAAU,oBAAoBA,CAAM,GAAG,EAGnD2D,EAAK3D,CAAM,EAAC,EACb,CAAA,CACL,CACA,CAMAwE,EAAmBnC,CAAO", "x_google_ignoreList": [0, 1, 2]}