var S=Object.defineProperty,L=Object.defineProperties;var P=Object.getOwnPropertyDescriptors;var g=Object.getOwnPropertySymbols;var D=Object.prototype.hasOwnProperty,M=Object.prototype.propertyIsEnumerable;var b=(n,t,e)=>t in n?S(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e,l=(n,t)=>{for(var e in t||(t={}))D.call(t,e)&&b(n,e,t[e]);if(g)for(var e of g(t))M.call(t,e)&&b(n,e,t[e]);return n},E=(n,t)=>L(n,P(t));import{P as F,c as I}from"../index/index.3YuNPc_l.js";import{C as H,S as R,i as x,g as A,e as _,B as k,E as a,j as z,n as T,k as j,M as V,b as v,d as U}from"../selector-engine/selector-engine.DUbyXJpR.js";const B=/^aria-[\w-]*$/i,w={"*":["class","dir","id","lang","role",B],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},W=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),G=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,$=(n,t)=>{const e=n.nodeName.toLowerCase();return t.includes(e)?W.has(e)?!!G.test(n.nodeValue):!0:t.filter(i=>i instanceof RegExp).some(i=>i.test(e))};function q(n,t,e){if(!n.length)return n;if(e&&typeof e=="function")return e(n);const s=new window.DOMParser().parseFromString(n,"text/html"),r=[].concat(...s.body.querySelectorAll("*"));for(const o of r){const h=o.nodeName.toLowerCase();if(!Object.keys(t).includes(h)){o.remove();continue}const N=[].concat(...o.attributes),O=[].concat(t["*"]||[],t[h]||[]);for(const d of N)$(d,O)||o.removeAttribute(d.nodeName)}return s.body.innerHTML}const K="TemplateFactory",Q={allowList:w,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},J={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},X={entry:"(string|element|function|null)",selector:"(string|element)"};class Y extends H{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return Q}static get DefaultType(){return J}static get NAME(){return K}getContent(){return Object.values(this._config.content).map(t=>this._resolvePossibleFunction(t)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content=l(l({},this._config.content),t),this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[s,r]of Object.entries(this._config.content))this._setContent(t,r,s);const e=t.children[0],i=this._resolvePossibleFunction(this._config.extraClass);return i&&e.classList.add(...i.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,i]of Object.entries(t))super._typeCheckConfig({selector:e,entry:i},X)}_setContent(t,e,i){const s=R.findOne(i,t);if(s){if(e=this._resolvePossibleFunction(e),!e){s.remove();return}if(x(e)){this._putElementInTemplate(A(e),s);return}if(this._config.html){s.innerHTML=this._maybeSanitize(e);return}s.textContent=e}}_maybeSanitize(t){return this._config.sanitize?q(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return _(t,[void 0,this])}_putElementInTemplate(t,e){if(this._config.html){e.innerHTML="",e.append(t);return}e.textContent=t.textContent}}const Z="tooltip",tt=new Set(["sanitize","allowList","sanitizeFn"]),p="fade",et="modal",u="show",it=".tooltip-inner",C=`.${et}`,y="hide.bs.modal",c="hover",m="focus",st="click",nt="manual",ot="hide",rt="hidden",at="show",lt="shown",ct="inserted",ht="click",ut="focusin",_t="focusout",pt="mouseenter",mt="mouseleave",ft={AUTO:"auto",TOP:"top",RIGHT:v()?"left":"right",BOTTOM:"bottom",LEFT:v()?"right":"left"},dt={allowList:w,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},gt={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class f extends k{constructor(t,e){if(typeof F=="undefined")throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return dt}static get DefaultType(){return gt}static get NAME(){return Z}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){if(this._isEnabled){if(this._isShown()){this._leave();return}this._enter()}}dispose(){clearTimeout(this._timeout),a.off(this._element.closest(C),y,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if(this._element.style.display==="none")throw new Error("Please use show on visible elements");if(!(this._isWithContent()&&this._isEnabled))return;const t=a.trigger(this._element,this.constructor.eventName(at)),i=(z(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!i)return;this._disposePopper();const s=this._getTipElement();this._element.setAttribute("aria-describedby",s.getAttribute("id"));const{container:r}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(r.append(s),a.trigger(this._element,this.constructor.eventName(ct))),this._popper=this._createPopper(s),s.classList.add(u),"ontouchstart"in document.documentElement)for(const h of[].concat(...document.body.children))a.on(h,"mouseover",T);const o=()=>{a.trigger(this._element,this.constructor.eventName(lt)),this._isHovered===!1&&this._leave(),this._isHovered=!1};this._queueCallback(o,this.tip,this._isAnimated())}hide(){if(!this._isShown()||a.trigger(this._element,this.constructor.eventName(ot)).defaultPrevented)return;if(this._getTipElement().classList.remove(u),"ontouchstart"in document.documentElement)for(const s of[].concat(...document.body.children))a.off(s,"mouseover",T);this._activeTrigger[st]=!1,this._activeTrigger[m]=!1,this._activeTrigger[c]=!1,this._isHovered=null;const i=()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),a.trigger(this._element,this.constructor.eventName(rt)))};this._queueCallback(i,this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(p,u),e.classList.add(`bs-${this.constructor.NAME}-auto`);const i=j(this.constructor.NAME).toString();return e.setAttribute("id",i),this._isAnimated()&&e.classList.add(p),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new Y(E(l({},this._config),{content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)})),this._templateFactory}_getContentForTemplate(){return{[it]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(p)}_isShown(){return this.tip&&this.tip.classList.contains(u)}_createPopper(t){const e=_(this._config.placement,[this,t,this._element]),i=ft[e.toUpperCase()];return I(this._element,t,this._getPopperConfig(i))}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(e=>Number.parseInt(e,10)):typeof t=="function"?e=>t(e,this._element):t}_resolvePossibleFunction(t){return _(t,[this._element,this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:i=>{this._getTipElement().setAttribute("data-popper-placement",i.state.placement)}}]};return l(l({},e),_(this._config.popperConfig,[void 0,e]))}_setListeners(){const t=this._config.trigger.split(" ");for(const e of t)if(e==="click")a.on(this._element,this.constructor.eventName(ht),this._config.selector,i=>{this._initializeOnDelegatedTarget(i).toggle()});else if(e!==nt){const i=e===c?this.constructor.eventName(pt):this.constructor.eventName(ut),s=e===c?this.constructor.eventName(mt):this.constructor.eventName(_t);a.on(this._element,i,this._config.selector,r=>{const o=this._initializeOnDelegatedTarget(r);o._activeTrigger[r.type==="focusin"?m:c]=!0,o._enter()}),a.on(this._element,s,this._config.selector,r=>{const o=this._initializeOnDelegatedTarget(r);o._activeTrigger[r.type==="focusout"?m:c]=o._element.contains(r.relatedTarget),o._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},a.on(this._element.closest(C),y,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(!this._element.getAttribute("aria-label")&&!this._element.textContent.trim()&&this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){if(this._isShown()||this._isHovered){this._isHovered=!0;return}this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show)}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=V.getDataAttributes(this._element);for(const i of Object.keys(e))tt.has(i)&&delete e[i];return t=l(l({},e),typeof t=="object"&&t?t:{}),t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=t.container===!1?document.body:A(t.container),typeof t.delay=="number"&&(t.delay={show:t.delay,hide:t.delay}),typeof t.title=="number"&&(t.title=t.title.toString()),typeof t.content=="number"&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,i]of Object.entries(this._config))this.constructor.Default[e]!==i&&(t[e]=i);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each(function(){const e=f.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]=="undefined")throw new TypeError(`No method named "${t}"`);e[t]()}})}}U(f);export{f as default};
//# sourceMappingURL=tooltip.aIpwpUuN.js.map
