var U=Object.defineProperty;var v=Object.getOwnPropertySymbols;var V=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable;var M=(t,e,n)=>e in t?U(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,h=(t,e)=>{for(var n in e||(e={}))V.call(e,n)&&M(t,n,e[n]);if(v)for(var n of v(e))z.call(e,n)&&M(t,n,e[n]);return t};const d=new Map,y={set(t,e,n){d.has(t)||d.set(t,new Map);const r=d.get(t);if(!r.has(e)&&r.size!==0){console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(r.keys())[0]}.`);return}r.set(e,n)},get(t,e){return d.has(t)&&d.get(t).get(e)||null},remove(t,e){if(!d.has(t))return;const n=d.get(t);n.delete(e),n.size===0&&d.delete(t)}},B=1e6,W=1e3,A="transitionend",j=t=>(t&&window.CSS&&window.CSS.escape&&(t=t.replace(/#([^\s"#']+)/g,(e,n)=>`#${CSS.escape(n)}`)),t),J=t=>t==null?`${t}`:Object.prototype.toString.call(t).match(/\s([a-z]+)/i)[1].toLowerCase(),gt=t=>{do t+=Math.floor(Math.random()*B);while(document.getElementById(t));return t},X=t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:n}=window.getComputedStyle(t);const r=Number.parseFloat(e),s=Number.parseFloat(n);return!r&&!s?0:(e=e.split(",")[0],n=n.split(",")[0],(Number.parseFloat(e)+Number.parseFloat(n))*W)},Z=t=>{t.dispatchEvent(new Event(A))},m=t=>!t||typeof t!="object"?!1:(typeof t.jquery!="undefined"&&(t=t[0]),typeof t.nodeType!="undefined"),T=t=>m(t)?t.jquery?t[0]:t:typeof t=="string"&&t.length>0?document.querySelector(j(t)):null,G=t=>{if(!m(t)||t.getClientRects().length===0)return!1;const e=getComputedStyle(t).getPropertyValue("visibility")==="visible",n=t.closest("details:not([open])");if(!n)return e;if(n!==t){const r=t.closest("summary");if(r&&r.parentNode!==n||r===null)return!1}return e},tt=t=>!t||t.nodeType!==Node.ELEMENT_NODE||t.classList.contains("disabled")?!0:typeof t.disabled!="undefined"?t.disabled:t.hasAttribute("disabled")&&t.getAttribute("disabled")!=="false",et=t=>{if(!document.documentElement.attachShadow)return null;if(typeof t.getRootNode=="function"){const e=t.getRootNode();return e instanceof ShadowRoot?e:null}return t instanceof ShadowRoot?t:t.parentNode?et(t.parentNode):null},ht=()=>{},mt=t=>{t.offsetHeight},k=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,b=[],nt=t=>{document.readyState==="loading"?(b.length||document.addEventListener("DOMContentLoaded",()=>{for(const e of b)e()}),b.push(t)):t()},yt=()=>document.documentElement.dir==="rtl",bt=t=>{nt(()=>{const e=k();if(e){const n=t.NAME,r=e.fn[n];e.fn[n]=t.jQueryInterface,e.fn[n].Constructor=t,e.fn[n].noConflict=()=>(e.fn[n]=r,t.jQueryInterface)}})},$=(t,e=[],n=t)=>typeof t=="function"?t.call(...e):n,rt=(t,e,n=!0)=>{if(!n){$(t);return}const s=X(e)+5;let i=!1;const o=({target:a})=>{a===e&&(i=!0,e.removeEventListener(A,o),$(t))};e.addEventListener(A,o),setTimeout(()=>{i||Z(e)},s)},Et=(t,e,n,r)=>{const s=t.length;let i=t.indexOf(e);return i===-1?!n&&r?t[s-1]:t[0]:(i+=n?1:-1,r&&(i=(i+s)%s),t[Math.max(0,Math.min(i,s-1))])},ot=/[^.]*(?=\..*)\.|.*/,st=/\..*/,it=/::\d+$/,E={};let L=1;const K={mouseenter:"mouseover",mouseleave:"mouseout"},at=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function P(t,e){return e&&`${e}::${L++}`||t.uidEvent||L++}function F(t){const e=P(t);return t.uidEvent=e,E[e]=E[e]||{},E[e]}function ut(t,e){return function n(r){return C(r,{delegateTarget:t}),n.oneOff&&O.off(t,r.type,e),e.apply(t,[r])}}function ct(t,e,n){return function r(s){const i=t.querySelectorAll(e);for(let{target:o}=s;o&&o!==this;o=o.parentNode)for(const a of i)if(a===o)return C(s,{delegateTarget:o}),r.oneOff&&O.off(t,s.type,e,n),n.apply(o,[s])}}function Y(t,e,n=null){return Object.values(t).find(r=>r.callable===e&&r.delegationSelector===n)}function H(t,e,n){const r=typeof e=="string",s=r?n:e||n;let i=q(t);return at.has(i)||(i=t),[r,s,i]}function I(t,e,n,r,s){if(typeof e!="string"||!t)return;let[i,o,a]=H(e,n,r);e in K&&(o=(Q=>function(g){if(!g.relatedTarget||g.relatedTarget!==g.delegateTarget&&!g.delegateTarget.contains(g.relatedTarget))return Q.call(this,g)})(o));const u=F(t),f=u[a]||(u[a]={}),c=Y(f,o,i?n:null);if(c){c.oneOff=c.oneOff&&s;return}const p=P(o,e.replace(ot,"")),l=i?ct(t,n,o):ut(t,o);l.delegationSelector=i?n:null,l.callable=o,l.oneOff=s,l.uidEvent=p,f[p]=l,t.addEventListener(a,l,i)}function w(t,e,n,r,s){const i=Y(e[n],r,s);i&&(t.removeEventListener(n,i,!!s),delete e[n][i.uidEvent])}function lt(t,e,n,r){const s=e[n]||{};for(const[i,o]of Object.entries(s))i.includes(r)&&w(t,e,n,o.callable,o.delegationSelector)}function q(t){return t=t.replace(st,""),K[t]||t}const O={on(t,e,n,r){I(t,e,n,r,!1)},one(t,e,n,r){I(t,e,n,r,!0)},off(t,e,n,r){if(typeof e!="string"||!t)return;const[s,i,o]=H(e,n,r),a=o!==e,u=F(t),f=u[o]||{},c=e.startsWith(".");if(typeof i!="undefined"){if(!Object.keys(f).length)return;w(t,u,o,i,s?n:null);return}if(c)for(const p of Object.keys(u))lt(t,u,p,e.slice(1));for(const[p,l]of Object.entries(f)){const _=p.replace(it,"");(!a||e.includes(_))&&w(t,u,o,l.callable,l.delegationSelector)}},trigger(t,e,n){if(typeof e!="string"||!t)return null;const r=k(),s=q(e),i=e!==s;let o=null,a=!0,u=!0,f=!1;i&&r&&(o=r.Event(e,n),r(t).trigger(o),a=!o.isPropagationStopped(),u=!o.isImmediatePropagationStopped(),f=o.isDefaultPrevented());const c=C(new Event(e,{bubbles:a,cancelable:!0}),n);return f&&c.preventDefault(),u&&t.dispatchEvent(c),c.defaultPrevented&&o&&o.preventDefault(),c}};function C(t,e={}){for(const[n,r]of Object.entries(e))try{t[n]=r}catch(s){Object.defineProperty(t,n,{configurable:!0,get(){return r}})}return t}function x(t){if(t==="true")return!0;if(t==="false")return!1;if(t===Number(t).toString())return Number(t);if(t===""||t==="null")return null;if(typeof t!="string")return t;try{return JSON.parse(decodeURIComponent(t))}catch(e){return t}}function D(t){return t.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`)}const R={setDataAttribute(t,e,n){t.setAttribute(`data-bs-${D(e)}`,n)},removeDataAttribute(t,e){t.removeAttribute(`data-bs-${D(e)}`)},getDataAttributes(t){if(!t)return{};const e={},n=Object.keys(t.dataset).filter(r=>r.startsWith("bs")&&!r.startsWith("bsConfig"));for(const r of n){let s=r.replace(/^bs/,"");s=s.charAt(0).toLowerCase()+s.slice(1),e[s]=x(t.dataset[r])}return e},getDataAttribute(t,e){return x(t.getAttribute(`data-bs-${D(e)}`))}};class ft{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,n){const r=m(n)?R.getDataAttribute(n,"config"):{};return h(h(h(h({},this.constructor.Default),typeof r=="object"?r:{}),m(n)?R.getDataAttributes(n):{}),typeof e=="object"?e:{})}_typeCheckConfig(e,n=this.constructor.DefaultType){for(const[r,s]of Object.entries(n)){const i=e[r],o=m(i)?"element":J(i);if(!new RegExp(s).test(o))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${r}" provided type "${o}" but expected type "${s}".`)}}}const dt="5.3.6";class Dt extends ft{constructor(e,n){super(),e=T(e),e&&(this._element=e,this._config=this._getConfig(n),y.set(this._element,this.constructor.DATA_KEY,this))}dispose(){y.remove(this._element,this.constructor.DATA_KEY),O.off(this._element,this.constructor.EVENT_KEY);for(const e of Object.getOwnPropertyNames(this))this[e]=null}_queueCallback(e,n,r=!0){rt(e,n,r)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return y.get(T(e),this.DATA_KEY)}static getOrCreateInstance(e,n={}){return this.getInstance(e)||new this(e,typeof n=="object"?n:null)}static get VERSION(){return dt}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}const S=t=>{let e=t.getAttribute("data-bs-target");if(!e||e==="#"){let n=t.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n=`#${n.split("#")[1]}`),e=n&&n!=="#"?n.trim():null}return e?e.split(",").map(n=>j(n)).join(","):null},N={find(t,e=document.documentElement){return[].concat(...Element.prototype.querySelectorAll.call(e,t))},findOne(t,e=document.documentElement){return Element.prototype.querySelector.call(e,t)},children(t,e){return[].concat(...t.children).filter(n=>n.matches(e))},parents(t,e){const n=[];let r=t.parentNode.closest(e);for(;r;)n.push(r),r=r.parentNode.closest(e);return n},prev(t,e){let n=t.previousElementSibling;for(;n;){if(n.matches(e))return[n];n=n.previousElementSibling}return[]},next(t,e){let n=t.nextElementSibling;for(;n;){if(n.matches(e))return[n];n=n.nextElementSibling}return[]},focusableChildren(t){const e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(n=>`${n}:not([tabindex^="-"])`).join(",");return this.find(e,t).filter(n=>!tt(n)&&G(n))},getSelectorFromElement(t){const e=S(t);return e&&N.findOne(e)?e:null},getElementFromSelector(t){const e=S(t);return e?N.findOne(e):null},getMultipleElementsFromSelector(t){const e=S(t);return e?N.find(e):[]}};export{Dt as B,ft as C,O as E,R as M,N as S,rt as a,yt as b,G as c,bt as d,$ as e,tt as f,T as g,Et as h,m as i,et as j,gt as k,ht as n,mt as r};
//# sourceMappingURL=selector-engine.DUbyXJpR.js.map
