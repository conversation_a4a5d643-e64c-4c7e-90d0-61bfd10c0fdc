{"version": 3, "file": "popover-pgn5fEK0.js", "sources": ["../../node_modules/.pnpm/bootstrap@4.6.2_j<PERSON>y@3.7.1_popper.js@1.16.1/node_modules/bootstrap/js/src/popover.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Tooltip from './tooltip'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  addAttachmentClass(attachment) {\n    $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  getTipElement() {\n    this.tip = this.tip || $(this.config.template)[0]\n    return this.tip\n  }\n\n  setContent() {\n    const $tip = $(this.getTipElement())\n\n    // We use append for html objects to maintain js events\n    this.setElementContent($tip.find(SELECTOR_TITLE), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n\n    this.setElementContent($tip.find(SELECTOR_CONTENT), content)\n\n    $tip.removeClass(`${CLASS_NAME_FADE} ${CLASS_NAME_SHOW}`)\n  }\n\n  // Private\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const $tip = $(this.getTipElement())\n    const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      $tip.removeClass(tabClass.join(''))\n    }\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Popover._jQueryInterface\n$.fn[NAME].Constructor = Popover\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Popover._jQueryInterface\n}\n\nexport default Popover\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "SELECTOR_TITLE", "SELECTOR_CONTENT", "<PERSON><PERSON><PERSON>", "__spreadProps", "__spreadValues", "<PERSON><PERSON><PERSON>", "DefaultType", "Event", "Popover", "attachment", "$tip", "content", "tabClass", "config", "data", "_config"], "mappings": "giBAcA,MAAMA,EAAO,UACPC,EAAU,QACVC,EAAW,aACXC,EAAY,IAAID,CAAQ,GACxBE,EAAqBC,EAAE,GAAGL,CAAI,EAC9BM,EAAe,aACfC,EAAqB,IAAI,OAAO,UAAUD,CAAY,OAAQ,GAAG,EAEjEE,EAAkB,OAClBC,EAAkB,OAElBC,EAAiB,kBACjBC,EAAmB,gBAEnBC,EAAUC,EAAAC,EAAA,GACXC,EAAQ,SADG,CAEd,UAAW,QACX,QAAS,QACT,QAAS,GACT,SAAU,qIAIZ,GAEMC,EAAcH,EAAAC,EAAA,GACfC,EAAQ,aADO,CAElB,QAAS,2BACX,GAEME,EAAQ,CACZ,KAAM,OAAOd,CAAS,GACtB,OAAQ,SAASA,CAAS,GAC1B,KAAM,OAAOA,CAAS,GACtB,MAAO,QAAQA,CAAS,GACxB,SAAU,WAAWA,CAAS,GAC9B,MAAO,QAAQA,CAAS,GACxB,QAAS,UAAUA,CAAS,GAC5B,SAAU,WAAWA,CAAS,GAC9B,WAAY,aAAaA,CAAS,GAClC,WAAY,aAAaA,CAAS,EACpC,EAMA,MAAMe,UAAgBH,CAAQ,CAE5B,WAAW,SAAU,CACnB,OAAOd,CACX,CAEE,WAAW,SAAU,CACnB,OAAOW,CACX,CAEE,WAAW,MAAO,CAChB,OAAOZ,CACX,CAEE,WAAW,UAAW,CACpB,OAAOE,CACX,CAEE,WAAW,OAAQ,CACjB,OAAOe,CACX,CAEE,WAAW,WAAY,CACrB,OAAOd,CACX,CAEE,WAAW,aAAc,CACvB,OAAOa,CACX,CAGE,eAAgB,CACd,OAAO,KAAK,YAAc,KAAK,YAAW,CAC9C,CAEE,mBAAmBG,EAAY,CAC7Bd,EAAE,KAAK,cAAa,CAAE,EAAE,SAAS,GAAGC,CAAY,IAAIa,CAAU,EAAE,CACpE,CAEE,eAAgB,CACd,YAAK,IAAM,KAAK,KAAOd,EAAE,KAAK,OAAO,QAAQ,EAAE,CAAC,EACzC,KAAK,GAChB,CAEE,YAAa,CACX,MAAMe,EAAOf,EAAE,KAAK,cAAe,CAAA,EAGnC,KAAK,kBAAkBe,EAAK,KAAKV,CAAc,EAAG,KAAK,SAAU,CAAA,EACjE,IAAIW,EAAU,KAAK,YAAW,EAC1B,OAAOA,GAAY,aACrBA,EAAUA,EAAQ,KAAK,KAAK,OAAO,GAGrC,KAAK,kBAAkBD,EAAK,KAAKT,CAAgB,EAAGU,CAAO,EAE3DD,EAAK,YAAY,GAAGZ,CAAe,IAAIC,CAAe,EAAE,CAC5D,CAGE,aAAc,CACZ,OAAO,KAAK,QAAQ,aAAa,cAAc,GAC7C,KAAK,OAAO,OAClB,CAEE,gBAAiB,CACf,MAAMW,EAAOf,EAAE,KAAK,cAAe,CAAA,EAC7BiB,EAAWF,EAAK,KAAK,OAAO,EAAE,MAAMb,CAAkB,EACxDe,IAAa,MAAQA,EAAS,OAAS,GACzCF,EAAK,YAAYE,EAAS,KAAK,EAAE,CAAC,CAExC,CAGE,OAAO,iBAAiBC,EAAQ,CAC9B,OAAO,KAAK,KAAK,UAAY,CAC3B,IAAIC,EAAOnB,EAAE,IAAI,EAAE,KAAKH,CAAQ,EAChC,MAAMuB,EAAU,OAAOF,GAAW,SAAWA,EAAS,KAEtD,GAAI,GAACC,GAAQ,eAAe,KAAKD,CAAM,KAIlCC,IACHA,EAAO,IAAIN,EAAQ,KAAMO,CAAO,EAChCpB,EAAE,IAAI,EAAE,KAAKH,EAAUsB,CAAI,GAGzB,OAAOD,GAAW,UAAU,CAC9B,GAAI,OAAOC,EAAKD,CAAM,GAAM,YAC1B,MAAM,IAAI,UAAU,oBAAoBA,CAAM,GAAG,EAGnDC,EAAKD,CAAM,EAAC,CACpB,CACK,CAAA,CACL,CACA,CAMAlB,EAAE,GAAGL,CAAI,EAAIkB,EAAQ,iBACrBb,EAAE,GAAGL,CAAI,EAAE,YAAckB,EACzBb,EAAE,GAAGL,CAAI,EAAE,WAAa,KACtBK,EAAE,GAAGL,CAAI,EAAII,EACNc,EAAQ", "x_google_ignoreList": [0]}