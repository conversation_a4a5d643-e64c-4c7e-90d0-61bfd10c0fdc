var y=Object.defineProperty;var T=Object.getOwnPropertySymbols;var N=Object.prototype.hasOwnProperty,$=Object.prototype.propertyIsEnumerable;var p=(i,t,e)=>t in i?y(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e,E=(i,t)=>{for(var e in t||(t={}))N.call(t,e)&&p(i,e,t[e]);if(T)for(var e of T(t))$.call(t,e)&&p(i,e,t[e]);return i};import o from"jquery";import{U as _}from"./util-B8s7WWwa.js";const c="scrollspy",v="4.6.2",f="bs.scrollspy",g=`.${f}`,D=".data-api",I=o.fn[c],L="dropdown-item",n="active",w=`activate${g}`,R=`scroll${g}`,H=`load${g}${D}`,M="offset",S="position",V='[data-spy="scroll"]',O=".nav, .list-group",u=".nav-link",P=".nav-item",C=".list-group-item",j=".dropdown",Y=".dropdown-item",q=".dropdown-toggle",A={offset:10,method:"auto",target:""},B={offset:"number",method:"string",target:"(string|element)"};class h{constructor(t,e){this._element=t,this._scrollElement=t.tagName==="BODY"?window:t,this._config=this._getConfig(e),this._selector=`${this._config.target} ${u},${this._config.target} ${C},${this._config.target} ${Y}`,this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,o(this._scrollElement).on(R,s=>this._process(s)),this.refresh(),this._process()}static get VERSION(){return v}static get Default(){return A}refresh(){const t=this._scrollElement===this._scrollElement.window?M:S,e=this._config.method==="auto"?t:this._config.method,s=e===S?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(r=>{let a;const d=_.getSelectorFromElement(r);if(d&&(a=document.querySelector(d)),a){const m=a.getBoundingClientRect();if(m.width||m.height)return[o(a)[e]().top+s,d]}return null}).filter(Boolean).sort((r,a)=>r[0]-a[0]).forEach(r=>{this._offsets.push(r[0]),this._targets.push(r[1])})}dispose(){o.removeData(this._element,f),o(this._scrollElement).off(g),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null}_getConfig(t){if(t=E(E({},A),typeof t=="object"&&t?t:{}),typeof t.target!="string"&&_.isElement(t.target)){let e=o(t.target).attr("id");e||(e=_.getUID(c),o(t.target).attr("id",e)),t.target=`#${e}`}return _.typeCheckConfig(c,t,B),t}_getScrollTop(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop}_getScrollHeight(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)}_getOffsetHeight(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height}_process(){const t=this._getScrollTop()+this._config.offset,e=this._getScrollHeight(),s=this._config.offset+e-this._getOffsetHeight();if(this._scrollHeight!==e&&this.refresh(),t>=s){const l=this._targets[this._targets.length-1];this._activeTarget!==l&&this._activate(l);return}if(this._activeTarget&&t<this._offsets[0]&&this._offsets[0]>0){this._activeTarget=null,this._clear();return}for(let l=this._offsets.length;l--;)this._activeTarget!==this._targets[l]&&t>=this._offsets[l]&&(typeof this._offsets[l+1]=="undefined"||t<this._offsets[l+1])&&this._activate(this._targets[l])}_activate(t){this._activeTarget=t,this._clear();const e=this._selector.split(",").map(l=>`${l}[data-target="${t}"],${l}[href="${t}"]`),s=o([].slice.call(document.querySelectorAll(e.join(","))));s.hasClass(L)?(s.closest(j).find(q).addClass(n),s.addClass(n)):(s.addClass(n),s.parents(O).prev(`${u}, ${C}`).addClass(n),s.parents(O).prev(P).children(u).addClass(n)),o(this._scrollElement).trigger(w,{relatedTarget:t})}_clear(){[].slice.call(document.querySelectorAll(this._selector)).filter(t=>t.classList.contains(n)).forEach(t=>t.classList.remove(n))}static _jQueryInterface(t){return this.each(function(){let e=o(this).data(f);const s=typeof t=="object"&&t;if(e||(e=new h(this,s),o(this).data(f,e)),typeof t=="string"){if(typeof e[t]=="undefined")throw new TypeError(`No method named "${t}"`);e[t]()}})}}o(window).on(H,()=>{const i=[].slice.call(document.querySelectorAll(V)),t=i.length;for(let e=t;e--;){const s=o(i[e]);h._jQueryInterface.call(s,s.data())}});o.fn[c]=h._jQueryInterface;o.fn[c].Constructor=h;o.fn[c].noConflict=()=>(o.fn[c]=I,h._jQueryInterface);export{h as default};
//# sourceMappingURL=scrollspy--h1QgKEA.js.map
