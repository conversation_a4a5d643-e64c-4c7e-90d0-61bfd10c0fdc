@import "sass:map";

.cart-products {
  display: flex;
  padding-bottom: map.get($spacers, 3);

  &:not(:first-child) {
    padding-top: map.get($spacers, 3);
    border-top: 1px solid $border-color;
  }

  &__thumb {
    @include custom-col(rem-calc(80px));
    padding-right: map.get($spacers, 2);
  }

  &__desc {
    flex: 1 1;
    padding-right: map.get($spacers, 2);
  }

  &__remove {
    flex: 0 0;
  }

}
