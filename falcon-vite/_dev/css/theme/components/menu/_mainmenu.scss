@use "sass:map";

.main-menu {
  $self: &;

  &__item-header {
    display: flex;
    align-items: center;
    @include media-breakpoint-up(md) {
      display: block;
    }
  }

  &__dropdown {
    margin: -#{$modal-inner-padding} -#{$modal-inner-padding} map.get($spacers, 3);
    border-bottom: 1px solid $border-color;
    @include media-breakpoint-up(md) {
      position: relative;
      display: flex;
      align-items: center;
      margin: 0;
      border: 0;
    }
  }

  &__list {
    @include media-breakpoint-up(md) {
      margin: 0 0 map.get($spacers, 2);

      &--1 {
        display: flex;
        flex-wrap: wrap;
      }
    }
  }

  &__item-link {
    display: block;
    padding: $modal-inner-padding;

    @include media-breakpoint-up(md) {
      display: inline-block;
      padding: 0;
    }

    &--top {
      @include font-size($font-size-lg);
      font-weight: 700;
      color: $gray-900;

      @include hover-focus() {
        text-decoration: none;
      }

      @include media-breakpoint-up(md) {
        padding: map.get($spacers, 3) map.get($spacers, 4);
      }
    }

    &--sub {
      color: $gray-900;
      @include media-breakpoint-up(md) {
        margin: 0 0 map.get($spacers, 1);
      }
    }

    &--1 {
      font-weight: 700;
    }

    &--2 {
      padding-left: #{$modal-inner-padding + rem-calc(map.get($spacers, 2))};
      @include media-breakpoint-up(md) {
        padding: 0;
      }
    }

    &--3 {
      padding-left: #{$modal-inner-padding + rem-calc(map.get($spacers, 3))};
      @include media-breakpoint-up(md) {
        padding-left: map.get($spacers, 2);
      }
    }

    &--4 {
      padding-left: #{$modal-inner-padding + rem-calc(map.get($spacers, 4))};
      @include media-breakpoint-up(md) {
        padding-left: map.get($spacers, 2);
      }
    }

  }

  &__item {
    border-top: 1px solid $border-color;

    &--top {
      &:first-child {
        border: 0;
      }
    }

    @include media-breakpoint-up(md) {
      border: 0;
    }

    &--active {
      @include media-breakpoint-up(md) {
        #{$self} {
          &__sub {
            visibility: visible;
            opacity: 1;
            transform: translateY(0);
          }
        }
      }
    }

    &--1 {
      @include media-breakpoint-up(md) {
        @include custom-col-padded(25%);
      }
    }
  }

  &__sub {
    background: $gray-100;
    @include media-breakpoint-up(md) {
      position: absolute;
      top: 100%;
      right: 0;
      left: 0;
      z-index: 100;
      padding: map.get($spacers, 4) map.get($spacers, 4) map.get($spacers, 3);
      visibility: hidden;
      background: $white;
      box-shadow: $box-shadow;
      opacity: 0;
      transition: 0.2s ease-in;
      transform: translateY(5px);
    }
  }
}
