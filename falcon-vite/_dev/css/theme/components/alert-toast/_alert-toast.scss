@use "sass:map";

.alert-toast {
  $self: &;

  @include font-size($font-size-sm);
  margin-bottom: map.get($spacers, 3);
  opacity: 0;
  transform: translateX(100%);
  box-shadow: $box-shadow;
  border-radius: $border-radius;
  transition: 0.4s ease-in-out;

  &__content {
    padding: map.get($spacers, 2) map.get($spacers, 3);
  }

  &.show {
    opacity: 1;
    transform: translateX(0);
  }

  &--info {
    color: color-yiq($primary);
    background: $primary;
  }

  &--danger {
    color: color-yiq($danger);
    background: $danger;
  }

  &--warning {
    color: color-yiq($warning);
    background: $warning;
  }

  &--success {
    color: color-yiq($success);
    background: $success;
  }
}

