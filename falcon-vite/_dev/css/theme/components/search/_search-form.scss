@use "sass:map";

.search-form {
  position: relative;
  max-width: rem-calc(620px);
  margin: 0 auto;

  &__form-group {
    position: relative;
    @include media-breakpoint-up(md) {
      .search-result-open & {
        z-index: 101;
      }
    }
  }

  &__input {
    height: $search-input-height;
    padding: 0 rem-calc(70px) 0 rem-calc(30px);
    line-height: $search-input-height;
    color: $gray-900;
    border-width: 1px;
    border-radius: 99em;

    &:focus {
      background: $gray-100;
      box-shadow: none;
    }

  }

  &__btn {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    padding: 0 map.get($spacers, 3);
    line-height: 1;
    color: $primary;
  }
}

.search-modal {
  .modal-body {
    padding-top: rem-calc(40px);
  }

  .close {
    position: absolute;
    top: 0;
    right: 0;
    padding: rem-calc(7px) rem-calc(10px);
  }
}
