@use "sass:math";
@import "sass:map";

.js-search-result {
  @include media-breakpoint-up(md) {
    position: absolute;
    top: 100%;
    right: 0;
    left: 0;
    z-index: 99;
    padding: #{math.div($search-input-height, 2) + #{map.get($spacers, 2)}} map.get($spacers, 3) map.get($spacers, 3);
    margin-top: -#{math.div($search-input-height, 2)};
    background: $white;
    border: 1px solid $border-color;
    border-radius: 0 0 $border-radius $border-radius;
  }
}

.search-result {
  @include media-breakpoint-up(md) {
    padding: map.get($spacers, 3);
  }

  &__products {
    margin: map.get($spacers, 3) -#{map.get($spacers, 2)} 0;

    .products-list__block {
      padding: 0 map.get($spacers, 2);
      margin: 0 0 map.get($spacers, 3);
      @include make-col(6);
      @include media-breakpoint-up(md) {
        @include make-col(4);
      }
    }
  }

  &__bottom {
    /* stylelint-disable */
    position: sticky;
    /* stylelint-enable */
    right: 0;
    bottom: -#{$modal-inner-padding};
    left: 0;
    z-index: 1;
    padding: map.get($spacers, 2) map.get($spacers, 3);
    margin: 0 -#{map.get($spacers, 2)};
    background: #fff;
    @include media-breakpoint-up(md) {
      padding: 0;
      margin: 0;
    }
  }
}
