@use "sass:map";

.homeslider {

  &__img {
    background: $gray-100;
  }

  &__list {
    margin: 0;
  }

  &__slider {
    position: relative;
  }

  &__caption {
    position: absolute;
    top: 50%;
    left: 80px;
    transform: translateY(-50%);
  }

  &__desc {
    * {
      color: $gray-800;
    }
  }


  &__arrow {
    top: 0;
    bottom: 0;
    padding: 0 map.get($spacers, 3);
    line-height: 1;

    > * {
      font-size: rem-calc(40px);
    }

    &--prev {
      left: 0;
    }

    &--next {
      right: 0;
    }
  }
}
