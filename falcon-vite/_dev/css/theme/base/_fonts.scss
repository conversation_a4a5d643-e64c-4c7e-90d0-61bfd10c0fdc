$material-design-icons-font-directory-path: '~material-design-icons-iconfont/dist/fonts/';
@import '~material-design-icons-iconfont/src/material-design-icons';

@import "../../fonts/icomoon/style";

%abstract-icon {

  width: 1em;
  height: 1em;

  /* use !important to prevent issues with browser extensions that change fonts */

  /* stylelint-disable */
  font-family: $icomoon-font-family !important;
  /* stylelint-enable */
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  line-height: 1;
  text-transform: none;
  speak: none;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


.material-icons {
  display: inline-block;
  /* stylelint-disable */
  font-family: "Material Icons";
  /* stylelint-enable */
  font-size: 24px;  /* Preferred icon size */
  font-style: normal;
  font-weight: 400;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;

  /* Support for all WebKit browsers. */
  -webkit-font-smoothing: antialiased;

  /* Support for Safari and Chrome. */
  text-rendering: optimizeLegibility;

  /* Support for Firefox. */
  -moz-osx-font-smoothing: grayscale;

  /* Support for IE. */
  font-feature-settings: "liga";
}

/* noto-sans-regular */
@font-face {
  font-family: "Noto Sans";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("./fonts/noto-sans-v39-latin-regular.woff2") format("woff2"), url("./fonts/noto-sans-v39-latin-regular.woff") format("woff");
}

/* noto-sans-500 */
@font-face {
  font-family: "Noto Sans";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("./fonts/noto-sans-v39-latin-500.woff2") format("woff2"), url("./fonts/noto-sans-v39-latin-500.woff") format("woff");
}

/* noto-sans-italic */
@font-face {
  font-family: "Noto Sans";
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url("./fonts/noto-sans-v39-latin-italic.woff2") format("woff2"), url("./fonts/noto-sans-v39-latin-italic.woff") format("woff");
}

/* noto-sans-700 */
@font-face {
  font-family: "Noto Sans";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("./fonts/noto-sans-v39-latin-700.woff2") format("woff2"), url("./fonts/noto-sans-v39-latin-700.woff") format("woff");
}

