@import "sass:map";

.product-thumbs {
  margin-right: -#{map.get($spacers, 1)};
  margin-left: -#{map.get($spacers, 1)};

  &__elem {
    padding: 0 map.get($spacers, 1);
    cursor: pointer;
    opacity: .3;
    transition: .3s ease opacity;
    @include make-col(4);

    &:focus,
    &:hover {
      opacity: 0.6;
    }

    &.swiper-slide-thumb-active {
      opacity: 1;
    }

    @include media-breakpoint-up(sm) {
      @include make-col(3);
    }
  }
}
