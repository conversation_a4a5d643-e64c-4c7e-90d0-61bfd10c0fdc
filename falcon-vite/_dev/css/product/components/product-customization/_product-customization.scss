@use "sass:map";

.product-customization {
  margin: map.get($spacers, 5) 0;

  .product-customization-item {
    margin: map.get($spacers, 3) 0;
  }

  .product-message {
    width: 100%;
    height: 3.125rem;
    padding: 0.625rem;
    resize: none;
    background: $gray-100;
    border: none;

    &:focus {
      background-color: #fff;
      outline: 0.1875rem solid $primary;
    }
  }

  .file-input {
    position: absolute;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 2.625rem;
    overflow: hidden;
    cursor: pointer;
    opacity: 0;
  }

  .customization-message{
    margin-top: 20px;
  }

  .custom-file {
    position: relative;
    display: block;
    width: 100%;
    height: 2.625rem;
    margin-top: map.get($spacers, 3);
    line-height: 2.625rem;
    color: $gray-600;
    text-indent: 0.625rem;
    background: $gray-100;

    button {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 0;
    }
  }

  small {
    color: $gray-600;
  }
}
