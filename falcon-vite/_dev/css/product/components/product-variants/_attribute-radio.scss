@import "sass:map";

.attribute-radio {
  $self: &;

  &__label {
    position: relative;
    margin: 0;
  }

  &__input {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    opacity: 0;

    &:checked ~ {
      #{$self} {
        &__text {
          color: $white;
          background-color: $primary;
          border-color: $primary;
        }
      }
    }
  }

  &__text {
    display: block;
    min-width: rem-calc(35px);
    padding: map.get($spacers, 1) map.get($spacers, 2);
    font-size: $font-size-sm;
    font-weight: 700;
    text-align: center;
    border: 1px solid $border-color;
    border-radius: $border-radius;
  }
}
