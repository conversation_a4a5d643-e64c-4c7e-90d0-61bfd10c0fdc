@import "variables" as *;

@font-face {
  font-family: '#{$icomoon-font-family}';
  src:
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.woff2?yhufbt') format('woff2'),
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.woff?yhufbt') format('woff'),
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.ttf?yhufbt') format('truetype'),
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.svg?yhufbt##{$icomoon-font-family}') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

.icon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: '#{$icomoon-font-family}' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-ps-socialfollow-discord {
  &:before {
    content: $icon-ps-socialfollow-discord;
  }
}
.icon-tiktok {
  &:before {
    content: $icon-tiktok;
  }
}
.icon-facebook {
  &:before {
    content: $icon-facebook;
  }
}
.icon-gplus {
  &:before {
    content: $icon-gplus;
  }
}
.icon-instagram {
  &:before {
    content: $icon-instagram;
  }
}
.icon-linkedin {
  &:before {
    content: $icon-linkedin;
  }
}
.icon-pinterest {
  &:before {
    content: $icon-pinterest;
  }
}
.icon-rss {
  &:before {
    content: $icon-rss;
  }
}
.icon-twitter {
  &:before {
    content: $icon-twitter;
  }
}
.icon-vimeo {
  &:before {
    content: $icon-vimeo;
  }
}
.icon-youtube {
  &:before {
    content: $icon-youtube;
  }
}

