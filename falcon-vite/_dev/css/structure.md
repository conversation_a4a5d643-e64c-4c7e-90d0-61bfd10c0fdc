Main Entry Files:
    `checkout.scss` - Uses `@import`
    `listing.scss` - Uses `@import`
    `product.scss` - Uses `@import`

Index Files with @import:
    `listing/_index.scss`
    `listing/components/_index.scss`
    `listing/components/filters/_index.scss`
    `product/_index.scss`
    `product/components/_index.scss`
    `checkout/_index.scss`
    `checkout/components/_index.scss`
    `checkout/components/checkout/_index.scss`

Component Index Files:
    `abstracts/variables/custom/_index.scss`
    `abstracts/variables/bootstrap/_index.scss`
    `abstracts/variables/_index.scss`
    `abstracts/mixins/_index.scss`
    `abstracts/functions/_index.scss`

Theme Component Files:
    `theme/base/_index.scss`
    `theme/utility/_index.scss`
    `theme/layout/_index.scss`
    `theme/override/bootstrap/_index.scss`
    `theme/override/swiper/_index.scss`
All component index files in `theme/components/`

Vendor Files:
    `theme/vendors/_bootstrap.scss` - Large file with many Bootstrap imports
    `theme/vendors/_swiper.scss`

Font Files:
    `fonts/icomoon/style.scss`
    `abstracts/variables/custom/_icomoon.scss`

Dynamic Import Files:
    `dynamic/tooltip/_index.scss`
    `dynamic/popover/_index.scss`