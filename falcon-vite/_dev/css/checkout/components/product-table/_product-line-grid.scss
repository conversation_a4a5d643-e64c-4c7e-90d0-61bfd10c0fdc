@import "sass:map";

.product-line-grid {
  align-items: center;

  &:not(:last-child) {
    padding-bottom: map.get($spacers, 3);
    margin-bottom: map.get($spacers, 3);
    border-bottom: 1px solid $border-color;
  }

  &__row {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin: 0 -#{map.get($spacers, 2)};
    @include media-breakpoint-up(md) {
      flex-wrap: nowrap;
    }
  }

  &__block {
    padding: 0 rem-calc(10px);

    &--image {
      align-self: flex-start;
      @include custom-col(80px);
      order: -3;
      @include media-breakpoint-up(md) {
        align-self: center;
        @include custom-col(100px);
      }
    }

    &--prod {
      order: -2;
      @include custom-col(calc(100% - #{rem-calc(80px)} - #{rem-calc(50px)}));
      @include media-breakpoint-up(md) {
        flex: 1 1 auto;
        max-width: inherit;
      }
    }

    &--qty {
      @include make-col(6);
      margin: rem-calc(15px) 0 0;
      @include media-breakpoint-up(md) {
        margin: 0;
        @include custom-col(rem-calc(130px));
      }
    }

    &--total {
      @include make-col(6);
      margin: rem-calc(15px) 0 0;
      text-align: right;
      @include media-breakpoint-up(md) {
        margin: 0;
        text-align: center;
        @include custom-col(rem-calc(120px));
      }
      @include media-breakpoint-up(xl) {
        @include custom-col(rem-calc(150px));
      }
    }

    &--delete {
      @include custom-col(rem-calc(50px));
      text-align: center;
      @include media-breakpoint-down(sm) {
        align-self: flex-start;
        order: -1;
        padding-top: rem-calc(5px);
      }
    }
  }
}
