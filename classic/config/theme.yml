name: classic
display_name: Classic
version: 2.2.0
author:
  name: "PrestaShop SA and Contributors"
  email: "<EMAIL>"
  url: "https://www.prestashop-project.org"

meta:
  compatibility:
    from: 8.2.1
    to: ~

  available_layouts:
    layout-full-width:
      name: Full Width
      description: No side columns, ideal for distraction-free pages such as product pages.
    layout-both-columns:
      name: Three Columns
      description: One large central column and 2 side columns.
    layout-left-column:
      name: Two Columns, small left column
      description: Two columns with a small left column
    layout-right-column:
      name: Two Columns, small right column
      description: Two columns with a small right column

assets:
# If you're using this theme as child and you want to load
# the parent theme assets, uncomment this line.
#  use_parent_assets: true

# The following lines are showing how to load assets in your page
# Uncomment and change value to start loading css or js files
#  css:
#  all:
#    - id: custom-lib-style
#    path: assets/css/custom-lib.css
#  product:
#    - id: product-style
#    path: assets/css/product.css
#    media: all
#    priority: 200
#  js:
#  cart:
#    - id: cat-extra-lib
#    path: assets/js/cart-lib.js


global_settings:
  configuration:
    PS_IMAGE_QUALITY: png
  modules:
    to_enable:
      - ps_linklist
  hooks:
    modules_to_hook:
      displayAfterBodyOpeningTag:
        - blockreassurance
      displayNavFullWidth:
        - blockreassurance
      displayAdminCustomers:
        - blockwishlist
      displayCustomerAccount:
        - blockwishlist
        - psgdpr
      displayMyAccountBlock:
        - blockwishlist
      displayNav1:
        - ps_contactinfo
      displayNav2:
        - ps_languageselector
        - ps_currencyselector
        - ps_customersignin
        - ps_shoppingcart
      displayTop:
        - ps_mainmenu
        - ps_searchbar
      displayHome:
        - ps_imageslider
        - ps_featuredproducts
        - ps_banner
        - ps_customtext
        - ps_specials
        - ps_newproducts
        - ps_bestsellers
      displayFooterBefore:
        - ps_emailsubscription
        - ps_socialfollow
        - blockreassurance
      displayFooter:
        - ps_linklist
        - ps_customeraccountlinks
        - ps_contactinfo
        - blockwishlist
      displayFooterAfter:
        - blockreassurance
      displayFooterProduct:
        - productcomments
      displayLeftColumn:
        - ps_categorytree
        - ps_facetedsearch
      displayContactLeftColumn:
        - ps_contactinfo
      displayContactRightColumn:
        - ps_contactinfo
      displayContactContent:
        - contactform
      displaySearch:
        - ps_searchbar
      displayProductAdditionalInfo:
        - ps_sharebuttons
        - productcomments
      displayProductListReviews:
        - productcomments
      displayReassurance:
        - blockreassurance
      displayOrderConfirmation2:
        - ps_featuredproducts
      displayProductActions:
        - blockwishlist

  image_types:
    cart_default:
      width: 125
      height: 125
      scope: [ products ]
    small_default:
      width: 98
      height: 98
      scope: [ products, categories, manufacturers, suppliers ]
    medium_default:
      width: 452
      height: 452
      scope: [ products, manufacturers, suppliers ]
    home_default:
      width: 250
      height: 250
      scope: [ products ]
    large_default:
      width: 800
      height: 800
      scope: [ products, manufacturers, suppliers ]
    category_default:
      width: 141
      height: 180
      scope: [ categories ]
    stores_default:
      width: 170
      height: 115
      scope: [ stores ]
  new_password_policy_feature: true

theme_settings:
  default_layout: layout-full-width
  layouts:
    category: layout-left-column
    best-sales: layout-left-column
    new-products: layout-left-column
    prices-drop: layout-left-column
    contact: layout-left-column
